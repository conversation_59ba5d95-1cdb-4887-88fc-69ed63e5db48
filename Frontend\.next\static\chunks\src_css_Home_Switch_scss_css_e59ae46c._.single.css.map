{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Switch.scss"], "sourcesContent": [".form-check-input {\r\n  width: 72px !important;\r\n  height: 34px !important;\r\n  border-radius: 30px !important;\r\n  position: relative;\r\n  background-color: #fff !important;\r\n  border: none !important;\r\n  cursor: pointer;\r\n  appearance: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.form-check-input::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 30px;\r\n  height: 30px;\r\n  top: 2px;\r\n  left: 2px;\r\n  background-color: #fff;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease-in-out;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.form-check-input:checked {\r\n  background-color: #00adef !important;\r\n}\r\n\r\n.form-check-input:checked::before {\r\n  transform: translateX(38px);\r\n}\r\n\r\n.form-check-input:focus {\r\n  box-shadow: none !important;\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA"}}]}