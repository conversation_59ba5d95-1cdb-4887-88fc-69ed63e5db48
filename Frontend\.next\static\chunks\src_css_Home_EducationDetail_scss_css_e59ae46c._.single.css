/* [project]/src/css/Home/EducationDetail.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

@media (max-width: 991px) {
  .education_detail {
    padding-top: 40px !important;
  }
}

.education_detail_tag {
  text-align: center;
  letter-spacing: -.1px;
  text-transform: uppercase;
  color: #fff;
  background-color: #00adef;
  border: 0;
  border-radius: 10px;
  padding: 6px 20px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

.education_detail_heading h1 {
  color: #fff;
  padding: 30px 0;
  font-size: 2.8rem;
  font-weight: 600;
}

@media (max-width: 1199px) {
  .education_detail_heading h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  .education_detail_heading h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {
  .education_detail_heading h1 {
    font-size: 1.3rem;
  }
}

.education_detail_heading h5 {
  color: #fff;
  padding-top: 30px;
  font-size: 1.25rem;
  font-weight: 600;
}

.education_detail_postimg {
  padding: 5rem 0;
}

@media (max-width: 767px) {
  .education_detail_postimg {
    padding: .625rem 0 2rem;
  }
}

.education_detail_postimg img {
  border-radius: 60px;
}

@media (max-width: 767px) {
  .education_detail_postimg img {
    border-radius: 30px;
  }
}

.education_detail_text h2 {
  overflow-wrap: break-word;
  font-size: 4rem;
}

@media (max-width: 1269px) {
  .education_detail_text h2 {
    font-size: 3rem !important;
  }
}

@media (max-width: 991px) {
  .education_detail_text h2 {
    font-size: 3rem !important;
  }
}

@media (max-width: 767px) {
  .education_detail_text h2 {
    font-size: 2.5rem !important;
  }
}

.education_detail_text p {
  letter-spacing: -.1px;
  color: #fff;
  overflow-wrap: break-word;
  padding-top: 20px;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 36px;
}

@media (max-width: 767px) {
  .education_detail_text p {
    padding-top: 0;
    font-size: 15px;
    line-height: 23px;
  }
}

.education_detail_author {
  padding-top: 5rem;
}

@media (max-width: 767px) {
  .education_detail_author {
    padding-top: 3rem;
  }
}

.education_detail_author_btn {
  color: #00adef;
  letter-spacing: -.1px;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  margin-bottom: 60px;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
}

@media (max-width: 767px) {
  .education_detail_author_btn {
    margin-bottom: 30px;
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.education_detail .recent_post {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #666;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  margin-bottom: 0;
  padding: 30px 0;
}

.education_detail_sidebar {
  position: relative;
}

@media screen and (min-width: 992px) {
  .education_detail_sidebar {
    position: -webkit-sticky;
    position: sticky;
    top: 100px;
  }
}

.education_detail_sidebar .collapse_btn {
  z-index: 99;
  background-color: #00adef;
  border-radius: 15px 0 0 15px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  display: none;
  position: fixed;
  top: 90px;
  right: 0;
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar .collapse_btn {
    display: flex;
  }
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar .collapse_btn {
    top: 70px;
  }
}

@media screen and (max-width: 1599px) {
  .education_detail_sidebar .btn-style {
    font-size: 1rem;
  }
}

.education_detail_sidebar_collapse {
  position: relative;
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_collapse {
    z-index: 98;
    width: 100%;
    margin-top: 0;
    transition: all .2s ease-in-out;
    position: fixed;
    top: 72px;
    right: -100%;
  }
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_collapse {
    top: 56px;
  }
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_collapse .collapse_wrap {
    background-color: #031940;
    border-radius: 20px;
    height: calc(100vh - 36px);
    padding: 70px 20px 20px;
  }

  .education_detail_sidebar_collapse.active {
    right: 0;
  }
}

.education_detail_sidebar_collapse.active .collapse_wrap {
  display: block;
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_top {
    display: flex;
  }

  .education_detail_sidebar_top .btn-style {
    margin-right: 10px;
    width: 50% !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 50%;
    margin-left: 10px;
    margin-top: 0 !important;
  }
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_top {
    flex-direction: column;
    align-items: center;
  }

  .education_detail_sidebar_top .btn-style {
    margin-bottom: 10px;
    margin-right: 0;
    width: 400px !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 400px;
    margin-left: 0;
    margin-top: 10px !important;
  }
}

@media screen and (max-width: 575px) {
  .education_detail_sidebar_top {
    display: block;
  }

  .education_detail_sidebar_top .btn-style {
    width: 100% !important;
  }

  .education_detail_sidebar_top .education_search {
    width: 100%;
  }
}

@media (min-width: 992px) {
  .education_detail_sidebar_profit {
    max-height: calc(100vh - 480px);
    padding-right: 5px;
    overflow-x: clip;
    overflow-y: auto;
  }
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_profit {
    width: 100%;
    max-width: 90%;
    margin: 0 auto;
    display: flex;
    position: relative;
    overflow: hidden;
  }
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit {
    max-width: 240px;
  }
}

.education_detail_sidebar_profit_inner {
  border-bottom: 1px solid rgba(255, 255, 255, .2);
  padding: 10px 0;
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_profit_inner {
    min-width: 300px;
    margin-right: 30px;
  }
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_inner {
    border-bottom: 0;
    min-width: 100%;
    margin-right: 0;
  }
}

.education_detail_sidebar_profit_inner_detail {
  cursor: pointer;
}

.education_detail_sidebar_profit_img {
  width: 130px;
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_img {
    width: 100px;
  }
}

.education_detail_sidebar_profit_img img {
  object-fit: cover;
  border-radius: 20px;
  height: 80px;
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_img img {
    height: 60px;
  }
}

.education_detail_sidebar_profit_text {
  width: calc(100% - 130px);
  padding-left: 5px;
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_text {
    width: calc(100% - 100px);
  }
}

.education_detail_sidebar_profit_text h6 {
  color: #fff;
  padding-bottom: 10px;
  font-size: 1rem;
  font-weight: 600;
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_text h6 {
    padding-bottom: 6px;
  }
}

.education_detail_sidebar_profit_text p {
  text-align: left;
  color: #c5c5d5;
  word-wrap: break-word;
  white-space: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
}

@media screen and (max-width: 767px) {
  .education_detail_sidebar_profit_text p {
    font-size: 14px;
    line-height: 15px;
  }
}

.education_detail_sidebar_profit_progressbar {
  margin-top: 10px;
}

.education_detail_sidebar_profit_progressbar .progress {
  background-color: rgba(255, 255, 255, .2);
  height: 7px;
}

.education_detail_sidebar_profit_progressbar .progress .progress-bar {
  background-color: #00adef;
}

.education_detail_sidebar_article {
  text-align: center;
  margin: 20px 0 0;
}

@media screen and (max-width: 991px) {
  .education_detail_sidebar_article {
    margin: 40px 0 0;
  }
}

.education_detail_sidebar_article_data h6 {
  padding-bottom: 10px;
}

.education_detail .scroll-btn {
  color: #fff;
  cursor: pointer;
  background-color: #00adef;
  border: none;
  border-radius: 10rem;
  justify-content: center;
  align-items: center;
  min-width: 30px;
  min-height: 30px;
  padding: 0;
  font-size: 1.2rem;
  display: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 991px) {
  .education_detail .scroll-btn {
    display: flex;
  }
}

.education_detail .scroll-btn.left {
  left: -10px;
}

.education_detail .scroll-btn.left svg {
  transform: rotate(180deg);
}

.education_detail .scroll-btn.right {
  right: -10px;
}

.education_detail .scroll-btn:hover {
  background-color: #00adef;
}

.education_detail .scroll-btn.disabled, .education_detail .scroll-btn:disabled {
  background-color: #414c60;
}

@media screen and (max-width: 991px) {
  .education_detail .commonSearch {
    max-width: 100%;
  }
}

.CircularProgressbar .CircularProgressbar-trail {
  stroke: rgba(255, 255, 255, .2);
}

.CircularProgressbar .CircularProgressbar-path {
  stroke: #00adef;
}

.CircularProgressbar_text {
  text-align: center;
}

.CircularProgressbar_text h6 {
  fill: #fff;
  padding-bottom: 5px;
  font-size: .875rem;
  font-weight: 600;
}

/*# sourceMappingURL=src_css_Home_EducationDetail_scss_css_e59ae46c._.single.css.map*/