{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/EducationDetail.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.education_detail {\r\n  @media (max-width: 991px) {\r\n    padding-top: 40px !important;\r\n  }\r\n\r\n  &_tag {\r\n    padding: 6px 20px;\r\n    background-color: var.$baseclr;\r\n    border-radius: 10px;\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 26px;\r\n    letter-spacing: -0.10000000149011612px;\r\n    text-transform: uppercase;\r\n    color: var.$white;\r\n    border: 0;\r\n  }\r\n\r\n  &_heading {\r\n    h1 {\r\n      font-size: 2.8rem;\r\n      font-weight: 600;\r\n      color: var.$white;\r\n      padding: 30px 0;\r\n\r\n      @media (max-width: 1199px) {\r\n        font-size: 2.5rem;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1.5rem;\r\n      }\r\n\r\n      @media (max-width: 390px) {\r\n        font-size: 1.30rem;\r\n      }\r\n    }\r\n\r\n    h5 {\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      color: var.$white;\r\n      padding-top: 30px;\r\n    }\r\n  }\r\n\r\n  &_postimg {\r\n    padding: 5rem 0;\r\n\r\n    @media (max-width: 767px) {\r\n      padding: 0.625rem 0 2rem;\r\n    }\r\n\r\n    img {\r\n      border-radius: 60px;\r\n\r\n      @media (max-width: 767px) {\r\n        border-radius: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_text {\r\n    h2 {\r\n      font-size: 4rem;\r\n      overflow-wrap: break-word;\r\n\r\n      @media (max-width: 1269px) {\r\n        font-size: 3rem !important;\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 3rem !important;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 2.5rem !important;\r\n      }\r\n    }\r\n\r\n    p {\r\n      font-size: 1.5rem;\r\n      font-weight: 400;\r\n      line-height: 36px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      color: var.$white;\r\n      padding-top: 20px;\r\n      overflow-wrap: break-word;\r\n\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 15px;\r\n        line-height: 23px;\r\n        padding-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_author {\r\n    padding-top: 5rem;\r\n\r\n    @media (max-width: 767px) {\r\n      padding-top: 3rem;\r\n    }\r\n\r\n    &_btn {\r\n      background-color: transparent;\r\n      border: 0;\r\n      color: var.$baseclr;\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      line-height: 24.5px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      margin-bottom: 60px;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1rem;\r\n        line-height: 1.25rem;\r\n        margin-bottom: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .recent_post {\r\n    background-color: transparent;\r\n    border-radius: 0;\r\n    border: 0;\r\n    margin-bottom: 0;\r\n    padding: 30px 0;\r\n    border-top: 1px solid var.$borderclr;\r\n    border-bottom: 1px solid var.$borderclr;\r\n  }\r\n\r\n  &_sidebar {\r\n    position: relative;\r\n\r\n    @media screen and (min-width: 992px) {\r\n      position: sticky;\r\n      top: 100px;\r\n    }\r\n\r\n    .collapse_btn {\r\n      position: fixed;\r\n      background-color: var.$baseclr;\r\n      width: 40px;\r\n      height: 40px;\r\n      align-items: center;\r\n      justify-content: center;\r\n      right: 0;\r\n      border-radius: 15px 0px 0px 15px;\r\n      top: 90px;\r\n      z-index: 99;\r\n      display: none;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        display: flex;\r\n      }\r\n\r\n      @media screen and (max-width: 767px) {\r\n        top: 70px;\r\n      }\r\n    }\r\n\r\n    .btn-style {\r\n      @media screen and (max-width: 1599px) {\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n\r\n    &_collapse {\r\n      position: relative;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        margin-top: 0;\r\n        margin-top: 0;\r\n        position: fixed;\r\n        width: 100%;\r\n        right: -100%;\r\n        transition: all ease-in-out 0.2s;\r\n        z-index: 98;\r\n        top: 72px;\r\n      }\r\n\r\n      @media screen and (max-width: 767px) {\r\n        top: 56px;\r\n      }\r\n\r\n      .collapse_wrap {\r\n        @media screen and (max-width: 991px) {\r\n          background-color: var.$clr031940;\r\n          border-radius: 20px;\r\n          padding: 70px 20px 20px;\r\n          height: calc(100vh - 36px);\r\n        }\r\n      }\r\n\r\n      &.active {\r\n        @media screen and (max-width: 991px) {\r\n          right: 0;\r\n        }\r\n\r\n        .collapse_wrap {\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_top {\r\n      @media screen and (max-width: 991px) {\r\n        display: flex;\r\n\r\n        .btn-style {\r\n          width: 50% !important;\r\n          margin-right: 10px;\r\n        }\r\n\r\n        .education_search {\r\n          width: 50%;\r\n          margin-top: 0 !important;\r\n          margin-left: 10px;\r\n        }\r\n      }\r\n\r\n      @media screen and (max-width: 767px) {\r\n        flex-direction: column;\r\n        align-items: center;\r\n\r\n        .btn-style {\r\n          width: 400px !important;\r\n          margin-right: 0;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .education_search {\r\n          width: 400px;\r\n          margin-top: 10px !important;\r\n          margin-left: 0;\r\n        }\r\n      }\r\n\r\n      @media screen and (max-width: 575px) {\r\n        display: block;\r\n\r\n        .btn-style {\r\n          width: 100% !important;\r\n        }\r\n\r\n        .education_search {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_profit {\r\n      @media (min-width: 992px) {\r\n        max-height: calc(100vh - 480px);\r\n        overflow-y: auto;\r\n        overflow-x: clip;\r\n        padding-right: 5px;\r\n      }\r\n\r\n      // @media  (min-width: 768px) and (max-width: 991px) {\r\n      //     display: flex;\r\n      //     flex-wrap: nowrap;\r\n      //     white-space: nowrap;\r\n      //     overflow-x: auto;\r\n      // }\r\n\r\n      @media screen and (max-width: 991px) {\r\n        display: flex;\r\n        overflow: hidden;\r\n        position: relative;\r\n        max-width: 90%;\r\n        width: 100%;\r\n        margin: 0 auto;\r\n      }\r\n\r\n      @media screen and (max-width: 767px) {\r\n        max-width: 240px;\r\n      }\r\n\r\n      &_inner {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n        padding: 10px 0;\r\n\r\n        @media screen and (max-width: 991px) {\r\n          min-width: 300px;\r\n          margin-right: 30px;\r\n        }\r\n\r\n        @media screen and (max-width: 767px) {\r\n          min-width: 100%;\r\n          margin-right: 0px;\r\n          border-bottom: 0;\r\n        }\r\n\r\n        &_detail {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      &_img {\r\n        width: 130px;\r\n\r\n        @media screen and (max-width: 767px) {\r\n          width: 100px;\r\n        }\r\n\r\n        img {\r\n          border-radius: 20px;\r\n          height: 80px;\r\n          object-fit: cover;\r\n\r\n          @media screen and (max-width: 767px) {\r\n            height: 60px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &_text {\r\n        width: calc(100% - 130px);\r\n        padding-left: 5px;\r\n\r\n        @media screen and (max-width: 767px) {\r\n          width: calc(100% - 100px);\r\n        }\r\n\r\n        h6 {\r\n          font-size: 1rem;\r\n          font-weight: 600;\r\n          color: var.$white;\r\n          padding-bottom: 10px;\r\n\r\n          @media screen and (max-width: 767px) {\r\n            padding-bottom: 6px;\r\n          }\r\n        }\r\n\r\n        p {\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          line-height: 20px;\r\n          text-align: left;\r\n          color: var.$textclr;\r\n          word-wrap: break-word;\r\n          white-space: normal;\r\n\r\n          @media screen and (max-width: 767px) {\r\n            font-size: 14px;\r\n            ;\r\n            line-height: 15px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &_progressbar {\r\n        margin-top: 10px;\r\n\r\n        .progress {\r\n          height: 7px;\r\n          background-color: rgba(255, 255, 255, 0.2);\r\n\r\n          .progress-bar {\r\n            background-color: var.$baseclr;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &_article {\r\n      margin: 20px 0 0;\r\n      text-align: center;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        margin: 40px 0 0;\r\n      }\r\n\r\n      &_data {\r\n        h6 {\r\n          padding-bottom: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .scroll-btn {\r\n    background-color: var.$baseclr;\r\n    color: white;\r\n    border: none;\r\n    padding: 0;\r\n    cursor: pointer;\r\n    font-size: 1.2rem;\r\n    min-width: 30px;\r\n    min-height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 10rem;\r\n    position: absolute;\r\n    display: none;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n\r\n    @media (max-width: 991px) {\r\n      display: flex;\r\n    }\r\n\r\n    &.left {\r\n      left: -10px;\r\n\r\n      svg {\r\n        transform: rotate(180deg);\r\n      }\r\n    }\r\n\r\n    &.right {\r\n      right: -10px;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: var.$baseclr;\r\n    }\r\n\r\n    &.disabled,\r\n    &:disabled {\r\n      background-color: #414c60;\r\n    }\r\n  }\r\n\r\n  .commonSearch {\r\n    @media screen and (max-width: 991px) {\r\n      max-width: 100%;\r\n\r\n      // .form-control {\r\n      //     border-radius: 10rem;\r\n      // }\r\n    }\r\n  }\r\n}\r\n\r\n.CircularProgressbar {\r\n  .CircularProgressbar-trail {\r\n    stroke: rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  .CircularProgressbar-path {\r\n    stroke: var.$baseclr;\r\n  }\r\n}\r\n\r\n.CircularProgressbar_text {\r\n  text-align: center;\r\n\r\n  h6 {\r\n    font-size: 0.875rem;\r\n    font-weight: 600;\r\n    fill: var.$white;\r\n    padding-bottom: 5px;\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AAAE;EADF;;;;;AAKE;;;;;;;;;;;;;;AAeE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAcE;EAdF;;;;;AAmBA;;;;;;;AAQF;;;;AAGE;EAHF;;;;;AAOE;;;;AAGE;EAHF;;;;;AAUA;;;;;AAIE;EAJF;;;;;AAQE;EARF;;;;;AAYE;EAZF;;;;;AAiBA;;;;;;;;;;AAUE;EAVF;;;;;;;AAkBF;;;;AAGE;EAHF;;;;;AAOE;;;;;;;;;;;AAUE;EAVF;;;;;;;AAkBF;;;;;;;;;;AAUA;;;;AAGE;EAHF;;;;;;;AAQE;;;;;;;;;;;;;;AAaE;EAbF;;;;;AAiBE;EAjBF;;;;;AAuBE;EADF;;;;;AAMA;;;;AAGE;EAHF;;;;;;;;;;;AAUI;EAIF;;;;;AAdF;EAmBI;;;;;;;EAIE;;;;;AAIJ;;;;AAKE;EACE;;;;EAMJ;;;;;EAGE;;;;;;;AAME;EAEA;;;;;EAZN;;;;;;EAqBM;;;;;;;AAMA;EAEA;;;;EAIJ;;;;EAGE;;;;;AAIA;EAAA;;;;;;;;AASA;EAEA;;;;;;;;;;AAaA;EAGA;;;;;AAGF;;;;;AAKE;EACA;;;;;;AAFF;EAMI;;;;;;;AANJ;;;;AAeE;;;;AACE;EAIJ;;;;;AAGE;;;;;;AAIA;EAGE;;;;;AAHF;;;;;AAWF;EAEE;;;;;AAFF;;;;;;;AASI;EAGA;;;;;AAJF;;;;;;;;;;AAgBE;EAEA;;;;;;AAPF;;;;AAiBF;;;;;AAGE;;;;AAIE;;;;;AAON;EAEE;;;;;AAFF;;;;AASI;;;;;;;;;;;;;;;;;;AAoBJ;EAGA;;;;;AAEA;;;;AACE;;;;AAMA;;;;AACE;;;;AAQJ;;;;AAIA;EAEE;;;;;AAKF;;;;AAWF;;;;AAIA;;;;AACE"}}]}