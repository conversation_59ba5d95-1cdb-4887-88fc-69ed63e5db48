{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonButton.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.btn-style,\r\n.btn-primary {\r\n  min-height: 66px;\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  text-align: center;\r\n  border-radius: 10rem;\r\n  padding: 0.5rem 1.5rem;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  background-color: var.$baseclr;\r\n  border: 0;\r\n  text-transform: capitalize;\r\n  transition: all ease-in-out 0.3s;\r\n  min-width: 150px;\r\n  color: var.$white;\r\n  \r\n  span{\r\n    line-height: 1;\r\n  }\r\n\r\n  @media (max-width: 1599px) {\r\n    min-height: 66px;\r\n  }\r\n\r\n  @media (max-width: 1199px) {\r\n    min-height: 56px;\r\n    font-size: 1.125rem;\r\n    font-weight: 500;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    min-height: 46px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: var.$baseclrhover;\r\n    color: var.$white;\r\n  }\r\n\r\n  &.transparent {\r\n    background-color: transparent;\r\n    border: none;\r\n  }\r\n\r\n  &.white-btn {\r\n    background: var.$white;\r\n    color: var.$black;\r\n\r\n    &:hover {\r\n      background: var.$baseclr;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.yellow-btn {\r\n    background-color: var.$yellow;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$yellowBtnHover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.gray-btn {\r\n    background-color: var.$grayBtn !important;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$grayBtnHover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.gradient-btn {\r\n    background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.green-btn {\r\n    background-color: var.$green;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$greenbtnhover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.red-btn {\r\n    background-color: var.$redlightclr;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$redbghover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.border-btn {\r\n    background: transparent;\r\n    color: var.$white;\r\n    border: 1px solid var.$baseclr;\r\n\r\n    &:hover {\r\n      background: var.$baseclr;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  .onlyIcon {\r\n    margin-right: 15px;\r\n    display: inline-flex;\r\n  }\r\n\r\n  &:disabled,\r\n  &.disabled {\r\n    background: var.$textclr;\r\n    color: var.$white;\r\n    cursor: not-allowed;\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n:disabled,\r\n.disabled {\r\n  background-color: #414c60;\r\n  color: var.$white;\r\n  cursor: not-allowed;\r\n  opacity: 1;\r\n}\r\n\r\n.white20 {\r\n  background-color: #ffffff1f;\r\n  width: 100%;\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;;;;;;;;;;;;AAkBE;;;;AAIA;EAtBF;;;;;AA0BE;EA1BF;;;;;;;AAgCE;EAhCF;;;;;;AAqCE;;;;;AAKA;;;;;AAKA;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;;AAKE;;;;;AAMF;;;;;AAKA;;;;;;;AASF;;;;;;;AAQA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Switch.scss"], "sourcesContent": [".form-check-input {\r\n  width: 72px !important;\r\n  height: 34px !important;\r\n  border-radius: 30px !important;\r\n  position: relative;\r\n  background-color: #fff !important;\r\n  border: none !important;\r\n  cursor: pointer;\r\n  appearance: none;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.form-check-input::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 30px;\r\n  height: 30px;\r\n  top: 2px;\r\n  left: 2px;\r\n  background-color: #fff;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease-in-out;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.form-check-input:checked {\r\n  background-color: #00adef !important;\r\n}\r\n\r\n.form-check-input:checked::before {\r\n  transform: translateX(38px);\r\n}\r\n\r\n.form-check-input:focus {\r\n  box-shadow: none !important;\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonHeading.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.common_heading {\r\n\r\n  h2,\r\n  h1 {\r\n    font-size: 5rem;\r\n    line-height: normal;\r\n    color: var.$white;\r\n    font-weight: 800;\r\n    font-family: \"<PERSON><PERSON>\", sans-serif;\r\n\r\n    @media (max-width: 1269px) {\r\n      font-size: 3rem !important;\r\n    }\r\n\r\n    @media (max-width: 991px) {\r\n      font-size: 3rem !important;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      font-size: 2.5rem !important;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AACE;;;;;;;;AAQE;EARF;;;;;AAYE;EAZF;;;;;AAgBE;EAhBF", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/FaqCard.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.faq_card {\r\n  margin-bottom: 6rem;\r\n\r\n  .common_heading {\r\n    h2 {\r\n      @media screen and (max-width: 767px) {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_accordion {\r\n    margin-top: 60px;\r\n\r\n    @media screen and (max-width: 991px) {\r\n      margin-top: 15px;\r\n    }\r\n\r\n    .accordion {\r\n      .accordion-item {\r\n        background-color: transparent;\r\n        border: 0;\r\n        border-bottom: 1px solid var.$borderclr;\r\n        border-radius: 0;\r\n\r\n        .accordion-header {\r\n          border: 0;\r\n\r\n          .accordion-button {\r\n            background-color: transparent;\r\n            border: none;\r\n            color: var.$white;\r\n            font-size: 24px;\r\n            font-weight: 600;\r\n            line-height: 36px;\r\n            letter-spacing: -1px;\r\n            text-align: left;\r\n            padding: 40px 0;\r\n            border-radius: 0;\r\n            box-shadow: none;\r\n\r\n            @media screen and (max-width: 991px) {\r\n              padding: 20px 0;\r\n            }\r\n\r\n            @media screen and (max-width: 767px) {\r\n              font-size: 16px;\r\n              line-height: 24px;\r\n            }\r\n\r\n            &::after {\r\n              background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\");\r\n              background-size: 32px;\r\n              width: 32px;\r\n              height: 32px;\r\n              background-repeat: no-repeat;\r\n\r\n              @media screen and (max-width: 991px) {\r\n                background-size: 20px;\r\n                width: 20px;\r\n                height: 20px;\r\n              }\r\n            }\r\n\r\n            &[aria-expanded=\"true\"] {\r\n              transform: none;\r\n\r\n              &::after {\r\n                background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\");\r\n                height: 4px;\r\n\r\n                @media screen and (max-width: 767px) {\r\n                  height: 2px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .accordion-collapse {\r\n        .accordion-body {\r\n          padding: 0;\r\n          color: var.$white;\r\n          font-size: 24px;\r\n          font-weight: 600;\r\n          line-height: 36px;\r\n          letter-spacing: -1px;\r\n          text-align: left;\r\n          padding-bottom: 30px;\r\n          border: 0;\r\n\r\n          @media screen and (max-width: 991px) {\r\n            padding-bottom: 20px;\r\n            font-size: 18px;\r\n            line-height: 30px;\r\n          }\r\n\r\n          @media screen and (max-width: 767px) {\r\n            font-size: 15px;\r\n            line-height: 23px;\r\n          }\r\n\r\n          // FAQ List Styling\r\n          .faq_list {\r\n            list-style-type: disc; // Change to \"circle\" or \"square\" if needed\r\n            padding-left: 25px; // Indents the list properly\r\n            margin-top: 10px;\r\n\r\n            @media screen and (max-width: 767px) {\r\n              padding-left: 20px;\r\n            }\r\n\r\n            li {\r\n              color: var.$white;\r\n              font-size: 24px;\r\n              font-weight: 600;\r\n              margin-bottom: 8px; // Adds space between list items\r\n\r\n              @media screen and (max-width: 991px) {\r\n                font-size: 16px;\r\n              }\r\n\r\n              @media screen and (max-width: 767px) {\r\n                font-size: 14px;\r\n                margin-bottom: 5px;\r\n              }\r\n\r\n              strong {\r\n                font-weight: 600;\r\n                color: var.$white;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAKM;EADF;;;;;AAOF;;;;AAGE;EAHF;;;;;AAQI;;;;;;;AAME;;;;AAGE;;;;;;;;;;;;;;AAaE;EAbF;;;;;AAiBE;EAjBF;;;;;;AAsBE;;;;;;;;AAOE;EAPF;;;;;;;AAcA;;;;AAGE;;;;;AAIE;EAJF;;;;;AAcN;;;;;;;;;;;AAWE;EAXF;;;;;;;AAiBE;EAjBF;;;;;;AAuBE;;;;;;AAKE;EALF;;;;;AASE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;;AAeE", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Header.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\" as *;\r\n\r\nheader {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 9998;\r\n}\r\n\r\n.home-page {\r\n  .siteHeader {\r\n    background-color: $black !important;\r\n    border-bottom: 0;\r\n\r\n    .navMenu {\r\n      .common_dropdown {\r\n        &.dropdown {\r\n          .dropdown-toggle {\r\n\r\n            &.show,\r\n            &:hover {\r\n              @media (min-width: 1200px) {\r\n                background-color: $clr2A2E39 !important;\r\n                color: $white;\r\n\r\n                &::after {\r\n                  transform: rotate(180deg);\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          &.show {\r\n            .dropdown-toggle {\r\n              &::after {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .dropdown-menu {\r\n          @media (min-width: 1200px) {\r\n            background-color: $clr1E222D !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .common_dropdown {\r\n      &.dropdown {\r\n        .dropdown-menu {\r\n          .dropdown-item {\r\n\r\n            &:hover,\r\n            &.active,\r\n            &:focus {\r\n              background-color: $clr2A2E39 !important;\r\n            }\r\n\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: $gradientblackbg !important;\r\n              color: $clr00b9ff;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n\r\n              &:hover,\r\n              &.active {\r\n                background: $clr2A2E39 !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar {\r\n      &-collapse {\r\n        .nav-link {\r\n          @media (max-width: 1199px) {\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: linear-gradient(to right,\r\n                  #000000,\r\n                  #2d2d2d) !important;\r\n              color: #00aeef;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar-collapse {\r\n      @media (max-width: 1199px) {\r\n        background-color: rgba(0, 0, 0, 0.9) !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  @media (width >=1200px) {\r\n\r\n    .siteHeader .navbar-collapse .nav-link:hover,\r\n    .siteHeader .navbar-collapse .nav-link.active,\r\n    .siteHeader .navbar-collapse .nav-link:focus {\r\n      background-color: #2a2e39 !important;\r\n      color: #fff;\r\n    }\r\n\r\n    .languageDropdown {\r\n      width: 64px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 100%;\r\n      }\r\n\r\n      .common_dropdown {\r\n        @media (max-width: 1199px) {\r\n          width: 100%;\r\n        }\r\n\r\n        .nav-link:hover,\r\n        .nav-link.active,\r\n        .nav-link:focus {\r\n          color: #fff;\r\n          background-color: $clr2A2E39 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,\r\n    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {\r\n      background: #2a2e39 !important;\r\n    }\r\n  }\r\n}\r\n\r\n.siteHeader {\r\n  height: 80px;\r\n  padding: 1rem 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  background-color: $clr031940;\r\n  border-bottom: 1px solid $clr064197;\r\n\r\n  .btn-style {\r\n    min-height: 56px;\r\n    min-width: 169px;\r\n\r\n    @media (max-width: 1199px) {\r\n      min-height: 40px;\r\n      min-width: 120px;\r\n      padding: 8px 1rem;\r\n      font-size: 14px;\r\n    }\r\n\r\n    @media (max-width: 575px) {\r\n      min-height: 34px;\r\n      min-width: 80px;\r\n      font-size: 14px;\r\n      ;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 1199px) {\r\n    z-index: 9999;\r\n    backdrop-filter: none;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    padding: 0.625rem 0;\r\n  }\r\n\r\n  .navbar {\r\n    padding: 0;\r\n    width: 100%;\r\n\r\n    .brandLogo {\r\n      img {\r\n        max-width: 190px;\r\n        width: 100%;\r\n\r\n        @media (max-width: 767px) {\r\n          max-width: 150px;\r\n          margin-right: 0rem;\r\n        }\r\n\r\n        @media (max-width: 360px) {\r\n          max-width: 120px;\r\n          margin-right: 0rem;\r\n        }\r\n      }\r\n    }\r\n\r\n    &-collapse {\r\n      height: auto !important;\r\n\r\n      .nav-link {\r\n        font-size: 1.25rem;\r\n        font-weight: 400;\r\n        background-color: transparent;\r\n        display: flex;\r\n        align-items: center;\r\n        white-space: nowrap;\r\n        padding: 0.5rem 1.5rem;\r\n        color: $white;\r\n\r\n        &:hover,\r\n        &.active,\r\n        &:focus {\r\n          color: $baseclr;\r\n\r\n          @media (min-width: 1200px) {\r\n            background-color: $clr283f67 !important;\r\n            color: $white;\r\n          }\r\n        }\r\n\r\n        @media (min-width: 1200px) {\r\n          margin: 0 3px;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.25rem 0rem;\r\n          border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n          font-size: 1.125rem;\r\n\r\n          img {\r\n            width: 22px;\r\n          }\r\n\r\n          &.white_stroke_icon {\r\n            font-weight: 700;\r\n            background: linear-gradient(to right, #031940, #283f67);\r\n            color: #00aeef;\r\n            transition: none;\r\n\r\n            svg {\r\n              path {\r\n                fill: $clr00b9ff;\r\n              }\r\n            }\r\n\r\n            // &:hover,\r\n            // &.active {\r\n            //     background: $clr283f67;\r\n            // }\r\n          }\r\n        }\r\n      }\r\n\r\n      @media (max-width: 1199px) {\r\n        position: fixed;\r\n        left: -350px;\r\n        top: 0px;\r\n        background-color: rgba(3, 25, 64, 0.9);\r\n        backdrop-filter: blur(5px);\r\n        width: 350px;\r\n        padding: 1.25rem 1rem;\r\n        display: block;\r\n        transition: all ease-in-out 0.2s;\r\n        height: 100vh !important;\r\n        z-index: 9999;\r\n        padding: 0;\r\n\r\n        a {\r\n          display: flex;\r\n          justify-content: flex-start;\r\n          text-align: left;\r\n        }\r\n\r\n        &.show {\r\n          left: 0;\r\n          height: 100vh;\r\n        }\r\n\r\n        .navMenu {\r\n          padding: 20px;\r\n          max-height: calc(100vh - 84px);\r\n          max-height: calc(100dvh - 84px);\r\n          overflow-y: auto;\r\n        }\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        left: -100%;\r\n        width: 100%;\r\n      }\r\n\r\n      .navMenu {\r\n        .common_dropdown {\r\n          &.dropdown {\r\n            .dropdown-toggle {\r\n              padding: 0.5rem 1.5rem !important;\r\n              border-radius: 0;\r\n\r\n              @media (max-width: 1199px) {\r\n                padding: 1.25rem 0rem !important;\r\n                border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n                width: 100%;\r\n              }\r\n\r\n              &::after {\r\n                display: block;\r\n                background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\");\r\n                background-repeat: no-repeat;\r\n                background-size: 1.15rem;\r\n                background-position: center;\r\n                width: 1.15rem;\r\n                height: 1.15rem;\r\n                border: 0;\r\n                transition: all ease-in-out 0.3s;\r\n                margin-left: 1rem;\r\n\r\n                @media (max-width: 1199px) {\r\n                  margin-left: 0;\r\n                  position: absolute;\r\n                  right: 0;\r\n                }\r\n              }\r\n\r\n              &.show,\r\n              &:hover {\r\n                @media (min-width: 1200px) {\r\n                  background-color: $clr283f67;\r\n                  color: $white;\r\n\r\n                  &::after {\r\n                    transform: rotate(180deg);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            &.show {\r\n              .dropdown-toggle {\r\n                &::after {\r\n                  transform: rotate(180deg);\r\n                }\r\n              }\r\n            }\r\n\r\n            .dropdown-menu {\r\n              @media screen and (max-width: 1199px) {\r\n                position: static;\r\n                border: 0;\r\n                background-color: transparent;\r\n                padding: 0;\r\n              }\r\n\r\n              .nav-link {\r\n                padding: 0.875rem 1.5rem;\r\n                align-items: start;\r\n                font-weight: 400 !important;\r\n\r\n                @media screen and (max-width: 1199px) {\r\n                  padding: 0.875rem 1rem;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar-toggler {\r\n      background-color: transparent;\r\n      margin-left: 0;\r\n      padding: 0;\r\n      position: relative;\r\n      width: 24px;\r\n      height: 18px;\r\n\r\n      &:focus {\r\n        box-shadow: none;\r\n      }\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-right: 13px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        margin-right: 13px;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 24px;\r\n        background-color: $white;\r\n        height: 2px;\r\n        transition: all ease-in-out 0.3s;\r\n      }\r\n\r\n      &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 24px;\r\n        background-color: $white;\r\n        height: 2px;\r\n        transition: all ease-in-out 0.3s;\r\n      }\r\n\r\n      .navbar-toggler-icon {\r\n        background-image: none;\r\n        height: 2px;\r\n        background-color: $white;\r\n        width: 24px;\r\n        transition: all ease-in-out 0.3s;\r\n        display: flex;\r\n      }\r\n    }\r\n\r\n    .common_dropdown {\r\n      &.dropdown {\r\n        .dropdown-toggle {\r\n          padding: 0.5rem 0.2rem !important;\r\n          color: $white;\r\n          border: 0;\r\n          border-radius: 0.625rem;\r\n          font-size: 1.25rem;\r\n          padding: 0;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          @media (max-width: 991px) {\r\n            font-size: 1.125rem;\r\n          }\r\n\r\n          &::after {\r\n            display: none;\r\n          }\r\n\r\n          &.show {\r\n            svg {\r\n              path {\r\n                fill: $baseclr;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .dropdown-menu {\r\n          border-radius: 0.625rem;\r\n          border: 1px solid rgba(255, 255, 255, 0.3);\r\n          min-width: 200px;\r\n          position: absolute;\r\n          top: 45px;\r\n\r\n          @media screen and (max-width: 1199px) {\r\n            position: static;\r\n            padding: 0;\r\n            min-width: 100%;\r\n          }\r\n\r\n          .dropdown-item {\r\n            font-size: 1.125rem;\r\n            font-weight: 600;\r\n            padding: 0.625rem 1rem;\r\n            color: $white;\r\n\r\n            @media (max-width: 991px) {\r\n              font-size: 1rem;\r\n            }\r\n\r\n            svg,\r\n            img {\r\n              margin-right: 10px;\r\n            }\r\n\r\n            &:hover,\r\n            &.active,\r\n            &:focus {\r\n              background: $clr283f67;\r\n            }\r\n\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: $gradientbluebg;\r\n              color: $clr00b9ff;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n\r\n              &:hover,\r\n              &.active {\r\n                background: $clr283f67 !important;\r\n              }\r\n            }\r\n          }\r\n\r\n          &.show {\r\n\r\n            svg,\r\n            img {\r\n              width: 18px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    @media screen and (max-width: 1199px) {\r\n      .openmenuSidebar {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.5);\r\n        padding: 30px 15px;\r\n\r\n        .brandLogo {\r\n          padding: 0;\r\n\r\n          img {\r\n            max-width: 150px;\r\n          }\r\n        }\r\n\r\n        .navbar-toggler {\r\n          position: absolute;\r\n          right: 15px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  &.openmenu {\r\n    .navbar {\r\n      .navbar-toggler {\r\n        &::after {\r\n          transform: rotate(45deg) translate(-5px, -5px);\r\n          background-color: $white;\r\n        }\r\n\r\n        &::before {\r\n          transform: rotate(-45deg) translate(-5px, 5px);\r\n          background-color: $white;\r\n        }\r\n\r\n        .navbar-toggler-icon {\r\n          opacity: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .user_icon {\r\n    img {\r\n      width: 26px;\r\n      height: 26px;\r\n    }\r\n\r\n  }\r\n\r\n  .sidebar_backdrop {\r\n    @media screen and (max-width: 767px) {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.languageDropdown {\r\n  width: 64px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: 100%;\r\n  }\r\n\r\n  .common_dropdown {\r\n    @media (max-width: 1199px) {\r\n      width: 100%;\r\n    }\r\n\r\n    .nav-link:hover,\r\n    .nav-link.active,\r\n    .nav-link:focus {\r\n      color: #fff;\r\n      background-color: #283f67 !important;\r\n    }\r\n\r\n    &.dropdown {\r\n      .dropdown-toggle {\r\n        color: $white;\r\n        border: 0;\r\n        border-radius: 0 !important;\r\n        font-size: 1.25rem;\r\n        padding: 0;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 1rem;\r\n        }\r\n\r\n        svg {\r\n          margin-right: 10px;\r\n        }\r\n\r\n        &:focus,\r\n        &:hover {\r\n          background-color: transparent !important;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalIcon .icon {\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .globalIcon .blue {\r\n    display: none;\r\n  }\r\n\r\n  .nav-item:hover .globalIcon .black,\r\n  .nav-item.show .globalIcon .black {\r\n    display: none;\r\n  }\r\n\r\n  .nav-item:hover .globalIcon .blue,\r\n  .nav-item.show .globalIcon .blue {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.userDropdown {\r\n  &.common_dropdown {\r\n    &.dropdown {\r\n      .dropdown-toggle {\r\n        // width: 40px;\r\n        // height: 40px;\r\n        // justify-content: center;\r\n        // border-radius: 0 !important;\r\n\r\n        .user_name {\r\n          display: none;\r\n\r\n          @media screen and (max-width: 1199px) {\r\n            display: block;\r\n            padding-left: 10px;\r\n            font-size: 18px;\r\n\r\n            svg {\r\n              width: 26px;\r\n              height: 26px;\r\n            }\r\n          }\r\n        }\r\n\r\n        @media screen and (max-width: 1199px) {\r\n          border-bottom: 0 !important;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: transparent !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.brandLogo {\r\n  @media (max-width: 1199px) {\r\n    display: flex;\r\n  }\r\n\r\n  img {\r\n    @media (max-width: 1199px) {\r\n      max-width: 150px;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      max-width: 110px;\r\n    }\r\n\r\n    @media (max-width: 359px) {\r\n      max-width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n.sidebar_backdrop {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100vh;\r\n  // backdrop-filter: blur(1px);\r\n  z-index: 1000;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  transition: all ease-in-out 0.2s;\r\n}\r\n\r\n.image_color_to_white {\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n.nav-link {\r\n\r\n  &:hover,\r\n  &.active,\r\n  &:focus {\r\n    @media (min-width: 1200px) {\r\n      background-color: $clr2A2E39 !important;\r\n      color: $white;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;;AAQE;;;;;AAWY;EAFF;;;;;EAMI;;;;;AASF;;;;AAQJ;EADF;;;;;AAaI;;;;AAMA;;;;;;;AAOI;;;;AAKF;;;;AAaJ;EACE;;;;;;;EASI;;;;EA3CJ;;;;;AAuDJ;EAAA;;;;;EAUF;;;;;AAIF;EAGE;;;;;AAKE;EADF;;;;;EAKE;;;;;AASJ;;;;;;;;;;;;AAaF;;;;;AAEA;EAEA;;;;;;;;AAAA;EAKI;;;;;;;AAMF;EAXF;;;;;;;AAcI;EAKJ;;;;;AAEE;;;;;AAGF;;;;;AAKE;EAIE;;;;;;AAIE;EAJF;;;;;;AASE;;;;AACE;;;;;;;;;;;AAaF;;;;AAMA;EAAA;;;;;EAAA;;;;;AAMI;EAKJ;;;;;;EArBF;;;;EA2BI;;;;;;;EAOA;;;;EAGE;;;;;;;;;;;;;;;EAsBJ;;;;;;EAKA;;;;;EAIA;;;;;;;;AAWA;EAAA;;;;;;AAEE;;;;;AAcE;EAAA;;;;;;;AAAA;;;;;;;;;;;;;AAcI;EACA;;;;;;;AALF;EAAA;;;;;EAmBA;;;;;AAMI;;;;AACE;EAQJ;;;;;;;;AAMJ;;;;;;AAQE;EAAA;;;;;AAKE;;;;;;;;;AAUZ;;;;AACE;EAEA;;;;;AAKA;EAAA;;;;;AARF;;;;;;;;;;;AAoBE;;;;;;;;;;;AAWA;;;;;;;;;AAWA;;;;;;;;;;;AAaI;EAAA;;;;;AAMA;;;;AAPF;;;;AAcE;;;;;;;;AAaF;EACE;;;;;;;AAMA;;;;;;;AAMA;EAAA;;;;;AAGE;;;;AAOA;;;;AAKA;;;;;;;AAMA;;;;AAIE;;;;AAQA;;;;AASF;EAAA;;;;;EASR;;;;EACE;;;;EAEE;;;;;;AAKE;;;;;AAOA;;;;;AAYA;;;;AAIA;;;;;AACA;EAGF;;;;;AAQJ;;;;AACE;EAOF;;;;;AAMJ;;;;;AAYI;;;;;;;;;;AAQE;EAAA;;;;;AAIE;;;;AAJF;;;;AAiBE;EAAA;;;;;AAAA;;;;AAKA;;;;AAWN;;;;AAIA;;;;AAKA;EAAA;;;;;;EAgBQ;;;;;EADF;;;;;AAQI;;;;AAOJ;EArBF;;;;EAAA;;;;;AAsBI;EAGF;;;;;AAAA;EACE;;;;;AAQR;;;;;;;;;;;AAIA;;;;AAAA;EAeF", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Footer.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.site_footer {\r\n  background-color: var.$black;\r\n\r\n  &_inner {\r\n    padding: 70px 0;\r\n\r\n    @media screen and (max-width: 991px) {\r\n      padding: 40px 0;\r\n    }\r\n  }\r\n\r\n  &_logo {\r\n    img {\r\n      width: 200px;\r\n    }\r\n  }\r\n\r\n  &_content {\r\n    p {\r\n      color: rgba(255, 255, 255, 0.65);\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      line-height: 26px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      margin-top: 20px;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_links {\r\n    @media screen and (max-width: 767px) {\r\n      margin-top: 20px;\r\n    }\r\n\r\n    h4 {\r\n      color: var.$clrc5c5d5;\r\n      margin-bottom: 1.25rem;\r\n      font-size: 1.65rem;\r\n      line-height: 35px;\r\n      font-weight: 600;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 18px;\r\n      }\r\n    }\r\n\r\n    ul {\r\n      li {\r\n        a {\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          line-height: 24.5px;\r\n          letter-spacing: -0.10000000149011612px;\r\n          color: var.$white;\r\n          transition: all ease-in-out 0.3s;\r\n          padding-bottom: 10px;\r\n\r\n          @media screen and (max-width: 991px) {\r\n            font-size: 16px;\r\n          }\r\n\r\n          &:hover,\r\n          &.active {\r\n            color: var.$baseclr;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_copyright {\r\n    padding: 1.25rem 0;\r\n    border-top: 1px solid var.$white;\r\n\r\n    p {\r\n      text-align: center;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      line-height: 26px;\r\n      letter-spacing: -0.10000000149011612px;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAGE;;;;AAGE;EAHF;;;;;AASE;;;;AAMA;;;;;;;;;AAQE;EARF;;;;;AAeA;EADF;;;;;AAKE;;;;;;;;AAOE;EAPF;;;;;AAcI;;;;;;;;;;AASE;EATF;;;;;AAaE;;;;AASR;;;;;AAIE;;;;;;;;AAOE;EAPF", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Pricing.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.pricing {\r\n  position: relative;\r\n\r\n  &_banner {\r\n    padding: 100px 0 42px;\r\n\r\n    @media (max-width: 767px) {\r\n      padding: 50px 0 42px;\r\n    }\r\n\r\n    &_content {\r\n      h1 {\r\n        font-size: 80px;\r\n        font-weight: 800;\r\n        line-height: 90px;\r\n\r\n        @media screen and (max-width: 1199px) {\r\n          font-size: 60px;\r\n          line-height: 65px;\r\n        }\r\n\r\n        @media screen and (max-width: 991px) {\r\n          font-size: 48px;\r\n          line-height: 52.8px;\r\n          font-weight: 800;\r\n        }\r\n\r\n        @media screen and (max-width: 767px) {\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      p {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        line-height: 36px;\r\n        letter-spacing: -1px;\r\n        text-align: left;\r\n        padding-top: 2rem;\r\n\r\n        @media screen and (max-width: 991px) {\r\n          font-size: 18px;\r\n          line-height: 27px;\r\n        }\r\n\r\n        @media screen and (max-width: 767px) {\r\n          text-align: center;\r\n          margin-bottom: 20px;\r\n          padding-top: 1.25rem;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_forever {\r\n      background: linear-gradient(139.01deg,\r\n          #26334d 18.26%,\r\n          #4e5d7a 63.25%,\r\n          #26334d 100.07%),\r\n        radial-gradient(27.58% 27.58% at 50% 50%,\r\n          rgb(254, 165, 1) 0%,\r\n          rgb(254, 165, 1) 100%);\r\n      border-radius: 40px;\r\n      padding: 50px 20px;\r\n      text-align: center;\r\n      position: relative;\r\n      margin: auto;\r\n      max-width: 22em;\r\n      box-sizing: border-box;\r\n      background-clip: padding-box;\r\n      /* !importanté */\r\n      border: solid 2px transparent;\r\n      /* !importanté */\r\n\r\n      &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        z-index: -1;\r\n        margin: -2px;\r\n        /* !importanté */\r\n        border-radius: inherit;\r\n        /* !importanté */\r\n        background: linear-gradient(142.34deg,\r\n            #00adef 0.06%,\r\n            rgba(254, 165, 0, 0) 46.5%,\r\n            #00adef 98.85%);\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        h4 {\r\n          font-size: 20px;\r\n          line-height: 25px;\r\n        }\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding: 30px 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_table {\r\n    position: relative;\r\n    z-index: 1;\r\n    padding-bottom: 7rem;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      background-image: url(\"https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png\");\r\n      background-position: center;\r\n      background-repeat: no-repeat;\r\n      background-size: 100%;\r\n      border-radius: 2rem;\r\n      z-index: -1;\r\n    }\r\n\r\n    &_switch {\r\n      margin-bottom: 25px;\r\n\r\n      p {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        line-height: 36px;\r\n        text-align: left;\r\n      }\r\n\r\n      .checkbox_input {\r\n        margin: 0 20px;\r\n      }\r\n    }\r\n\r\n    &_col {\r\n      &:first-child {\r\n        .pricing_table_box {\r\n          border-right: 0;\r\n          border-top-left-radius: 50px;\r\n          border-bottom-left-radius: 50px;\r\n        }\r\n      }\r\n\r\n      &:last-child {\r\n        .pricing_table_box {\r\n          border-left: 0;\r\n          border-top-right-radius: 50px;\r\n          border-bottom-right-radius: 50px;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_box {\r\n      background: #00000033;\r\n      border: 3px solid #ffffff1a;\r\n      padding: 50px 20px;\r\n      width: 100%;\r\n\r\n      @media (min-width: 1400px) {\r\n        padding: 50px 30px;\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        border: 3px solid #ffffff1a !important;\r\n        border-radius: 30px;\r\n        margin-top: 30px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding: 30px 20px;\r\n      }\r\n\r\n      &_heading {\r\n        text-align: center;\r\n\r\n        h2 {\r\n          font-size: 48px;\r\n          font-weight: 800;\r\n          text-align: center;\r\n          margin: 20px 0;\r\n\r\n          @media screen and (max-width: 991px) {\r\n            font-size: 36px;\r\n          }\r\n\r\n          @media screen and (max-width: 767px) {\r\n            font-size: 28px;\r\n          }\r\n\r\n          span {\r\n            font-size: 24px;\r\n\r\n            @media screen and (max-width: 991px) {\r\n              font-size: 18px;\r\n            }\r\n\r\n            @media screen and (max-width: 767px) {\r\n              font-size: 16px;\r\n            }\r\n          }\r\n        }\r\n\r\n        p {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          line-height: 27px;\r\n          text-align: center;\r\n          margin: 20px 0;\r\n\r\n          a {\r\n            &:hover {\r\n              color: var.$green;\r\n            }\r\n          }\r\n        }\r\n\r\n        .green-btn {\r\n          @media screen and (max-width: 991px) {\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      ul {\r\n        margin-top: 30px;\r\n\r\n        @media (min-width: 768px) and (max-width: 991px) {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n        }\r\n\r\n        li {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          line-height: 27px;\r\n          margin: 20px 0;\r\n          color: var.$white;\r\n\r\n          @media (min-width: 768px) and (max-width: 991px) {\r\n            width: 50%;\r\n          }\r\n\r\n          svg {\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_col {\r\n    display: flex;\r\n  }\r\n\r\n  &_box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n  }\r\n\r\n  ul {\r\n    flex-grow: 1;\r\n  }\r\n\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAGE;;;;AAGE;EAHF;;;;;AAQI;;;;;;AAKE;EALF;;;;;;AAUE;EAVF;;;;;;;AAgBE;EAhBF;;;;;AAqBA;;;;;;;;;AAQE;EARF;;;;;;AAaE;EAbF;;;;;;;AAqBF;;;;;;;;;;;;;;;;;;;AAoBE;;;;;;;;;;;;;AAkBA;EACE;;;;;;AAMF;EA7CF;;;;;AAmDF;;;;;;AAKE;;;;;;;;;;;;;;;AAeA;;;;AAGE;;;;;;;AAOA;;;;AAOE;;;;;;AAQA;;;;;;AAQJ;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;;;AAgBE;EAhBF;;;;;AAoBE;;;;AAGE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAcE;;;;AAGE;EAHF;;;;;AAOE;EAPF;;;;;AAaF;;;;;;;;AAQI;;;;AAOF;EADF;;;;;AAOF;;;;AAGE;EAHF;;;;;;AAQE;;;;;;;;AAOE;EAPF;;;;;AAWE;;;;AAQR;;;;AAIA;;;;;;;AAOA", "debugId": null}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonTooltip.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.tooltip-container {\r\n  position: relative;\r\n  display: inline-block;\r\n  cursor: pointer;\r\n\r\n  img {\r\n    max-width: 20px;\r\n    min-width: 20px;\r\n    min-height: 20px;\r\n    max-height: 20px;\r\n  }\r\n}\r\n\r\n.tooltip-wrapper {\r\n  flex-shrink: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.tooltip-box {\r\n  position: absolute;\r\n  padding: 5px 10px;\r\n  border-radius: 5px;\r\n  z-index: 1000;\r\n  min-width: 300px;\r\n  width: 300px;\r\n  background-color: hwb(210 1% 65% / 0.699);\r\n  color: var.$white;\r\n  text-align: left;\r\n  padding: 10px 15px;\r\n  border-radius: 5px;\r\n  backdrop-filter: blur(6px);\r\n  pointer-events: auto !important;\r\n\r\n  p,\r\n  a {\r\n    font-size: 0.875rem !important;\r\n    font-weight: 300;\r\n    line-height: 20px;\r\n\r\n    @media screen and (max-width: 991px) {\r\n      font-size: 14px;\r\n      line-height: 18px;\r\n    }\r\n  }\r\n\r\n  @media screen and (max-width: 991px) {\r\n    min-width: 200px;\r\n    width: 200px;\r\n  }\r\n\r\n}\r\n\r\n.tooltip-top-left {\r\n  top: 0;\r\n  right: 0;\r\n  transform: translateY(-100%);\r\n}\r\n\r\n.tooltip-top-right {\r\n  top: 0;\r\n  left: 0;\r\n  transform: translateY(-100%);\r\n}\r\n\r\n.tooltip-bottom-left {\r\n  bottom: 0;\r\n  right: 0;\r\n  transform: translateY(100%);\r\n}\r\n\r\n.tooltip-bottom-right {\r\n  bottom: 0;\r\n  left: 0;\r\n  transform: translateY(100%);\r\n}\r\n\r\n.tooltip-center-bottom {\r\n  top: 25px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n}\r\n\r\n.tooltip-center-top {\r\n  bottom: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%) translateY(-10px);\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;AAKE;;;;;;;AAQF;;;;;AAKA;;;;;;;;;;;;;;;AAaE;;;;;;AAKE;EAGA;;;;;EACE;;;;;;AAKJ;;;;;;AAOF;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA", "debugId": null}}]}