{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/app/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/app/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/app.scss", "turbopack:///turbopack:///[project]/src/app/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/assets/fonts/fonts.scss", "turbopack:///turbopack:///[project]/src/app/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/globals.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"./theme/var\";\r\n\r\nbody {\r\n  font-size: 1rem;\r\n  overflow-x: clip;\r\n  background-color: var.$themeclr;\r\n  font-family: var(--font-gilroy);\r\n}\r\n\r\n*,\r\n::after,\r\n::before {\r\n  box-sizing: border-box;\r\n}\r\n\r\ndiv.__toast,\r\n.react-hot-toast,\r\n[data-sonner-toast],\r\n[data-toast] {\r\n  z-index: 10000 !important;\r\n}\r\n\r\nhr {\r\n  color: var.$white;\r\n}\r\n\r\nimg {\r\n  max-width: 100%;\r\n}\r\n\r\nul {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style: none;\r\n}\r\n\r\na,\r\na:hover {\r\n  text-decoration: none;\r\n  transition: all ease-in-out 0.3s;\r\n  color: var.$baseclr;\r\n}\r\n\r\n.text-link {\r\n  color: var.$baseclr;\r\n\r\n  &:hover {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\na,\r\nspan {\r\n  display: inline-block;\r\n}\r\n\r\nsvg {\r\n  path {\r\n    transition: all ease-in-out 0.3s;\r\n  }\r\n}\r\n\r\nol {\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\nsmall {\r\n  font-family: var.$basefont;\r\n}\r\n\r\nbody {\r\n  overflow: hidden;\r\n}\r\n\r\nh1,\r\n.h1,\r\nh2,\r\n.h2,\r\nh3,\r\n.h3,\r\nh4,\r\n.h4,\r\nh5,\r\n.h5,\r\nh6,\r\n.h6,\r\np {\r\n  margin-bottom: 0;\r\n  color: var.$white;\r\n}\r\n\r\nh1,\r\n.h1 {\r\n  font-size: 3rem;\r\n  font-weight: 800;\r\n\r\n  // @media (max-width:1279px) {\r\n  //   font-size: 3rem;\r\n  // }\r\n\r\n  @media (max-width: 1199px) {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  @media (max-width: 390px) {\r\n    font-size: 1.30rem;\r\n  }\r\n}\r\n\r\nh2,\r\n.h2 {\r\n  font-size: 5rem;\r\n  font-weight: 800;\r\n\r\n  @media (max-width: 1269px) {\r\n    font-size: 3rem;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1.363rem;\r\n  }\r\n}\r\n\r\nh3,\r\n.h3 {\r\n  font-size: 2.8rem;\r\n  font-weight: 800;\r\n\r\n  @media (max-width: 1199px) {\r\n    font-size: 1.688rem;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\nh4,\r\n.h4 {\r\n  font-size: 1.65rem;\r\n  line-height: 35px;\r\n  font-weight: 600;\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1.15rem;\r\n    line-height: 25px;\r\n  }\r\n}\r\n\r\nh5,\r\n.h5 {\r\n  font-size: 1.25rem;\r\n  line-height: 30px;\r\n  font-weight: 600;\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1rem;\r\n    line-height: 25px;\r\n  }\r\n}\r\n\r\nh6,\r\n.h6 {\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\np {\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n}\r\n\r\n.font-weight-400 {\r\n  font-weight: 400;\r\n}\r\n\r\n.font-weight-500 {\r\n  font-weight: 500;\r\n}\r\n\r\n.font-weight-600 {\r\n  font-weight: 600;\r\n}\r\n\r\n.font-weight-700 {\r\n  font-weight: 700;\r\n}\r\n\r\n.font-weight-800 {\r\n  font-weight: 800;\r\n}\r\n\r\n.divider {\r\n  height: 1px;\r\n  width: 100%;\r\n  background-color: var.$borderclr;\r\n  opacity: 1;\r\n  margin: 1.25rem 0;\r\n}\r\n\r\n.pt-40 {\r\n  padding-top: 40px;\r\n}\r\n\r\n.pt-50 {\r\n  padding-top: 50px;\r\n}\r\n\r\n.pt-70 {\r\n  padding-top: 70px;\r\n}\r\n\r\n.pb-40 {\r\n  padding-bottom: 40px;\r\n}\r\n\r\n.pb-50 {\r\n  padding-bottom: 50px;\r\n}\r\n\r\n.pb-60 {\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.pb-70 {\r\n  padding-bottom: 70px;\r\n}\r\n\r\n.py-40 {\r\n  padding: 40px 0;\r\n}\r\n\r\n.py-80 {\r\n  padding: 80px 0;\r\n\r\n  @media (max-width: 767px) {\r\n    padding: 40px 0;\r\n  }\r\n}\r\n\r\n.py-100 {\r\n  padding: 100px 0;\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 70px 0 !important;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    padding: 50px 0 !important;\r\n  }\r\n}\r\n\r\n.mt-10 {\r\n  margin-top: 10px !important;\r\n}\r\n\r\n.mt-20 {\r\n  margin-top: 20px !important;\r\n}\r\n\r\n.mt-30 {\r\n  margin-top: 30px !important;\r\n}\r\n\r\n.mt-40 {\r\n  margin-top: 40px !important;\r\n\r\n  @media (max-width: 767px) {\r\n    margin-top: 30px !important;\r\n  }\r\n}\r\n\r\n.mt-50 {\r\n  margin-top: 50px !important;\r\n\r\n  @media (max-width: 767px) {\r\n    margin-top: 30px !important;\r\n  }\r\n}\r\n\r\n.my-10 {\r\n  margin: 10px 0 !important;\r\n}\r\n\r\n.my-20 {\r\n  margin: 20px 0 !important;\r\n}\r\n\r\n.my-30 {\r\n  margin: 30px 0 !important;\r\n}\r\n\r\n.my-40 {\r\n  margin: 40px 0 !important;\r\n\r\n  @media (max-width: 767px) {\r\n    margin: 30px 0 !important;\r\n  }\r\n}\r\n\r\n.my-50 {\r\n  margin: 50px 0 !important;\r\n\r\n  @media (max-width: 767px) {\r\n    margin: 30px 0 !important;\r\n  }\r\n}\r\n\r\nfigure {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.black_text {\r\n  color: var.$black;\r\n}\r\n\r\n.white_text {\r\n  color: #fff;\r\n}\r\n\r\n.red_text {\r\n  color: var.$red !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$red !important;\r\n    }\r\n  }\r\n}\r\n\r\n.green_text {\r\n  color: var.$green !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$green !important;\r\n    }\r\n  }\r\n}\r\n\r\n.gray_text {\r\n  color: var.$greytext !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$greytext !important;\r\n    }\r\n  }\r\n}\r\n\r\n.white_icon {\r\n  svg {\r\n    path {\r\n      fill: var.$white !important;\r\n    }\r\n  }\r\n}\r\n\r\n.yellowlight_text {\r\n  color: var.$yellowlighttext !important;\r\n}\r\n\r\n.greenlight_text {\r\n  color: var.$greenlighttext !important;\r\n}\r\n\r\n.redlight_text {\r\n  color: var.$redlighttext !important;\r\n}\r\n\r\n.darkblue_text {\r\n  color: var.$clr04498C !important;\r\n}\r\n\r\n.blue_text {\r\n  color: var.$baseclr !important;\r\n}\r\n\r\n.grey_text {\r\n  color: var.$textclr !important;\r\n}\r\n\r\n.lightgrey_text {\r\n  color: var.$lightgreyclr !important;\r\n}\r\n\r\n.darkgrey_text {\r\n  color: var.$darkgreytext !important;\r\n}\r\n\r\n.yellow_text {\r\n  color: var.$yellow !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$yellow !important;\r\n    }\r\n  }\r\n}\r\n\r\n.green_bg {\r\n  background-color: var.$greenlightbg !important;\r\n}\r\n\r\n.red_bg {\r\n  background-color: var.$redlightclr !important;\r\n}\r\n\r\n.blue_bg {\r\n  background-color: var.$clr031940 !important;\r\n}\r\n\r\n.baseblue_bg {\r\n  background-color: #3791d3 !important;\r\n  border-radius: 1.25rem !important;\r\n\r\n  .solidArrow {\r\n    svg {\r\n      path {\r\n        fill: var.$baseclr !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    background: linear-gradient(0deg, #ffffff28, #ffffff28),\r\n      linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;\r\n  }\r\n}\r\n\r\n.bluelight_bg {\r\n  background-color: var.$clr04498C !important;\r\n  color: var.$white !important;\r\n}\r\n\r\n.greenlight_bg {\r\n  background-color: var.$greenlightbg !important;\r\n}\r\n\r\n.white_bg {\r\n  background-color: var.$white !important;\r\n}\r\n\r\n.whitelight_bg {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n.Redgrandient {\r\n  background: linear-gradient(0deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.1)),\r\n    linear-gradient(270.33deg,\r\n      rgba(255, 105, 106, 0.4) 0%,\r\n      rgba(255, 105, 106, 0.2) 50%,\r\n      rgba(255, 105, 106, 0.4) 100%) !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$redlightclr !important;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    background: linear-gradient(0deg, #ffffff28, #ffffff28),\r\n      linear-gradient(270.33deg, #ff696a80, #ff696a40, #ff696a80) !important;\r\n  }\r\n}\r\n\r\n.greengrandient {\r\n  background: linear-gradient(0deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.1)),\r\n    linear-gradient(270.33deg,\r\n      rgba(50, 205, 51, 0.4) 0%,\r\n      rgba(50, 205, 51, 0.15) 45.5%,\r\n      rgba(50, 205, 51, 0.4) 98%) !important;\r\n\r\n  svg {\r\n    path {\r\n      fill: var.$green !important;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    background: linear-gradient(0deg, #ffffff28, #ffffff28),\r\n      linear-gradient(270.33deg, #32cd3380, #32cd3340 45.5%, #32cd3380 98%) !important;\r\n  }\r\n}\r\n\r\n.bluegrandient {\r\n  background: linear-gradient(0deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.1)),\r\n    linear-gradient(270.33deg,\r\n      rgba(0, 173, 239, 0.4) 0%,\r\n      rgba(0, 173, 239, 0.15) 45.5%,\r\n      rgba(0, 173, 239, 0.4) 98%) !important;\r\n  border-radius: 1.25rem !important;\r\n\r\n  .solidArrow {\r\n    svg {\r\n      path {\r\n        fill: var.$baseclr !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    background: linear-gradient(0deg, #ffffff28, #ffffff28),\r\n      linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;\r\n  }\r\n}\r\n\r\n.greengrandientbg {\r\n  border-radius: 1.25rem !important;\r\n  background: linear-gradient(0deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.1)),\r\n    linear-gradient(270.33deg,\r\n      rgba(50, 205, 51, 0.4) 0%,\r\n      rgba(50, 205, 51, 0.15) 45.5%,\r\n      rgba(50, 205, 51, 0.4) 98%) !important;\r\n  border: 0 !important;\r\n}\r\n\r\n.redgrandientbg {\r\n  border-radius: 1.25rem !important;\r\n  background: linear-gradient(0deg,\r\n      rgba(255, 255, 255, 0.1),\r\n      rgba(255, 255, 255, 0.1)),\r\n    linear-gradient(270.33deg,\r\n      rgba(255, 105, 106, 0.4) 0%,\r\n      rgba(255, 105, 106, 0.2) 50%,\r\n      rgba(255, 105, 106, 0.4) 100%) !important;\r\n  border: 0 !important;\r\n}\r\n\r\n.bluedark_bg {\r\n  background: var.$clr04498C !important;\r\n}\r\n\r\n.cardgrandient {\r\n  background: radial-gradient(50% 50% at 50% 50%,\r\n      rgba(0, 185, 255, 0.5) 21.5%,\r\n      rgba(0, 83, 153, 0.5) 100%),\r\n    linear-gradient(135deg,\r\n      rgba(255, 255, 255, 0) 0%,\r\n      rgba(255, 255, 255, 0.2) 47.5%,\r\n      rgba(255, 255, 255, 0) 100%);\r\n}\r\n\r\n.green_arrow {\r\n  svg {\r\n    path {\r\n      fill: var.$green !important;\r\n    }\r\n  }\r\n}\r\n\r\nbody {\r\n  ::-webkit-scrollbar {\r\n    width: 5px;\r\n    height: 4px;\r\n    border-radius: 1rem;\r\n  }\r\n\r\n  ::-webkit-scrollbar-track {\r\n    box-shadow: none;\r\n  }\r\n\r\n  ::-webkit-scrollbar-thumb {\r\n    background-color: #00adef;\r\n    border-radius: 1rem;\r\n  }\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding-left: 20px !important;\r\n  padding-right: 20px !important;\r\n\r\n  @media (min-width: 1400px) {\r\n    max-width: 1300px;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n  }\r\n}\r\n\r\ninput:-webkit-autofill,\r\ninput:-webkit-autofill:hover,\r\ninput:-webkit-autofill:focus,\r\ninput:-webkit-autofill:active {\r\n  transition: background-color 5000s ease-in-out 0s;\r\n  caret-color: transparent !important;\r\n  -webkit-text-fill-color: var.$black;\r\n}\r\n\r\n.commonCard {\r\n  background-color: var.$cardbg;\r\n  padding: 2.5em;\r\n  border-radius: 0.625rem;\r\n  border: 1px solid var.$baseclr;\r\n\r\n  @media (max-width: 1399px) {\r\n    padding: 2em;\r\n  }\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 1.25em 1rem;\r\n  }\r\n}\r\n\r\n.borderTabs {\r\n  white-space: nowrap;\r\n  flex-wrap: nowrap;\r\n  overflow-x: auto;\r\n  overflow-y: clip;\r\n  border-bottom: 0;\r\n\r\n  &.nav {\r\n    border-bottom: 1px solid var.$baseclr;\r\n\r\n    .nav-item {\r\n\r\n      .nav-link {\r\n        color: var.$baseclr;\r\n        font-size: 1rem;\r\n        font-weight: 600;\r\n        line-height: normal;\r\n        background-color: transparent;\r\n        border: 0;\r\n        border-bottom: 3px solid transparent;\r\n        border-radius: 0;\r\n        padding: 0 1.5rem 1rem 1.5rem;\r\n        transition: all ease-in-out 0.3s;\r\n\r\n        @media (max-width: 767px) {\r\n          font-size: 1rem;\r\n          // padding: 0rem 0rem 1rem;\r\n        }\r\n\r\n        &.active {\r\n          border-bottom: 5px solid var.$baseclr;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.radioBtn {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .checkbox_input {\r\n    .form-check {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: 1px solid var.$borderclr;\r\n      border-radius: 1rem;\r\n      min-height: 64px;\r\n      padding: 0.5rem 1.25rem;\r\n      cursor: pointer;\r\n      transition: all ease-in-out 0.3s;\r\n\r\n      @media (max-width: 1399px) {\r\n        min-height: 50px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding: 0.5rem 1rem;\r\n      }\r\n\r\n      .form-check-input {\r\n        margin: 0;\r\n        cursor: pointer;\r\n        width: 20px !important;\r\n        height: 20px !important;\r\n        background-color: transparent;\r\n      }\r\n\r\n      .form-check-label {\r\n        margin-bottom: 0;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 1.25rem;\r\n        margin: 0;\r\n        color: var.$greytext;\r\n\r\n        @media (max-width: 575px) {\r\n          padding-left: 0.5rem;\r\n        }\r\n\r\n        .radioIcon {\r\n          margin-right: 0.625rem;\r\n        }\r\n      }\r\n\r\n      &.active,\r\n      &:hover {\r\n        background-color: var.$blackbg;\r\n        border-color: var.$baseclr;\r\n\r\n        .form-check-label {\r\n          color: var.$textclr;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.big_tabs {\r\n  &.nav {\r\n    .nav-item {\r\n      display: flex;\r\n\r\n      .nav-link {\r\n        padding: 1.5rem 1rem;\r\n        background-color: rgba(4, 73, 140, 0.2);\r\n        border: 2px solid rgba(4, 73, 140, 0.2);\r\n        width: 100%;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        font-size: 24px;\r\n        font-weight: 700;\r\n        letter-spacing: -1px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        flex-direction: column;\r\n        color: var.$white;\r\n\r\n        @media (max-width: 767px) {\r\n          font-size: 18px;\r\n          padding: 1rem 0.5rem;\r\n        }\r\n\r\n        .tabs_icon {\r\n          display: block;\r\n          margin-bottom: 14px;\r\n          width: 56px;\r\n          height: 56px;\r\n          background-color: rgba(3, 25, 64, 0.3);\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-bottom: 14px;\r\n        }\r\n\r\n        &.active,\r\n        &:hover,\r\n        &:focus {\r\n          background: linear-gradient(180deg, #04498c 0%, #011426 100%) !important;\r\n          border: 2px solid rgba(0, 173, 239, 0.5);\r\n          color: var.$white;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.slider-container {\r\n  .slick-slider {\r\n    // .slick-list {\r\n    //   .slick-track {\r\n    //     .slick-slide {\r\n    //     }\r\n    //   }\r\n    // }\r\n\r\n    .slick-arrow {\r\n\r\n      &.slick-prev,\r\n      &.slick-next {\r\n        position: absolute;\r\n        bottom: 0px;\r\n        width: 50px;\r\n        height: 50px;\r\n        border-radius: 10rem;\r\n        background-color: var.$baseclr;\r\n        z-index: 2;\r\n\r\n        @media (max-width: 991px) {\r\n          width: 32px;\r\n          height: 32px;\r\n        }\r\n      }\r\n\r\n      &.slick-prev {\r\n        left: -25px;\r\n\r\n        @media (min-width: 576px) and (max-width: 767px) {\r\n          left: 5px;\r\n        }\r\n\r\n        @media (max-width: 374px) {\r\n          left: 5px;\r\n        }\r\n\r\n        &:before {\r\n          content: \"\";\r\n          width: 8px;\r\n          height: 15px;\r\n          background-image: url(\"https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\");\r\n          background-repeat: no-repeat;\r\n          background-size: 100%;\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 48%;\r\n          transform: translate(-50%, -50%) rotate(180deg);\r\n          opacity: 1;\r\n        }\r\n      }\r\n\r\n      &.slick-next {\r\n        right: -25px;\r\n\r\n        @media (min-width: 576px) and (max-width: 767px) {\r\n          right: 5px;\r\n        }\r\n\r\n        @media (max-width: 374px) {\r\n          right: 5px;\r\n        }\r\n\r\n        &:before {\r\n          content: \"\";\r\n          width: 8px;\r\n          height: 15px;\r\n          background-image: url(\"https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\");\r\n          background-repeat: no-repeat;\r\n          background-size: 100%;\r\n          position: absolute;\r\n          top: 54%;\r\n          left: 52%;\r\n          transform: translate(-50%, -50%);\r\n          opacity: 1;\r\n\r\n          // @media (max-width: 991px) {\r\n          //   width: 16px;\r\n          //   height: 12px;\r\n          // }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.common_dropdown {\r\n  &.dropdown {\r\n    .dropdown-toggle {\r\n      color: var.$white;\r\n      border: 0;\r\n      border-radius: 0.625rem;\r\n      font-size: 1.25rem;\r\n      padding: 0.625rem 1.25rem;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 1.8rem;\r\n      }\r\n\r\n      @media (min-width: 1200px) {\r\n        &:hover {\r\n          background-color: var.$clr283f67;\r\n          color: var.$white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .dropdown-menu {\r\n      background-color: #031940;\r\n      border-radius: 0.625rem;\r\n      border: 1px solid rgba(255, 255, 255, 0.3);\r\n      min-width: 200px;\r\n\r\n      .dropdown-item {\r\n        font-size: 1.25rem;\r\n        font-weight: 600;\r\n        padding: 0.625rem 1rem;\r\n        color: var.$white;\r\n\r\n        &:hover {\r\n          background-color: var.$clr283f67;\r\n          color: var.$white;\r\n        }\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 1rem;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.home-page {\r\n  .common_dropdown {\r\n    &.dropdown {\r\n      .dropdown-menu {\r\n        background-color: var.$clr1E222D;\r\n\r\n        .dropdown-item {\r\n          &:hover {\r\n            background-color: var.$clr2A2E39;\r\n            color: var.$white;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-control {\r\n  min-height: 56px;\r\n  box-shadow: none;\r\n  outline: none;\r\n  width: 100%;\r\n  padding: 0.5rem 1.25rem;\r\n  border-radius: 1rem;\r\n  border: 1px solid var.$inputbgclr;\r\n  background-color: var.$inputbgclr;\r\n  color: var.$white;\r\n  font-size: 1rem;\r\n\r\n  @media (max-width: 1599px) {\r\n    min-height: 52px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  &.is-invalid,\r\n  &.was-validated,\r\n  &:invalid {\r\n    background-image: none;\r\n    border-color: var.$red;\r\n  }\r\n\r\n  &:hover {\r\n    appearance: none;\r\n  }\r\n\r\n  &::placeholder {\r\n    color: var.$white;\r\n    opacity: 0.4;\r\n  }\r\n\r\n  &:disabled {\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    box-shadow: none;\r\n    border: 1px solid var.$inputbgclr;\r\n    background-color: var.$inputbgclr;\r\n    color: var.$white;\r\n  }\r\n\r\n  &.passwordInput {\r\n    padding-right: 4.375rem;\r\n  }\r\n}\r\n\r\n.login_fontStyle_forget {\r\n  font-weight: 700;\r\n}\r\n\r\n.text_area_bg {\r\n  width: 100%;\r\n  height: 15rem;\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.text-pre-line {\r\n  white-space: pre-line;\r\n}\r\n\r\n\r\n\r\n.select-btn {\r\n  border-radius: 7px !important;\r\n  background: #136fcb;\r\n  width: 50%;\r\n  margin: 10px 0px;\r\n}\r\n\r\n.green_btn {\r\n  background-color: var.$green !important;\r\n  color: #fff;\r\n}\r\n\r\n.green_btn:hover {\r\n  background-color: var.$greenbtnhover !important;\r\n}\r\n\r\n.white_btn {\r\n  background-color: var.$white !important;\r\n  color: var.$black !important;\r\n  border: 1px solid #00000033 !important;\r\n}\r\n\r\n.yellow_btn {\r\n  background-color: var.$yellow !important;\r\n  color: #fff;\r\n}\r\n\r\n.password_check p {\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.password_check .box1 p {\r\n  color: #FF696A;\r\n  font-weight: 600;\r\n}\r\n\r\n.password_check .box1_bg {\r\n  background-color: #FF696A;\r\n}\r\n\r\n.password_check .box2_bg {\r\n  background-color: #FF6A23;\r\n}\r\n\r\n.password_check .box3_bg {\r\n  background-color: #FFA723;\r\n}\r\n\r\n.password_check .box4_bg {\r\n  background-color: #FCD53F;\r\n}\r\n\r\n.password_check .box5_bg {\r\n  background-color: #DFF33B;\r\n}\r\n\r\n.password_check .white10_bg {\r\n  background-color: #ffffff10;\r\n}\r\n\r\n.security_check .user_email {\r\n  color: #32CD33;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.security_check_input input {\r\n  height: 57px;\r\n  width: 58px;\r\n  border-radius: 15px;\r\n  background-color: #ffffff30;\r\n  text-align: center;\r\n  font-size: 30px;\r\n}\r\n\r\n.security_check_input input:focus-visible {\r\n  outline: 1px solid white !important;\r\n}\r\n\r\n.security_check_resend_btn {\r\n  background-color: #031940;\r\n  padding: 10px 50px;\r\n  border-radius: 50px;\r\n  transition: all .3s ease-in-out;\r\n  font-weight: 600;\r\n  font-size: 20px;\r\n}\r\n\r\n.security_check_resend_btn:hover {\r\n  background-color: #0099d1;\r\n  color: #fff;\r\n}\r\n\r\n.security_check_resend_btn:disabled,\r\n.security_check_resend_btn:disabled:hover {\r\n  background-color: #6c757d;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.rotate {\r\n  animation: spin 1s linear;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.svg-baseblue {\r\n  filter: invert(49%) sepia(63%) saturate(5181%) hue-rotate(171deg) brightness(96%) contrast(97%);\r\n  width: 33px;\r\n  height: 32px;\r\n}\r\n\r\n.baseblue_border {\r\n  border-color: var.$baseclr !important;\r\n}\r\n\r\n.darkblue_border {\r\n  border-color: var.$clr04498C !important;\r\n}\r\n\r\n.darkgray_border {\r\n  border-color: var.$darkgreytext !important;\r\n}\r\n\r\n.portfolio-blur-overlay {\r\n  position: fixed;\r\n  z-index: 99999;\r\n  inset: 0;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  backdrop-filter: blur(3px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .loader-content {\r\n    text-align: center;\r\n    font-weight: bold;\r\n    color: #1338ff;\r\n\r\n    .spinner-border {\r\n      width: 2.5rem;\r\n      height: 2.5rem;\r\n    }\r\n  }\r\n}\r\n\r\n.loading-screen {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n}", "/* Regular */\r\n@font-face {\r\n    font-family: 'Gilroy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/<PERSON>roy-Regular.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/<PERSON><PERSON>-Regular.woff2') format('woff2');\r\n    font-weight: 400;\r\n    font-style: normal;\r\n}\r\n\r\n/* Bold */\r\n@font-face {\r\n    font-family: '<PERSON>roy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/<PERSON><PERSON>-Bold.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff2') format('woff2');\r\n    font-weight: 700;\r\n    font-style: normal;\r\n}\r\n@font-face {\r\n    font-family: '<PERSON><PERSON>-Bold';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/<PERSON><PERSON>-Bold.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/<PERSON><PERSON>-Bold.woff2') format('woff2');\r\n    font-weight: 400;\r\n    font-style: normal;\r\n}\r\n\r\n/* Extra Bold */\r\n@font-face {\r\n    font-family: 'Gilroy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff2') format('woff2');\r\n    font-weight: 900;\r\n    font-style: normal;\r\n}\r\n\r\n/* Medium */\r\n@font-face {\r\n    font-family: 'Gilroy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff2') format('woff2');\r\n    font-weight: 500;\r\n    font-style: normal;\r\n}\r\n\r\n/* Italic */\r\n@font-face {\r\n    font-family: 'Gilroy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff2') format('woff2');\r\n    font-weight: 400;\r\n    font-style: italic;\r\n}\r\n\r\n/* Bold Italic */\r\n@font-face {\r\n    font-family: 'Gilroy';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff2') format('woff2');\r\n    font-weight: 700;\r\n    font-style: italic;\r\n}\r\n\r\n/* Semi Bold */\r\n@font-face {\r\n    font-family: 'Gilroy-Semibold';\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff') format('woff');\r\n    src: url('https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff2') format('woff2');\r\n    font-weight: 400;\r\n    font-style: normal;\r\n}", "@import '../css/app.scss';\r\n@import '../assets/fonts/fonts.scss';\r\n@import '../css/theme/_var.scss';\r\n\r\n@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\n:root {\r\n  --foreground: #ffffff;\r\n  --background: #011132;\r\n  --font-gilroy: '<PERSON>roy', sans-serif;\r\n}\r\n\r\n@media (prefers-color-scheme: light) {\r\n  :root {\r\n    --background: #011132;\r\n    --foreground: #ededed;\r\n  }\r\n}\r\n\r\nbody {\r\n  color: var(--foreground);\r\n  background: var(--background);\r\n  font-family: var(--font-gilroy);\r\n}\r\n\r\n.osano-cm-widget {\r\n  display: none;\r\n}\r\n\r\n.osano-cm-dialog {\r\n  border: 1px solid #00719d;\r\n}\r\n\r\n.arrow-right {\r\n  width: 50px !important;\r\n  height: 50px !important;\r\n  border-radius: 10rem;\r\n  background-color: #00adef !important;\r\n  z-index: 2;\r\n}\r\n\r\n.slick-prev,\r\n.slick-next {\r\n  position: relative !important;\r\n  left: 0px !important;\r\n  right: 0px !important;\r\n}\r\n\r\n.popup {\r\n  position: absolute;\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n  overflow: auto;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);\r\n  z-index: 1000;\r\n}\r\n\r\n.nextjs-toast-errors-parent {\r\n  display: none;\r\n}\r\n\r\n.text-sec {\r\n  color: #ffffff\r\n}\r\n\r\n.font-14 {\r\n  font-size: 14px;\r\n  ;\r\n}\r\n\r\n.font-18 {\r\n  font-size: 18px;\r\n}\r\n\r\n.popup-container {\r\n  position: fixed;\r\n  inset: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.min-h-500 {\r\n  height: 600px !important;\r\n  min-height: 600px !important;\r\n  min-width: 350px !important;\r\n}\r\n\r\n.caret {\r\n  animation: blink 1s step-end infinite;\r\n}\r\n\r\n.scroll-lock {\r\n  position: fixed;\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n@keyframes blink {\r\n  50% {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.new-link {\r\n  transition: all .3s ease-in-out;\r\n  color: #00adef !important;\r\n}\r\n\r\n.text-md-nowrap {\r\n  white-space: nowrap;\r\n}\r\n\r\n@media (max-width: 700px) {\r\n  .text-md-nowrap {\r\n    white-space: unset;\r\n  }\r\n\r\n}\r\n\r\n.scroll-table {\r\n  max-height: 270px;\r\n  overflow-y: scroll;\r\n}\r\n\r\n.bg-trans>* {\r\n  background-color: transparent;\r\n}\r\n\r\n/* Styling for Tiptap editor */\r\n.jodit-container {\r\n  background: white !important;\r\n  /* color: black !important; */\r\n}\r\n\r\n.jodit-container .jodit-wysiwyg {\r\n  background: rgb(139, 124, 124) !important;\r\n  color: black !important;\r\n  min-height: 300px;\r\n  padding: 10px;\r\n  font-size: 16px;\r\n}\r\n\r\n.jodit-container .jodit-toolbar {\r\n  background: #f8f9fa !important;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.cart_button {\r\n  border-radius: 10px !important;\r\n  width: 70%;\r\n}\r\n\r\n.cart_select {\r\n  padding: 0px 20px;\r\n  border-radius: 10px;\r\n  width: 25%;\r\n  background-color: white;\r\n  color: black;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.bb-blue {\r\n  border-bottom: 3px solid #00ADEF4D;\r\n}\r\n\r\n.ws-normal {\r\n  white-space: normal !important;\r\n}\r\n\r\n.txt-blue {\r\n  color: #00adef;\r\n}\r\n\r\n.search-highlight {\r\n  color: #04498C;\r\n  font-weight: 600 !important;\r\n}\r\n\r\n.scroll-hidden {\r\n  overflow-y: auto;\r\n  max-height: 100vh;\r\n  /* or any height you want */\r\n}\r\n\r\n.scroll-hidden::-webkit-scrollbar {\r\n  display: none;\r\n  /* Chrome, Safari */\r\n}\r\n\r\n\r\na,\r\na:hover {\r\n  text-decoration: none;\r\n  transition: all ease-in-out 0.3s;\r\n  color: #00adef;\r\n}\r\n\r\nh1,\r\n.h1 {\r\n  font-size: 3rem;\r\n  font-weight: 800;\r\n}\r\n\r\n@media (max-width: 1199px) {\r\n\r\n  h1,\r\n  .h1 {\r\n    font-size: 2.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h1,\r\n  .h1 {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 390px) {\r\n\r\n  h1,\r\n  .h1 {\r\n    font-size: 1.3rem;\r\n  }\r\n}\r\n\r\nh2,\r\n.h2 {\r\n  font-size: 5rem;\r\n  font-weight: 800;\r\n}\r\n\r\n@media (max-width: 1269px) {\r\n\r\n  h2,\r\n  .h2 {\r\n    font-size: 3rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h2,\r\n  .h2 {\r\n    font-size: 1.363rem;\r\n  }\r\n}\r\n\r\nh3,\r\n.h3 {\r\n  font-size: 2.8rem;\r\n  font-weight: 800;\r\n}\r\n\r\n@media (max-width: 1199px) {\r\n\r\n  h3,\r\n  .h3 {\r\n    font-size: 1.688rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h3,\r\n  .h3 {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\nh4,\r\n.h4 {\r\n  font-size: 1.65rem;\r\n  line-height: 35px;\r\n  font-weight: 600;\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h4,\r\n  .h4 {\r\n    font-size: 1.15rem;\r\n    line-height: 25px;\r\n  }\r\n}\r\n\r\nh5,\r\n.h5 {\r\n  font-size: 1.25rem;\r\n  line-height: 30px;\r\n  font-weight: 600;\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h5,\r\n  .h5 {\r\n    font-size: 1rem;\r\n    line-height: 25px;\r\n  }\r\n}\r\n\r\nh6,\r\n.h6 {\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\r\n  h6,\r\n  .h6 {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\np {\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  p {\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n\r\n.content-center {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.custom_checkbox {\r\n  margin-bottom: 1.25rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n\r\n  &_input {\r\n    height: 20px !important;\r\n    width: 20px !important;\r\n    margin-top: 0 !important;\r\n    background-color: transparent !important;\r\n    border: 1px solid #00ADEF !important;\r\n\r\n    &:focus {\r\n      box-shadow: none !important;\r\n    }\r\n\r\n    &:checked {\r\n      background-color: #00ADEF !important;\r\n      border: 1px solid #00ADEF !important;\r\n    }\r\n  }\r\n\r\n  &_label {\r\n    color: #fff;\r\n    font-size: 15px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n  }\r\n\r\n}\r\n\r\n.switch {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 50px;\r\n  height: 28px;\r\n}\r\n\r\n.switch input {\r\n  opacity: 0;\r\n  width: 0;\r\n  height: 0;\r\n}\r\n\r\n.switch-label {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n\r\n  @media (width<=550px) {\r\n    font-size: 16px;\r\n  }\r\n\r\n  @media (width<=350px) {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.slider {\r\n  position: absolute;\r\n  cursor: pointer;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  transition: 0.4s;\r\n  border-radius: 34px;\r\n}\r\n\r\n.slider:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 22px;\r\n  width: 22px;\r\n  left: 3px;\r\n  bottom: 3px;\r\n  background-color: #9c9a9f;\r\n  transition: 0.4s;\r\n  border-radius: 50%;\r\n}\r\n\r\ninput:checked+.slider {\r\n  background-color: #0099d1;\r\n}\r\n\r\ninput:checked+.slider:before {\r\n  transform: translateX(22px);\r\n  background-color: white;\r\n}\r\n\r\n.error-message {\r\n  color: #ff696a !important;\r\n  font-size: 16px;\r\n  display: block;\r\n  font-weight: 700;\r\n}\r\n\r\n.success-message {\r\n  color: #32CD33 !important;\r\n  font-size: 16px;\r\n  display: block;\r\n  font-weight: 700;\r\n}"], "names": [], "mappings": "AAEA;;;;;;;AAOA;;;;AAMA;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;AAaA;;;;AAGE;;;;AAKF;;;;AAME;;;;AAKF;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AA0BE;EATF;;;;;AAaE;EAbF;;;;;AAiBE;EAjBF;;;;;AA2BE;EALF;;;;;AASE;EATF;;;;;AAmBE;EALF;;;;;AASE;EATF;;;;;AAoBE;EANF;;;;;;AAkBE;EANF;;;;;;AAiBE;EALF;;;;;AAcE;EAJF;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAGE;EAHF;;;;;AAQA;;;;AAGE;EAHF;;;;;AAOE;EAPF;;;;;AAoBA;;;;AAIA;;;;AAGE;EAHF;;;;;AAQA;;;;AAGE;EAHF;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAGE;EAHF;;;;;AAQA;;;;AAGE;EAHF;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAII;;;;AAMJ;;;;AAII;;;;AAMJ;;;;AAII;;;;AAQA;;;;AAMJ;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAII;;;;AAMJ;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAMM;;;;AAMJ;;;;AAMF;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAUI;;;;AAKF;;;;AAMF;;;;AAUI;;;;AAKF;;;;AAMF;;;;;AAYM;;;;AAMJ;;;;AAMF;;;;;;AAYA;;;;;;AAYA;;;;AAIA;;;;AAYI;;;;AAOF;;;;;;AAMA;;;;AAIA;;;;;AAMF;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;;AAgBA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AASA;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAeA;;;;;;;;AAOE;;;;AAKI;;;;;;;;;;;;;AAYE;EAZF;;;;;AAiBE;;;;;AASR;;;;;AAKI;;;;;;;;;;;;AAWE;EAXF;;;;;AAeE;EAfF;;;;;AAmBE;;;;;;;;AAQA;;;;;;;;;AASE;EATF;;;;;AAaE;;;;AAKF;;;;;AAKE;;;;AAUJ;;;;AAGE;;;;;;;;;;;;;;;;;AAgBE;EAhBF;;;;;;AAqBE;;;;;;;;;;;AAUE;;;;;;AAOA;;;;;;;;;;AA0BF;EAGA;;;;;;AACE;;;;AAKJ;EAGE;;;;;AAHF;EAOE;;;;;AAPF;;;;;;;;;;;;;;AAqBI;;;;AAKJ;EAGE;;;;;AAHF;EAOE;;;;;AAPF;;;;;;;;;;;;;;AAqBI;;;;;;;;;;AAsBJ;EAEA;;;;;AATF;EAaE;;;;;;AAEI;;;;;;;AASJ;;;;;;;AAME;;;;;AAIE;EAIF;;;;;AAXF;;;;AAuBE;;;;;AAII;;;;;;;;;;;;;AAkBV;EAGA;;;;;;AACE;;;;;AAQA;;;;;;AAGF;;;;;AAKE;;;;AAKA;;;;;;;AAMA;;;;AAKA;;;;AAIJ;;;;;;;;AASE;;;;AAIA;;;;;;;AAQA;;;;;AAKA;;;;AAKA;;;;;;AAMA;;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAKA;;;;;;;;;AASA;;;;AAKA;;;;;;;;;AAQA;;;;;AAKA;;;;;AAOA;;;;AAIA;;;;;;;;;;AASE;;;;;;AAMF;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAWA;;;;;;AAIE;;;;;AAIE;;;;;;;AASJ;;;;;;;;AAjoCE;;;;;;;;AASA;;;;;;;;AAOA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;AA9DF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AApGF;;;;;AAAA;;;;AAAA;;;;;;;;;;;AAAA;;;;;AAAA;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;;;;AAAA;;;;AAAA;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;;;;;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;AAAA;;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;AAAA;EAAA;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;;;AAAA;;;;;;AAAA;;;;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;;;;;;;AAAA;;;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;AAAA;;;;AAAA;EAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;;AAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;;AAAA;EAAA;;;;;;AAAA;;;;;;AAAA;EAAA;;;;;;AAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;;AAAA;;;;;;;AAAA;;;;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;AAAA;;;;;;;AAAA;EAAA;;;;;AAAA;EAAA;;;;;AAAA;;;;;;;;;;;;AAAA;;;;;;;;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;EAAA;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;;EAAA;;;;;;AAAA;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;EAAA;;;;;;AAAA;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;;EAAA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA"}}]}