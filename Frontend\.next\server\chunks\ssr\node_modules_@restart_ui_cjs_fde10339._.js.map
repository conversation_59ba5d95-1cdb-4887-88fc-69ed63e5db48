{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/utils.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.getChildRef = getChildRef;\nexports.getReactVersion = getReactVersion;\nexports.isEscKey = isEscKey;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction isEscKey(e) {\n  return e.code === 'Escape' || e.keyCode === 27;\n}\nfunction getReactVersion() {\n  const parts = React.version.split('.');\n  return {\n    major: +parts[0],\n    minor: +parts[1],\n    patch: +parts[2]\n  };\n}\nfunction getChildRef(element) {\n  if (!element || typeof element === 'function') {\n    return null;\n  }\n  const {\n    major\n  } = getReactVersion();\n  const childRef = major >= 19 ? element.props.ref : element.ref;\n  return childRef;\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,WAAW,GAAG;AACtB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,QAAQ,GAAG;AACnB,IAAI,QAAQ;AACZ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,SAAS,CAAC;IACjB,OAAO,EAAE,IAAI,KAAK,YAAY,EAAE,OAAO,KAAK;AAC9C;AACA,SAAS;IACP,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;IAClC,OAAO;QACL,OAAO,CAAC,KAAK,CAAC,EAAE;QAChB,OAAO,CAAC,KAAK,CAAC,EAAE;QAChB,OAAO,CAAC,KAAK,CAAC,EAAE;IAClB;AACF;AACA,SAAS,YAAY,OAAO;IAC1B,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY;QAC7C,OAAO;IACT;IACA,MAAM,EACJ,KAAK,EACN,GAAG;IACJ,MAAM,WAAW,SAAS,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,QAAQ,GAAG;IAC9D,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/SelectableContext.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.makeEventKey = exports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nconst SelectableContext = /*#__PURE__*/React.createContext(null);\nconst makeEventKey = (eventKey, href = null) => {\n  if (eventKey != null) return String(eventKey);\n  return href || null;\n};\nexports.makeEventKey = makeEventKey;\nvar _default = exports.default = SelectableContext;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,YAAY,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC9C,IAAI,QAAQ;AACZ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,MAAM,oBAAoB,WAAW,GAAE,MAAM,aAAa,CAAC;AAC3D,MAAM,eAAe,CAAC,UAAU,OAAO,IAAI;IACzC,IAAI,YAAY,MAAM,OAAO,OAAO;IACpC,OAAO,QAAQ;AACjB;AACA,QAAQ,YAAY,GAAG;AACvB,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/DataKey.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.PROPERTY_PREFIX = exports.ATTRIBUTE_PREFIX = void 0;\nexports.dataAttr = dataAttr;\nexports.dataProp = dataProp;\nconst ATTRIBUTE_PREFIX = exports.ATTRIBUTE_PREFIX = `data-rr-ui-`;\nconst PROPERTY_PREFIX = exports.PROPERTY_PREFIX = `rrUi`;\nfunction dataAttr(property) {\n  return `${ATTRIBUTE_PREFIX}${property}`;\n}\nfunction dataProp(property) {\n  return `${PROPERTY_PREFIX}${property}`;\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,eAAe,GAAG,QAAQ,gBAAgB,GAAG,KAAK;AAC1D,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,MAAM,mBAAmB,QAAQ,gBAAgB,GAAG,CAAC,WAAW,CAAC;AACjE,MAAM,kBAAkB,QAAQ,eAAe,GAAG,CAAC,IAAI,CAAC;AACxD,SAAS,SAAS,QAAQ;IACxB,OAAO,GAAG,mBAAmB,UAAU;AACzC;AACA,SAAS,SAAS,QAAQ;IACxB,OAAO,GAAG,kBAAkB,UAAU;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/getScrollbarWidth.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = getBodyScrollbarWidth;\n/**\n * Get the width of the vertical window scrollbar if it's visible\n */\nfunction getBodyScrollbarWidth(ownerDocument = document) {\n  const window = ownerDocument.defaultView;\n  return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG;AAClB;;CAEC,GACD,SAAS,sBAAsB,gBAAgB,QAAQ;IACrD,MAAM,SAAS,cAAc,WAAW;IACxC,OAAO,KAAK,GAAG,CAAC,OAAO,UAAU,GAAG,cAAc,eAAe,CAAC,WAAW;AAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/ModalManager.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = exports.OPEN_DATA_ATTRIBUTE = void 0;\nvar _css = _interopRequireDefault(require(\"dom-helpers/css\"));\nvar _DataKey = require(\"./DataKey\");\nvar _getScrollbarWidth = _interopRequireDefault(require(\"./getScrollbarWidth\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nconst OPEN_DATA_ATTRIBUTE = exports.OPEN_DATA_ATTRIBUTE = (0, _DataKey.dataAttr)('modal-open');\n\n/**\n * Manages a stack of Modals as well as ensuring\n * body scrolling is is disabled and padding accounted for\n */\nclass ModalManager {\n  constructor({\n    ownerDocument,\n    handleContainerOverflow = true,\n    isRTL = false\n  } = {}) {\n    this.handleContainerOverflow = handleContainerOverflow;\n    this.isRTL = isRTL;\n    this.modals = [];\n    this.ownerDocument = ownerDocument;\n  }\n  getScrollbarWidth() {\n    return (0, _getScrollbarWidth.default)(this.ownerDocument);\n  }\n  getElement() {\n    return (this.ownerDocument || document).body;\n  }\n  setModalAttributes(_modal) {\n    // For overriding\n  }\n  removeModalAttributes(_modal) {\n    // For overriding\n  }\n  setContainerStyle(containerState) {\n    const style = {\n      overflow: 'hidden'\n    };\n\n    // we are only interested in the actual `style` here\n    // because we will override it\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const container = this.getElement();\n    containerState.style = {\n      overflow: container.style.overflow,\n      [paddingProp]: container.style[paddingProp]\n    };\n    if (containerState.scrollBarWidth) {\n      // use computed style, here to get the real padding\n      // to add our scrollbar width\n      style[paddingProp] = `${parseInt((0, _css.default)(container, paddingProp) || '0', 10) + containerState.scrollBarWidth}px`;\n    }\n    container.setAttribute(OPEN_DATA_ATTRIBUTE, '');\n    (0, _css.default)(container, style);\n  }\n  reset() {\n    [...this.modals].forEach(m => this.remove(m));\n  }\n  removeContainerStyle(containerState) {\n    const container = this.getElement();\n    container.removeAttribute(OPEN_DATA_ATTRIBUTE);\n    Object.assign(container.style, containerState.style);\n  }\n  add(modal) {\n    let modalIdx = this.modals.indexOf(modal);\n    if (modalIdx !== -1) {\n      return modalIdx;\n    }\n    modalIdx = this.modals.length;\n    this.modals.push(modal);\n    this.setModalAttributes(modal);\n    if (modalIdx !== 0) {\n      return modalIdx;\n    }\n    this.state = {\n      scrollBarWidth: this.getScrollbarWidth(),\n      style: {}\n    };\n    if (this.handleContainerOverflow) {\n      this.setContainerStyle(this.state);\n    }\n    return modalIdx;\n  }\n  remove(modal) {\n    const modalIdx = this.modals.indexOf(modal);\n    if (modalIdx === -1) {\n      return;\n    }\n    this.modals.splice(modalIdx, 1);\n\n    // if that was the last modal in a container,\n    // clean up the container\n    if (!this.modals.length && this.handleContainerOverflow) {\n      this.removeContainerStyle(this.state);\n    }\n    this.removeModalAttributes(modal);\n  }\n  isTopModal(modal) {\n    return !!this.modals.length && this.modals[this.modals.length - 1] === modal;\n  }\n}\nvar _default = exports.default = ModalManager;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,QAAQ,mBAAmB,GAAG,KAAK;AACrD,IAAI,OAAO;AACX,IAAI;AACJ,IAAI,qBAAqB;AACzB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,MAAM,sBAAsB,QAAQ,mBAAmB,GAAG,CAAC,GAAG,SAAS,QAAQ,EAAE;AAEjF;;;CAGC,GACD,MAAM;IACJ,YAAY,EACV,aAAa,EACb,0BAA0B,IAAI,EAC9B,QAAQ,KAAK,EACd,GAAG,CAAC,CAAC,CAAE;QACN,IAAI,CAAC,uBAAuB,GAAG;QAC/B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,aAAa,GAAG;IACvB;IACA,oBAAoB;QAClB,OAAO,CAAC,GAAG,mBAAmB,OAAO,EAAE,IAAI,CAAC,aAAa;IAC3D;IACA,aAAa;QACX,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,QAAQ,EAAE,IAAI;IAC9C;IACA,mBAAmB,MAAM,EAAE;IACzB,iBAAiB;IACnB;IACA,sBAAsB,MAAM,EAAE;IAC5B,iBAAiB;IACnB;IACA,kBAAkB,cAAc,EAAE;QAChC,MAAM,QAAQ;YACZ,UAAU;QACZ;QAEA,oDAAoD;QACpD,8BAA8B;QAC9B,MAAM,cAAc,IAAI,CAAC,KAAK,GAAG,gBAAgB;QACjD,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,eAAe,KAAK,GAAG;YACrB,UAAU,UAAU,KAAK,CAAC,QAAQ;YAClC,CAAC,YAAY,EAAE,UAAU,KAAK,CAAC,YAAY;QAC7C;QACA,IAAI,eAAe,cAAc,EAAE;YACjC,mDAAmD;YACnD,6BAA6B;YAC7B,KAAK,CAAC,YAAY,GAAG,GAAG,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,WAAW,gBAAgB,KAAK,MAAM,eAAe,cAAc,CAAC,EAAE,CAAC;QAC5H;QACA,UAAU,YAAY,CAAC,qBAAqB;QAC5C,CAAC,GAAG,KAAK,OAAO,EAAE,WAAW;IAC/B;IACA,QAAQ;QACN;eAAI,IAAI,CAAC,MAAM;SAAC,CAAC,OAAO,CAAC,CAAA,IAAK,IAAI,CAAC,MAAM,CAAC;IAC5C;IACA,qBAAqB,cAAc,EAAE;QACnC,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,UAAU,eAAe,CAAC;QAC1B,OAAO,MAAM,CAAC,UAAU,KAAK,EAAE,eAAe,KAAK;IACrD;IACA,IAAI,KAAK,EAAE;QACT,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnC,IAAI,aAAa,CAAC,GAAG;YACnB,OAAO;QACT;QACA,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,aAAa,GAAG;YAClB,OAAO;QACT;QACA,IAAI,CAAC,KAAK,GAAG;YACX,gBAAgB,IAAI,CAAC,iBAAiB;YACtC,OAAO,CAAC;QACV;QACA,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK;QACnC;QACA,OAAO;IACT;IACA,OAAO,KAAK,EAAE;QACZ,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACrC,IAAI,aAAa,CAAC,GAAG;YACnB;QACF;QACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU;QAE7B,6CAA6C;QAC7C,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACvD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK;QACtC;QACA,IAAI,CAAC,qBAAqB,CAAC;IAC7B;IACA,WAAW,KAAK,EAAE;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK;IACzE;AACF;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/useWindow.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.WindowProvider = void 0;\nexports.default = useWindow;\nvar _react = require(\"react\");\nvar _canUseDOM = _interopRequireDefault(require(\"dom-helpers/canUseDOM\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nconst Context = /*#__PURE__*/(0, _react.createContext)(_canUseDOM.default ? window : undefined);\nconst WindowProvider = exports.WindowProvider = Context.Provider;\n\n/**\n * The document \"window\" placed in React context. Helpful for determining\n * SSR context, or when rendering into an iframe.\n *\n * @returns the current window\n */\nfunction useWindow() {\n  return (0, _react.useContext)(Context);\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,cAAc,GAAG,KAAK;AAC9B,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI,aAAa;AACjB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,MAAM,UAAU,WAAW,GAAE,CAAC,GAAG,OAAO,aAAa,EAAE,WAAW,OAAO,GAAG,SAAS;AACrF,MAAM,iBAAiB,QAAQ,cAAc,GAAG,QAAQ,QAAQ;AAEhE;;;;;CAKC,GACD,SAAS;IACP,OAAO,CAAC,GAAG,OAAO,UAAU,EAAE;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/useWaitForDOMRef.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = useWaitForDOMRef;\nexports.resolveContainerRef = void 0;\nvar _ownerDocument = _interopRequireDefault(require(\"dom-helpers/ownerDocument\"));\nvar _canUseDOM = _interopRequireDefault(require(\"dom-helpers/canUseDOM\"));\nvar _react = require(\"react\");\nvar _useWindow = _interopRequireDefault(require(\"./useWindow\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nconst resolveContainerRef = (ref, document) => {\n  if (!_canUseDOM.default) return null;\n  if (ref == null) return (document || (0, _ownerDocument.default)()).body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if (ref && ('nodeType' in ref || ref.getBoundingClientRect)) return ref;\n  return null;\n};\nexports.resolveContainerRef = resolveContainerRef;\nfunction useWaitForDOMRef(ref, onResolved) {\n  const window = (0, _useWindow.default)();\n  const [resolvedRef, setRef] = (0, _react.useState)(() => resolveContainerRef(ref, window == null ? void 0 : window.document));\n  if (!resolvedRef) {\n    const earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n  (0, _react.useEffect)(() => {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  (0, _react.useEffect)(() => {\n    const nextRef = resolveContainerRef(ref);\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG;AAClB,QAAQ,mBAAmB,GAAG,KAAK;AACnC,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI,aAAa;AACjB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,MAAM,sBAAsB,CAAC,KAAK;IAChC,IAAI,CAAC,WAAW,OAAO,EAAE,OAAO;IAChC,IAAI,OAAO,MAAM,OAAO,CAAC,YAAY,CAAC,GAAG,eAAe,OAAO,GAAG,EAAE,IAAI;IACxE,IAAI,OAAO,QAAQ,YAAY,MAAM;IACrC,IAAI,OAAO,aAAa,KAAK,MAAM,IAAI,OAAO;IAC9C,IAAI,OAAO,CAAC,cAAc,OAAO,IAAI,qBAAqB,GAAG,OAAO;IACpE,OAAO;AACT;AACA,QAAQ,mBAAmB,GAAG;AAC9B,SAAS,iBAAiB,GAAG,EAAE,UAAU;IACvC,MAAM,SAAS,CAAC,GAAG,WAAW,OAAO;IACrC,MAAM,CAAC,aAAa,OAAO,GAAG,CAAC,GAAG,OAAO,QAAQ,EAAE,IAAM,oBAAoB,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,QAAQ;IAC3H,IAAI,CAAC,aAAa;QAChB,MAAM,WAAW,oBAAoB;QACrC,IAAI,UAAU,OAAO;IACvB;IACA,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,cAAc,aAAa;YAC7B,WAAW;QACb;IACF,GAAG;QAAC;QAAY;KAAY;IAC5B,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,MAAM,UAAU,oBAAoB;QACpC,IAAI,YAAY,aAAa;YAC3B,OAAO;QACT;IACF,GAAG;QAAC;QAAK;KAAY;IACrB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/NoopTransition.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _useMergedRefs = _interopRequireDefault(require(\"@restart/hooks/useMergedRefs\"));\nvar _react = require(\"react\");\nvar _utils = require(\"./utils\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction NoopTransition({\n  children,\n  in: inProp,\n  onExited,\n  mountOnEnter,\n  unmountOnExit\n}) {\n  const ref = (0, _react.useRef)(null);\n  const hasEnteredRef = (0, _react.useRef)(inProp);\n  const handleExited = (0, _useEventCallback.default)(onExited);\n  (0, _react.useEffect)(() => {\n    if (inProp) hasEnteredRef.current = true;else {\n      handleExited(ref.current);\n    }\n  }, [inProp, handleExited]);\n  const combinedRef = (0, _useMergedRefs.default)(ref, (0, _utils.getChildRef)(children));\n  const child = /*#__PURE__*/(0, _react.cloneElement)(children, {\n    ref: combinedRef\n  });\n  if (inProp) return child;\n  if (unmountOnExit) {\n    return null;\n  }\n  if (!hasEnteredRef.current && mountOnEnter) {\n    return null;\n  }\n  return child;\n}\nvar _default = exports.default = NoopTransition;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,oBAAoB;AACxB,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI;AACJ,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,eAAe,EACtB,QAAQ,EACR,IAAI,MAAM,EACV,QAAQ,EACR,YAAY,EACZ,aAAa,EACd;IACC,MAAM,MAAM,CAAC,GAAG,OAAO,MAAM,EAAE;IAC/B,MAAM,gBAAgB,CAAC,GAAG,OAAO,MAAM,EAAE;IACzC,MAAM,eAAe,CAAC,GAAG,kBAAkB,OAAO,EAAE;IACpD,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,QAAQ,cAAc,OAAO,GAAG;aAAU;YAC5C,aAAa,IAAI,OAAO;QAC1B;IACF,GAAG;QAAC;QAAQ;KAAa;IACzB,MAAM,cAAc,CAAC,GAAG,eAAe,OAAO,EAAE,KAAK,CAAC,GAAG,OAAO,WAAW,EAAE;IAC7E,MAAM,QAAQ,WAAW,GAAE,CAAC,GAAG,OAAO,YAAY,EAAE,UAAU;QAC5D,KAAK;IACP;IACA,IAAI,QAAQ,OAAO;IACnB,IAAI,eAAe;QACjB,OAAO;IACT;IACA,IAAI,CAAC,cAAc,OAAO,IAAI,cAAc;QAC1C,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/useRTGTransitionProps.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = useRTGTransitionProps;\nvar _react = require(\"react\");\nvar _useMergedRefs = _interopRequireDefault(require(\"@restart/hooks/useMergedRefs\"));\nvar _utils = require(\"./utils\");\nconst _excluded = [\"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"addEndListener\", \"children\"];\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\n/**\n * Normalizes RTG transition callbacks with nodeRef to better support\n * strict mode.\n *\n * @param props Transition props.\n * @returns Normalized transition props.\n */\nfunction useRTGTransitionProps(_ref) {\n  let {\n      onEnter,\n      onEntering,\n      onEntered,\n      onExit,\n      onExiting,\n      onExited,\n      addEndListener,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const nodeRef = (0, _react.useRef)(null);\n  const mergedRef = (0, _useMergedRefs.default)(nodeRef, (0, _utils.getChildRef)(children));\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = (0, _react.useCallback)(normalize(onEnter), [onEnter]);\n  const handleEntering = (0, _react.useCallback)(normalize(onEntering), [onEntering]);\n  const handleEntered = (0, _react.useCallback)(normalize(onEntered), [onEntered]);\n  const handleExit = (0, _react.useCallback)(normalize(onExit), [onExit]);\n  const handleExiting = (0, _react.useCallback)(normalize(onExiting), [onExiting]);\n  const handleExited = (0, _react.useCallback)(normalize(onExited), [onExited]);\n  const handleAddEndListener = (0, _react.useCallback)(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return Object.assign({}, props, {\n    nodeRef\n  }, onEnter && {\n    onEnter: handleEnter\n  }, onEntering && {\n    onEntering: handleEntering\n  }, onEntered && {\n    onEntered: handleEntered\n  }, onExit && {\n    onExit: handleExit\n  }, onExiting && {\n    onExiting: handleExiting\n  }, onExited && {\n    onExited: handleExited\n  }, addEndListener && {\n    addEndListener: handleAddEndListener\n  }, {\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, Object.assign({}, innerProps, {\n      ref: mergedRef\n    })) : /*#__PURE__*/(0, _react.cloneElement)(children, {\n      ref: mergedRef\n    })\n  });\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG;AAClB,IAAI;AACJ,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,YAAY;IAAC;IAAW;IAAc;IAAa;IAAU;IAAa;IAAY;IAAkB;CAAW;AACzH,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM;;;;;;CAMC,GACD,SAAS,sBAAsB,IAAI;IACjC,IAAI,EACA,OAAO,EACP,UAAU,EACV,SAAS,EACT,MAAM,EACN,SAAS,EACT,QAAQ,EACR,cAAc,EACd,QAAQ,EACT,GAAG,MACJ,QAAQ,8BAA8B,MAAM;IAC9C,MAAM,UAAU,CAAC,GAAG,OAAO,MAAM,EAAE;IACnC,MAAM,YAAY,CAAC,GAAG,eAAe,OAAO,EAAE,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE;IAC/E,MAAM,YAAY,CAAA,WAAY,CAAA;YAC5B,IAAI,YAAY,QAAQ,OAAO,EAAE;gBAC/B,SAAS,QAAQ,OAAO,EAAE;YAC5B;QACF;IAEA,8CAA8C,GAC9C,MAAM,cAAc,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,UAAU;QAAC;KAAQ;IACzE,MAAM,iBAAiB,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,aAAa;QAAC;KAAW;IAClF,MAAM,gBAAgB,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,YAAY;QAAC;KAAU;IAC/E,MAAM,aAAa,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,SAAS;QAAC;KAAO;IACtE,MAAM,gBAAgB,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,YAAY;QAAC;KAAU;IAC/E,MAAM,eAAe,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,WAAW;QAAC;KAAS;IAC5E,MAAM,uBAAuB,CAAC,GAAG,OAAO,WAAW,EAAE,UAAU,iBAAiB;QAAC;KAAe;IAChG,6CAA6C,GAE7C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QAC9B;IACF,GAAG,WAAW;QACZ,SAAS;IACX,GAAG,cAAc;QACf,YAAY;IACd,GAAG,aAAa;QACd,WAAW;IACb,GAAG,UAAU;QACX,QAAQ;IACV,GAAG,aAAa;QACd,WAAW;IACb,GAAG,YAAY;QACb,UAAU;IACZ,GAAG,kBAAkB;QACnB,gBAAgB;IAClB,GAAG;QACD,UAAU,OAAO,aAAa,aAAa,CAAC,QAAQ,aACpD,2DAA2D;YAC3D,SAAS,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;gBAC7C,KAAK;YACP,MAAM,WAAW,GAAE,CAAC,GAAG,OAAO,YAAY,EAAE,UAAU;YACpD,KAAK;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/RTGTransition.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useRTGTransitionProps = _interopRequireDefault(require(\"./useRTGTransitionProps\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"component\"];\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\n// Normalizes Transition callbacks when nodeRef is used.\nconst RTGTransition = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      component: Component\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const transitionProps = (0, _useRTGTransitionProps.default)(props);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, Object.assign({\n    ref: ref\n  }, transitionProps));\n});\nvar _default = exports.default = RTGTransition;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,QAAQ;AACZ,IAAI,yBAAyB;AAC7B,IAAI;AACJ,MAAM,YAAY;IAAC;CAAY;AAC/B,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,wDAAwD;AACxD,MAAM,gBAAgB,WAAW,GAAE,MAAM,UAAU,CAAC,CAAC,MAAM;IACzD,IAAI,EACA,WAAW,SAAS,EACrB,GAAG,MACJ,QAAQ,8BAA8B,MAAM;IAC9C,MAAM,kBAAkB,CAAC,GAAG,uBAAuB,OAAO,EAAE;IAC5D,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,WAAW,OAAO,MAAM,CAAC;QAChE,KAAK;IACP,GAAG;AACL;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/ImperativeTransition.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = ImperativeTransition;\nexports.renderTransition = renderTransition;\nexports.useTransition = useTransition;\nvar _useMergedRefs = _interopRequireDefault(require(\"@restart/hooks/useMergedRefs\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _useIsomorphicEffect = _interopRequireDefault(require(\"@restart/hooks/useIsomorphicEffect\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _NoopTransition = _interopRequireDefault(require(\"./NoopTransition\"));\nvar _RTGTransition = _interopRequireDefault(require(\"./RTGTransition\"));\nvar _utils = require(\"./utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction useTransition({\n  in: inProp,\n  onTransition\n}) {\n  const ref = (0, _react.useRef)(null);\n  const isInitialRef = (0, _react.useRef)(true);\n  const handleTransition = (0, _useEventCallback.default)(onTransition);\n  (0, _useIsomorphicEffect.default)(() => {\n    if (!ref.current) {\n      return undefined;\n    }\n    let stale = false;\n    handleTransition({\n      in: inProp,\n      element: ref.current,\n      initial: isInitialRef.current,\n      isStale: () => stale\n    });\n    return () => {\n      stale = true;\n    };\n  }, [inProp, handleTransition]);\n  (0, _useIsomorphicEffect.default)(() => {\n    isInitialRef.current = false;\n    // this is for strict mode\n    return () => {\n      isInitialRef.current = true;\n    };\n  }, []);\n  return ref;\n}\n/**\n * Adapts an imperative transition function to a subset of the RTG `<Transition>` component API.\n *\n * ImperativeTransition does not support mounting options or `appear` at the moment, meaning\n * that it always acts like: `mountOnEnter={true} unmountOnExit={true} appear={true}`\n */\nfunction ImperativeTransition({\n  children,\n  in: inProp,\n  onExited,\n  onEntered,\n  transition\n}) {\n  const [exited, setExited] = (0, _react.useState)(!inProp);\n\n  // TODO: I think this needs to be in an effect\n  if (inProp && exited) {\n    setExited(false);\n  }\n  const ref = useTransition({\n    in: !!inProp,\n    onTransition: options => {\n      const onFinish = () => {\n        if (options.isStale()) return;\n        if (options.in) {\n          onEntered == null ? void 0 : onEntered(options.element, options.initial);\n        } else {\n          setExited(true);\n          onExited == null ? void 0 : onExited(options.element);\n        }\n      };\n      Promise.resolve(transition(options)).then(onFinish, error => {\n        if (!options.in) setExited(true);\n        throw error;\n      });\n    }\n  });\n  const combinedRef = (0, _useMergedRefs.default)(ref, (0, _utils.getChildRef)(children));\n  return exited && !inProp ? null : /*#__PURE__*/(0, _react.cloneElement)(children, {\n    ref: combinedRef\n  });\n}\nfunction renderTransition(component, runTransition, props) {\n  if (component) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_RTGTransition.default, Object.assign({}, props, {\n      component: component\n    }));\n  }\n  if (runTransition) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(ImperativeTransition, Object.assign({}, props, {\n      transition: runTransition\n    }));\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NoopTransition.default, Object.assign({}, props));\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG;AAClB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,aAAa,GAAG;AACxB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB;AACxB,IAAI,uBAAuB;AAC3B,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI;AACJ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,cAAc,EACrB,IAAI,MAAM,EACV,YAAY,EACb;IACC,MAAM,MAAM,CAAC,GAAG,OAAO,MAAM,EAAE;IAC/B,MAAM,eAAe,CAAC,GAAG,OAAO,MAAM,EAAE;IACxC,MAAM,mBAAmB,CAAC,GAAG,kBAAkB,OAAO,EAAE;IACxD,CAAC,GAAG,qBAAqB,OAAO,EAAE;QAChC,IAAI,CAAC,IAAI,OAAO,EAAE;YAChB,OAAO;QACT;QACA,IAAI,QAAQ;QACZ,iBAAiB;YACf,IAAI;YACJ,SAAS,IAAI,OAAO;YACpB,SAAS,aAAa,OAAO;YAC7B,SAAS,IAAM;QACjB;QACA,OAAO;YACL,QAAQ;QACV;IACF,GAAG;QAAC;QAAQ;KAAiB;IAC7B,CAAC,GAAG,qBAAqB,OAAO,EAAE;QAChC,aAAa,OAAO,GAAG;QACvB,0BAA0B;QAC1B,OAAO;YACL,aAAa,OAAO,GAAG;QACzB;IACF,GAAG,EAAE;IACL,OAAO;AACT;AACA;;;;;CAKC,GACD,SAAS,qBAAqB,EAC5B,QAAQ,EACR,IAAI,MAAM,EACV,QAAQ,EACR,SAAS,EACT,UAAU,EACX;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAC,GAAG,OAAO,QAAQ,EAAE,CAAC;IAElD,8CAA8C;IAC9C,IAAI,UAAU,QAAQ;QACpB,UAAU;IACZ;IACA,MAAM,MAAM,cAAc;QACxB,IAAI,CAAC,CAAC;QACN,cAAc,CAAA;YACZ,MAAM,WAAW;gBACf,IAAI,QAAQ,OAAO,IAAI;gBACvB,IAAI,QAAQ,EAAE,EAAE;oBACd,aAAa,OAAO,KAAK,IAAI,UAAU,QAAQ,OAAO,EAAE,QAAQ,OAAO;gBACzE,OAAO;oBACL,UAAU;oBACV,YAAY,OAAO,KAAK,IAAI,SAAS,QAAQ,OAAO;gBACtD;YACF;YACA,QAAQ,OAAO,CAAC,WAAW,UAAU,IAAI,CAAC,UAAU,CAAA;gBAClD,IAAI,CAAC,QAAQ,EAAE,EAAE,UAAU;gBAC3B,MAAM;YACR;QACF;IACF;IACA,MAAM,cAAc,CAAC,GAAG,eAAe,OAAO,EAAE,KAAK,CAAC,GAAG,OAAO,WAAW,EAAE;IAC7E,OAAO,UAAU,CAAC,SAAS,OAAO,WAAW,GAAE,CAAC,GAAG,OAAO,YAAY,EAAE,UAAU;QAChF,KAAK;IACP;AACF;AACA,SAAS,iBAAiB,SAAS,EAAE,aAAa,EAAE,KAAK;IACvD,IAAI,WAAW;QACb,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,eAAe,OAAO,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACxF,WAAW;QACb;IACF;IACA,IAAI,eAAe;QACjB,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,sBAAsB,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACtF,YAAY;QACd;IACF;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,gBAAgB,OAAO,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/Modal.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _activeElement = _interopRequireDefault(require(\"dom-helpers/activeElement\"));\nvar _contains = _interopRequireDefault(require(\"dom-helpers/contains\"));\nvar _canUseDOM = _interopRequireDefault(require(\"dom-helpers/canUseDOM\"));\nvar _listen = _interopRequireDefault(require(\"dom-helpers/listen\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _useMounted = _interopRequireDefault(require(\"@restart/hooks/useMounted\"));\nvar _useWillUnmount = _interopRequireDefault(require(\"@restart/hooks/useWillUnmount\"));\nvar _usePrevious = _interopRequireDefault(require(\"@restart/hooks/usePrevious\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _ModalManager = _interopRequireDefault(require(\"./ModalManager\"));\nvar _useWaitForDOMRef = _interopRequireDefault(require(\"./useWaitForDOMRef\"));\nvar _useWindow = _interopRequireDefault(require(\"./useWindow\"));\nvar _ImperativeTransition = require(\"./ImperativeTransition\");\nvar _utils = require(\"./utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"show\", \"role\", \"className\", \"style\", \"children\", \"backdrop\", \"keyboard\", \"onBackdropClick\", \"onEscapeKeyDown\", \"transition\", \"runTransition\", \"backdropTransition\", \"runBackdropTransition\", \"autoFocus\", \"enforceFocus\", \"restoreFocus\", \"restoreFocusOptions\", \"renderDialog\", \"renderBackdrop\", \"manager\", \"container\", \"onShow\", \"onHide\", \"onExit\", \"onExited\", \"onExiting\", \"onEnter\", \"onEntering\", \"onEntered\"];\n/* eslint-disable @typescript-eslint/no-use-before-define, react/prop-types */\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nlet manager;\n\n/*\n  Modal props are split into a version with and without index signature so that you can fully use them in another projects\n  This is due to Typescript not playing well with index signatures e.g. when using Omit\n*/\n\nfunction getManager(window) {\n  if (!manager) manager = new _ModalManager.default({\n    ownerDocument: window == null ? void 0 : window.document\n  });\n  return manager;\n}\nfunction useModalManager(provided) {\n  const window = (0, _useWindow.default)();\n  const modalManager = provided || getManager(window);\n  const modal = (0, _react.useRef)({\n    dialog: null,\n    backdrop: null\n  });\n  return Object.assign(modal.current, {\n    add: () => modalManager.add(modal.current),\n    remove: () => modalManager.remove(modal.current),\n    isTopModal: () => modalManager.isTopModal(modal.current),\n    setDialogRef: (0, _react.useCallback)(ref => {\n      modal.current.dialog = ref;\n    }, []),\n    setBackdropRef: (0, _react.useCallback)(ref => {\n      modal.current.backdrop = ref;\n    }, [])\n  });\n}\nconst Modal = /*#__PURE__*/(0, _react.forwardRef)((_ref, ref) => {\n  let {\n      show = false,\n      role = 'dialog',\n      className,\n      style,\n      children,\n      backdrop = true,\n      keyboard = true,\n      onBackdropClick,\n      onEscapeKeyDown,\n      transition,\n      runTransition,\n      backdropTransition,\n      runBackdropTransition,\n      autoFocus = true,\n      enforceFocus = true,\n      restoreFocus = true,\n      restoreFocusOptions,\n      renderDialog,\n      renderBackdrop = props => /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", Object.assign({}, props)),\n      manager: providedManager,\n      container: containerRef,\n      onShow,\n      onHide = () => {},\n      onExit,\n      onExited,\n      onExiting,\n      onEnter,\n      onEntering,\n      onEntered\n    } = _ref,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const ownerWindow = (0, _useWindow.default)();\n  const container = (0, _useWaitForDOMRef.default)(containerRef);\n  const modal = useModalManager(providedManager);\n  const isMounted = (0, _useMounted.default)();\n  const prevShow = (0, _usePrevious.default)(show);\n  const [exited, setExited] = (0, _react.useState)(!show);\n  const lastFocusRef = (0, _react.useRef)(null);\n  (0, _react.useImperativeHandle)(ref, () => modal, [modal]);\n  if (_canUseDOM.default && !prevShow && show) {\n    lastFocusRef.current = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);\n  }\n\n  // TODO: I think this needs to be in an effect\n  if (show && exited) {\n    setExited(false);\n  }\n  const handleShow = (0, _useEventCallback.default)(() => {\n    modal.add();\n    removeKeydownListenerRef.current = (0, _listen.default)(document, 'keydown', handleDocumentKeyDown);\n    removeFocusListenerRef.current = (0, _listen.default)(document, 'focus',\n    // the timeout is necessary b/c this will run before the new modal is mounted\n    // and so steals focus from it\n    () => setTimeout(handleEnforceFocus), true);\n    if (onShow) {\n      onShow();\n    }\n\n    // autofocus after onShow to not trigger a focus event for previous\n    // modals before this one is shown.\n    if (autoFocus) {\n      var _modal$dialog$ownerDo, _modal$dialog;\n      const currentActiveElement = (0, _activeElement.default)((_modal$dialog$ownerDo = (_modal$dialog = modal.dialog) == null ? void 0 : _modal$dialog.ownerDocument) != null ? _modal$dialog$ownerDo : ownerWindow == null ? void 0 : ownerWindow.document);\n      if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {\n        lastFocusRef.current = currentActiveElement;\n        modal.dialog.focus();\n      }\n    }\n  });\n  const handleHide = (0, _useEventCallback.default)(() => {\n    modal.remove();\n    removeKeydownListenerRef.current == null ? void 0 : removeKeydownListenerRef.current();\n    removeFocusListenerRef.current == null ? void 0 : removeFocusListenerRef.current();\n    if (restoreFocus) {\n      var _lastFocusRef$current;\n      // Support: <=IE11 doesn't support `focus()` on svg elements (RB: #917)\n      (_lastFocusRef$current = lastFocusRef.current) == null ? void 0 : _lastFocusRef$current.focus == null ? void 0 : _lastFocusRef$current.focus(restoreFocusOptions);\n      lastFocusRef.current = null;\n    }\n  });\n\n  // TODO: try and combine these effects: https://github.com/react-bootstrap/react-overlays/pull/794#discussion_r409954120\n\n  // Show logic when:\n  //  - show is `true` _and_ `container` has resolved\n  (0, _react.useEffect)(() => {\n    if (!show || !container) return;\n    handleShow();\n  }, [show, container, /* should never change: */handleShow]);\n\n  // Hide cleanup logic when:\n  //  - `exited` switches to true\n  //  - component unmounts;\n  (0, _react.useEffect)(() => {\n    if (!exited) return;\n    handleHide();\n  }, [exited, handleHide]);\n  (0, _useWillUnmount.default)(() => {\n    handleHide();\n  });\n\n  // --------------------------------\n\n  const handleEnforceFocus = (0, _useEventCallback.default)(() => {\n    if (!enforceFocus || !isMounted() || !modal.isTopModal()) {\n      return;\n    }\n    const currentActiveElement = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);\n    if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {\n      modal.dialog.focus();\n    }\n  });\n  const handleBackdropClick = (0, _useEventCallback.default)(e => {\n    if (e.target !== e.currentTarget) {\n      return;\n    }\n    onBackdropClick == null ? void 0 : onBackdropClick(e);\n    if (backdrop === true) {\n      onHide();\n    }\n  });\n  const handleDocumentKeyDown = (0, _useEventCallback.default)(e => {\n    if (keyboard && (0, _utils.isEscKey)(e) && modal.isTopModal()) {\n      onEscapeKeyDown == null ? void 0 : onEscapeKeyDown(e);\n      if (!e.defaultPrevented) {\n        onHide();\n      }\n    }\n  });\n  const removeFocusListenerRef = (0, _react.useRef)();\n  const removeKeydownListenerRef = (0, _react.useRef)();\n  const handleHidden = (...args) => {\n    setExited(true);\n    onExited == null ? void 0 : onExited(...args);\n  };\n  if (!container) {\n    return null;\n  }\n  const dialogProps = Object.assign({\n    role,\n    ref: modal.setDialogRef,\n    // apparently only works on the dialog role element\n    'aria-modal': role === 'dialog' ? true : undefined\n  }, rest, {\n    style,\n    className,\n    tabIndex: -1\n  });\n  let dialog = renderDialog ? renderDialog(dialogProps) : /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", Object.assign({}, dialogProps, {\n    children: /*#__PURE__*/React.cloneElement(children, {\n      role: 'document'\n    })\n  }));\n  dialog = (0, _ImperativeTransition.renderTransition)(transition, runTransition, {\n    unmountOnExit: true,\n    mountOnEnter: true,\n    appear: true,\n    in: !!show,\n    onExit,\n    onExiting,\n    onExited: handleHidden,\n    onEnter,\n    onEntering,\n    onEntered,\n    children: dialog\n  });\n  let backdropElement = null;\n  if (backdrop) {\n    backdropElement = renderBackdrop({\n      ref: modal.setBackdropRef,\n      onClick: handleBackdropClick\n    });\n    backdropElement = (0, _ImperativeTransition.renderTransition)(backdropTransition, runBackdropTransition, {\n      in: !!show,\n      appear: true,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      children: backdropElement\n    });\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n    children: /*#__PURE__*/_reactDom.default.createPortal( /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n      children: [backdropElement, dialog]\n    }), container)\n  });\n});\nModal.displayName = 'Modal';\nvar _default = exports.default = Object.assign(Modal, {\n  Manager: _ModalManager.default\n});"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,MAAM,YAAY;IAAC;IAAQ;IAAQ;IAAa;IAAS;IAAY;IAAY;IAAY;IAAmB;IAAmB;IAAc;IAAiB;IAAsB;IAAyB;IAAa;IAAgB;IAAgB;IAAuB;IAAgB;IAAkB;IAAW;IAAa;IAAU;IAAU;IAAU;IAAY;IAAa;IAAW;IAAc;CAAY;AAC3a,4EAA4E,GAC5E,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,IAAI;AAEJ;;;AAGA,GAEA,SAAS,WAAW,MAAM;IACxB,IAAI,CAAC,SAAS,UAAU,IAAI,cAAc,OAAO,CAAC;QAChD,eAAe,UAAU,OAAO,KAAK,IAAI,OAAO,QAAQ;IAC1D;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,QAAQ;IAC/B,MAAM,SAAS,CAAC,GAAG,WAAW,OAAO;IACrC,MAAM,eAAe,YAAY,WAAW;IAC5C,MAAM,QAAQ,CAAC,GAAG,OAAO,MAAM,EAAE;QAC/B,QAAQ;QACR,UAAU;IACZ;IACA,OAAO,OAAO,MAAM,CAAC,MAAM,OAAO,EAAE;QAClC,KAAK,IAAM,aAAa,GAAG,CAAC,MAAM,OAAO;QACzC,QAAQ,IAAM,aAAa,MAAM,CAAC,MAAM,OAAO;QAC/C,YAAY,IAAM,aAAa,UAAU,CAAC,MAAM,OAAO;QACvD,cAAc,CAAC,GAAG,OAAO,WAAW,EAAE,CAAA;YACpC,MAAM,OAAO,CAAC,MAAM,GAAG;QACzB,GAAG,EAAE;QACL,gBAAgB,CAAC,GAAG,OAAO,WAAW,EAAE,CAAA;YACtC,MAAM,OAAO,CAAC,QAAQ,GAAG;QAC3B,GAAG,EAAE;IACP;AACF;AACA,MAAM,QAAQ,WAAW,GAAE,CAAC,GAAG,OAAO,UAAU,EAAE,CAAC,MAAM;IACvD,IAAI,EACA,OAAO,KAAK,EACZ,OAAO,QAAQ,EACf,SAAS,EACT,KAAK,EACL,QAAQ,EACR,WAAW,IAAI,EACf,WAAW,IAAI,EACf,eAAe,EACf,eAAe,EACf,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,qBAAqB,EACrB,YAAY,IAAI,EAChB,eAAe,IAAI,EACnB,eAAe,IAAI,EACnB,mBAAmB,EACnB,YAAY,EACZ,iBAAiB,CAAA,QAAS,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,EAC5F,SAAS,eAAe,EACxB,WAAW,YAAY,EACvB,MAAM,EACN,SAAS,KAAO,CAAC,EACjB,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,EACV,SAAS,EACV,GAAG,MACJ,OAAO,8BAA8B,MAAM;IAC7C,MAAM,cAAc,CAAC,GAAG,WAAW,OAAO;IAC1C,MAAM,YAAY,CAAC,GAAG,kBAAkB,OAAO,EAAE;IACjD,MAAM,QAAQ,gBAAgB;IAC9B,MAAM,YAAY,CAAC,GAAG,YAAY,OAAO;IACzC,MAAM,WAAW,CAAC,GAAG,aAAa,OAAO,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAC,GAAG,OAAO,QAAQ,EAAE,CAAC;IAClD,MAAM,eAAe,CAAC,GAAG,OAAO,MAAM,EAAE;IACxC,CAAC,GAAG,OAAO,mBAAmB,EAAE,KAAK,IAAM,OAAO;QAAC;KAAM;IACzD,IAAI,WAAW,OAAO,IAAI,CAAC,YAAY,MAAM;QAC3C,aAAa,OAAO,GAAG,CAAC,GAAG,eAAe,OAAO,EAAE,eAAe,OAAO,KAAK,IAAI,YAAY,QAAQ;IACxG;IAEA,8CAA8C;IAC9C,IAAI,QAAQ,QAAQ;QAClB,UAAU;IACZ;IACA,MAAM,aAAa,CAAC,GAAG,kBAAkB,OAAO,EAAE;QAChD,MAAM,GAAG;QACT,yBAAyB,OAAO,GAAG,CAAC,GAAG,QAAQ,OAAO,EAAE,UAAU,WAAW;QAC7E,uBAAuB,OAAO,GAAG,CAAC,GAAG,QAAQ,OAAO,EAAE,UAAU,SAChE,6EAA6E;QAC7E,8BAA8B;QAC9B,IAAM,WAAW,qBAAqB;QACtC,IAAI,QAAQ;YACV;QACF;QAEA,mEAAmE;QACnE,mCAAmC;QACnC,IAAI,WAAW;YACb,IAAI,uBAAuB;YAC3B,MAAM,uBAAuB,CAAC,GAAG,eAAe,OAAO,EAAE,CAAC,wBAAwB,CAAC,gBAAgB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,cAAc,aAAa,KAAK,OAAO,wBAAwB,eAAe,OAAO,KAAK,IAAI,YAAY,QAAQ;YACtP,IAAI,MAAM,MAAM,IAAI,wBAAwB,CAAC,CAAC,GAAG,UAAU,OAAO,EAAE,MAAM,MAAM,EAAE,uBAAuB;gBACvG,aAAa,OAAO,GAAG;gBACvB,MAAM,MAAM,CAAC,KAAK;YACpB;QACF;IACF;IACA,MAAM,aAAa,CAAC,GAAG,kBAAkB,OAAO,EAAE;QAChD,MAAM,MAAM;QACZ,yBAAyB,OAAO,IAAI,OAAO,KAAK,IAAI,yBAAyB,OAAO;QACpF,uBAAuB,OAAO,IAAI,OAAO,KAAK,IAAI,uBAAuB,OAAO;QAChF,IAAI,cAAc;YAChB,IAAI;YACJ,uEAAuE;YACvE,CAAC,wBAAwB,aAAa,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,KAAK,IAAI,OAAO,KAAK,IAAI,sBAAsB,KAAK,CAAC;YAC7I,aAAa,OAAO,GAAG;QACzB;IACF;IAEA,wHAAwH;IAExH,mBAAmB;IACnB,mDAAmD;IACnD,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,CAAC,QAAQ,CAAC,WAAW;QACzB;IACF,GAAG;QAAC;QAAM;QAAW,wBAAwB,GAAE;KAAW;IAE1D,2BAA2B;IAC3B,+BAA+B;IAC/B,yBAAyB;IACzB,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,CAAC,QAAQ;QACb;IACF,GAAG;QAAC;QAAQ;KAAW;IACvB,CAAC,GAAG,gBAAgB,OAAO,EAAE;QAC3B;IACF;IAEA,mCAAmC;IAEnC,MAAM,qBAAqB,CAAC,GAAG,kBAAkB,OAAO,EAAE;QACxD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,UAAU,IAAI;YACxD;QACF;QACA,MAAM,uBAAuB,CAAC,GAAG,eAAe,OAAO,EAAE,eAAe,OAAO,KAAK,IAAI,YAAY,QAAQ;QAC5G,IAAI,MAAM,MAAM,IAAI,wBAAwB,CAAC,CAAC,GAAG,UAAU,OAAO,EAAE,MAAM,MAAM,EAAE,uBAAuB;YACvG,MAAM,MAAM,CAAC,KAAK;QACpB;IACF;IACA,MAAM,sBAAsB,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAA;QACzD,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;QACA,mBAAmB,OAAO,KAAK,IAAI,gBAAgB;QACnD,IAAI,aAAa,MAAM;YACrB;QACF;IACF;IACA,MAAM,wBAAwB,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAA;QAC3D,IAAI,YAAY,CAAC,GAAG,OAAO,QAAQ,EAAE,MAAM,MAAM,UAAU,IAAI;YAC7D,mBAAmB,OAAO,KAAK,IAAI,gBAAgB;YACnD,IAAI,CAAC,EAAE,gBAAgB,EAAE;gBACvB;YACF;QACF;IACF;IACA,MAAM,yBAAyB,CAAC,GAAG,OAAO,MAAM;IAChD,MAAM,2BAA2B,CAAC,GAAG,OAAO,MAAM;IAClD,MAAM,eAAe,CAAC,GAAG;QACvB,UAAU;QACV,YAAY,OAAO,KAAK,IAAI,YAAY;IAC1C;IACA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IACA,MAAM,cAAc,OAAO,MAAM,CAAC;QAChC;QACA,KAAK,MAAM,YAAY;QACvB,mDAAmD;QACnD,cAAc,SAAS,WAAW,OAAO;IAC3C,GAAG,MAAM;QACP;QACA;QACA,UAAU,CAAC;IACb;IACA,IAAI,SAAS,eAAe,aAAa,eAAe,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa;QAC9H,UAAU,WAAW,GAAE,MAAM,YAAY,CAAC,UAAU;YAClD,MAAM;QACR;IACF;IACA,SAAS,CAAC,GAAG,sBAAsB,gBAAgB,EAAE,YAAY,eAAe;QAC9E,eAAe;QACf,cAAc;QACd,QAAQ;QACR,IAAI,CAAC,CAAC;QACN;QACA;QACA,UAAU;QACV;QACA;QACA;QACA,UAAU;IACZ;IACA,IAAI,kBAAkB;IACtB,IAAI,UAAU;QACZ,kBAAkB,eAAe;YAC/B,KAAK,MAAM,cAAc;YACzB,SAAS;QACX;QACA,kBAAkB,CAAC,GAAG,sBAAsB,gBAAgB,EAAE,oBAAoB,uBAAuB;YACvG,IAAI,CAAC,CAAC;YACN,QAAQ;YACR,cAAc;YACd,eAAe;YACf,UAAU;QACZ;IACF;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,YAAY,QAAQ,EAAE;QAC7D,UAAU,WAAW,GAAE,UAAU,OAAO,CAAC,YAAY,CAAE,WAAW,GAAE,CAAC,GAAG,YAAY,IAAI,EAAE,YAAY,QAAQ,EAAE;YAC9G,UAAU;gBAAC;gBAAiB;aAAO;QACrC,IAAI;IACN;AACF;AACA,MAAM,WAAW,GAAG;AACpB,IAAI,WAAW,QAAQ,OAAO,GAAG,OAAO,MAAM,CAAC,OAAO;IACpD,SAAS,cAAc,OAAO;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/DropdownContext.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nvar _default = exports.default = DropdownContext;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,QAAQ;AACZ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,MAAM,kBAAkB,WAAW,GAAE,MAAM,aAAa,CAAC;AACzD,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/popper.js"], "sourcesContent": ["'use strict';\n\nvar top = 'top';\nvar bottom = 'bottom';\nvar right = 'right';\nvar left = 'left';\nvar auto = 'auto';\nvar basePlacements = [top, bottom, right, left];\nvar start = 'start';\nvar end = 'end';\nvar clippingParents = 'clippingParents';\nvar viewport = 'viewport';\nvar popper = 'popper';\nvar reference = 'reference';\nvar variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nvar placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nvar beforeRead = 'beforeRead';\nvar read = 'read';\nvar afterRead = 'afterRead'; // pure-logic modifiers\n\nvar beforeMain = 'beforeMain';\nvar main = 'main';\nvar afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nvar beforeWrite = 'beforeWrite';\nvar write = 'write';\nvar afterWrite = 'afterWrite';\nvar modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];\n\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\n\nfunction getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nvar max = Math.max;\nvar min = Math.min;\nvar round = Math.round;\n\nfunction getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}\n\nfunction isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}\n\n// means it doesn't take into account transforms.\n\nfunction getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}\n\nfunction contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}\n\nfunction getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\n\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n\nfunction getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}\n\nfunction getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nfunction getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n\nfunction getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n\nfunction within(min$1, value, max$1) {\n  return max(min$1, min(value, max$1));\n}\nfunction withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}\n\nfunction getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}\n\nfunction mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}\n\nfunction expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect$1(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar arrow$1 = {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect$1,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};\n\nfunction getVariation(placement) {\n  return placement.split('-')[1];\n}\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nfunction mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar computeStyles$1 = {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar eventListeners = {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};\n\nvar hash$1 = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash$1[matched];\n  });\n}\n\nvar hash = {\n  start: 'end',\n  end: 'start'\n};\nfunction getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}\n\nfunction getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}\n\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}\n\nfunction getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}\n\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nfunction getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}\n\nfunction isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n\nfunction getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nfunction listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}\n\nfunction rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nfunction getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}\n\nfunction computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n    }\n  }\n\n  return offsets;\n}\n\nfunction detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n\nfunction computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements$1 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements$1.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements$1;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar flip$1 = {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar hide$1 = {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};\n\nfunction distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar offset$1 = {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar popperOffsets$1 = {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};\n\nfunction getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min$1 = offset + overflow[mainSide];\n    var max$1 = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? min(min$1, tetherMin) : min$1, offset, tether ? max(max$1, tetherMax) : max$1);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nvar preventOverflow$1 = {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};\n\nfunction getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}\n\nfunction getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nfunction getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nfunction orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}\n\nfunction debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n\nfunction mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}\n\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nfunction popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\n// For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\nconst createPopper = popperGenerator({\n  defaultModifiers: [\n    hide$1,\n    popperOffsets$1,\n    computeStyles$1,\n    eventListeners,\n    offset$1,\n    flip$1,\n    preventOverflow$1,\n    arrow$1,\n  ],\n});\n\nexports.createPopper = createPopper;\nexports.placements = placements;\n"], "names": [], "mappings": "AAEA,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB;IAAC;IAAK;IAAQ;IAAO;CAAK;AAC/C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAsB,WAAW,GAAE,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;IACnF,OAAO,IAAI,MAAM,CAAC;QAAC,YAAY,MAAM;QAAO,YAAY,MAAM;KAAI;AACpE,GAAG,EAAE;AACL,IAAI,aAAa,WAAW,GAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB;IAAC;CAAK,EAAE,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;IAC7F,OAAO,IAAI,MAAM,CAAC;QAAC;QAAW,YAAY,MAAM;QAAO,YAAY,MAAM;KAAI;AAC/E,GAAG,EAAE,GAAG,sCAAsC;AAE9C,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY,aAAa,uBAAuB;AAEpD,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY,aAAa,kFAAkF;AAE/G,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB;IAAC;IAAY;IAAM;IAAW;IAAY;IAAM;IAAW;IAAa;IAAO;CAAW;AAE/G,SAAS,iBAAiB,SAAS;IACjC,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IAEA,IAAI,KAAK,QAAQ,OAAO,mBAAmB;QACzC,IAAI,gBAAgB,KAAK,aAAa;QACtC,OAAO,gBAAgB,cAAc,WAAW,IAAI,SAAS;IAC/D;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,aAAa,UAAU,MAAM,OAAO;IACxC,OAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,aAAa,UAAU,MAAM,WAAW;IAC5C,OAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,IAAI;IACxB,0BAA0B;IAC1B,IAAI,OAAO,eAAe,aAAa;QACrC,OAAO;IACT;IAEA,IAAI,aAAa,UAAU,MAAM,UAAU;IAC3C,OAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,QAAQ,KAAK,KAAK;AAEtB,SAAS;IACP,IAAI,SAAS,UAAU,aAAa;IAEpC,IAAI,UAAU,QAAQ,OAAO,MAAM,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;QACnE,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,SAAU,IAAI;YACrC,OAAO,KAAK,KAAK,GAAG,MAAM,KAAK,OAAO;QACxC,GAAG,IAAI,CAAC;IACV;IAEA,OAAO,UAAU,SAAS;AAC5B;AAEA,SAAS;IACP,OAAO,CAAC,iCAAiC,IAAI,CAAC;AAChD;AAEA,SAAS,sBAAsB,OAAO,EAAE,YAAY,EAAE,eAAe;IACnE,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IAEA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IAEA,IAAI,aAAa,QAAQ,qBAAqB;IAC9C,IAAI,SAAS;IACb,IAAI,SAAS;IAEb,IAAI,gBAAgB,cAAc,UAAU;QAC1C,SAAS,QAAQ,WAAW,GAAG,IAAI,MAAM,WAAW,KAAK,IAAI,QAAQ,WAAW,IAAI,IAAI;QACxF,SAAS,QAAQ,YAAY,GAAG,IAAI,MAAM,WAAW,MAAM,IAAI,QAAQ,YAAY,IAAI,IAAI;IAC7F;IAEA,IAAI,OAAO,UAAU,WAAW,UAAU,WAAW,QACjD,iBAAiB,KAAK,cAAc;IAExC,IAAI,mBAAmB,CAAC,sBAAsB;IAC9C,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,oBAAoB,iBAAiB,eAAe,UAAU,GAAG,CAAC,CAAC,IAAI;IACnG,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,oBAAoB,iBAAiB,eAAe,SAAS,GAAG,CAAC,CAAC,IAAI;IACjG,IAAI,QAAQ,WAAW,KAAK,GAAG;IAC/B,IAAI,SAAS,WAAW,MAAM,GAAG;IACjC,OAAO;QACL,OAAO;QACP,QAAQ;QACR,KAAK;QACL,OAAO,IAAI;QACX,QAAQ,IAAI;QACZ,MAAM;QACN,GAAG;QACH,GAAG;IACL;AACF;AAEA,iDAAiD;AAEjD,SAAS,cAAc,OAAO;IAC5B,IAAI,aAAa,sBAAsB,UAAU,yDAAyD;IAC1G,4DAA4D;IAE5D,IAAI,QAAQ,QAAQ,WAAW;IAC/B,IAAI,SAAS,QAAQ,YAAY;IAEjC,IAAI,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,UAAU,GAAG;QAC3C,QAAQ,WAAW,KAAK;IAC1B;IAEA,IAAI,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,WAAW,GAAG;QAC7C,SAAS,WAAW,MAAM;IAC5B;IAEA,OAAO;QACL,GAAG,QAAQ,UAAU;QACrB,GAAG,QAAQ,SAAS;QACpB,OAAO;QACP,QAAQ;IACV;AACF;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,WAAW,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,2CAA2C;IAEpG,IAAI,OAAO,QAAQ,CAAC,QAAQ;QAC1B,OAAO;IACT,OACK,IAAI,YAAY,aAAa,WAAW;QACzC,IAAI,OAAO;QAEX,GAAG;YACD,IAAI,QAAQ,OAAO,UAAU,CAAC,OAAO;gBACnC,OAAO;YACT,EAAE,gEAAgE;YAGlE,OAAO,KAAK,UAAU,IAAI,KAAK,IAAI;QACrC,QAAS,KAAM;IACjB,EAAE,+BAA+B;IAGnC,OAAO;AACT;AAEA,SAAS,YAAY,OAAO;IAC1B,OAAO,UAAU,CAAC,QAAQ,QAAQ,IAAI,EAAE,EAAE,WAAW,KAAK;AAC5D;AAEA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,UAAU,SAAS,gBAAgB,CAAC;AAC7C;AAEA,SAAS,eAAe,OAAO;IAC7B,OAAO;QAAC;QAAS;QAAM;KAAK,CAAC,OAAO,CAAC,YAAY,aAAa;AAChE;AAEA,SAAS,mBAAmB,OAAO;IACjC,mEAAmE;IACnE,OAAO,CAAC,CAAC,UAAU,WAAW,QAAQ,aAAa,GACnD,QAAQ,QAAQ,KAAK,OAAO,QAAQ,EAAE,eAAe;AACvD;AAEA,SAAS,cAAc,OAAO;IAC5B,IAAI,YAAY,aAAa,QAAQ;QACnC,OAAO;IACT;IAEA,OACE,kCAAkC;IAClC,2BAA2B;IAC3B,QAAQ,YAAY,IAAI,2DAA2D;IACnF,QAAQ,UAAU,IAAI,CACtB,aAAa,WAAW,QAAQ,IAAI,GAAG,IAAI,KAAK,sBAAsB;IACtE,uDAAuD;IACvD,mBAAmB,SAAS,WAAW;;AAG3C;AAEA,SAAS,oBAAoB,OAAO;IAClC,IAAI,CAAC,cAAc,YAAY,qDAAqD;IACpF,iBAAiB,SAAS,QAAQ,KAAK,SAAS;QAC9C,OAAO;IACT;IAEA,OAAO,QAAQ,YAAY;AAC7B,EAAE,6EAA6E;AAC/E,8BAA8B;AAG9B,SAAS,mBAAmB,OAAO;IACjC,IAAI,YAAY,WAAW,IAAI,CAAC;IAChC,IAAI,OAAO,WAAW,IAAI,CAAC;IAE3B,IAAI,QAAQ,cAAc,UAAU;QAClC,2FAA2F;QAC3F,IAAI,aAAa,iBAAiB;QAElC,IAAI,WAAW,QAAQ,KAAK,SAAS;YACnC,OAAO;QACT;IACF;IAEA,IAAI,cAAc,cAAc;IAEhC,IAAI,aAAa,cAAc;QAC7B,cAAc,YAAY,IAAI;IAChC;IAEA,MAAO,cAAc,gBAAgB;QAAC;QAAQ;KAAO,CAAC,OAAO,CAAC,YAAY,gBAAgB,EAAG;QAC3F,IAAI,MAAM,iBAAiB,cAAc,wEAAwE;QACjH,6BAA6B;QAC7B,qGAAqG;QAErG,IAAI,IAAI,SAAS,KAAK,UAAU,IAAI,WAAW,KAAK,UAAU,IAAI,OAAO,KAAK,WAAW;YAAC;YAAa;SAAc,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM,CAAC,KAAK,aAAa,IAAI,UAAU,KAAK,YAAY,aAAa,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,QAAQ;YACpP,OAAO;QACT,OAAO;YACL,cAAc,YAAY,UAAU;QACtC;IACF;IAEA,OAAO;AACT,EAAE,yEAAyE;AAC3E,kDAAkD;AAGlD,SAAS,gBAAgB,OAAO;IAC9B,IAAI,UAAS,UAAU;IACvB,IAAI,eAAe,oBAAoB;IAEvC,MAAO,gBAAgB,eAAe,iBAAiB,iBAAiB,cAAc,QAAQ,KAAK,SAAU;QAC3G,eAAe,oBAAoB;IACrC;IAEA,IAAI,gBAAgB,CAAC,YAAY,kBAAkB,UAAU,YAAY,kBAAkB,UAAU,iBAAiB,cAAc,QAAQ,KAAK,QAAQ,GAAG;QAC1J,OAAO;IACT;IAEA,OAAO,gBAAgB,mBAAmB,YAAY;AACxD;AAEA,SAAS,yBAAyB,SAAS;IACzC,OAAO;QAAC;QAAO;KAAS,CAAC,OAAO,CAAC,cAAc,IAAI,MAAM;AAC3D;AAEA,SAAS,OAAO,KAAK,EAAE,KAAK,EAAE,KAAK;IACjC,OAAO,IAAI,OAAO,IAAI,OAAO;AAC/B;AACA,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,GAAG;IACrC,IAAI,IAAI,OAAO,KAAK,OAAO;IAC3B,OAAO,IAAI,MAAM,MAAM;AACzB;AAEA,SAAS;IACP,OAAO;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AAEA,SAAS,mBAAmB,aAAa;IACvC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,sBAAsB;AACjD;AAEA,SAAS,gBAAgB,KAAK,EAAE,IAAI;IAClC,OAAO,KAAK,MAAM,CAAC,SAAU,OAAO,EAAE,GAAG;QACvC,OAAO,CAAC,IAAI,GAAG;QACf,OAAO;IACT,GAAG,CAAC;AACN;AAEA,IAAI,kBAAkB,SAAS,gBAAgB,OAAO,EAAE,KAAK;IAC3D,UAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE;QAC/E,WAAW,MAAM,SAAS;IAC5B,MAAM;IACN,OAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS;AAC7F;AAEA,SAAS,MAAM,IAAI;IACjB,IAAI;IAEJ,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI,EAChB,UAAU,KAAK,OAAO;IAC1B,IAAI,eAAe,MAAM,QAAQ,CAAC,KAAK;IACvC,IAAI,gBAAgB,MAAM,aAAa,CAAC,aAAa;IACrD,IAAI,gBAAgB,iBAAiB,MAAM,SAAS;IACpD,IAAI,OAAO,yBAAyB;IACpC,IAAI,aAAa;QAAC;QAAM;KAAM,CAAC,OAAO,CAAC,kBAAkB;IACzD,IAAI,MAAM,aAAa,WAAW;IAElC,IAAI,CAAC,gBAAgB,CAAC,eAAe;QACnC;IACF;IAEA,IAAI,gBAAgB,gBAAgB,QAAQ,OAAO,EAAE;IACrD,IAAI,YAAY,cAAc;IAC9B,IAAI,UAAU,SAAS,MAAM,MAAM;IACnC,IAAI,UAAU,SAAS,MAAM,SAAS;IACtC,IAAI,UAAU,MAAM,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI;IACtH,IAAI,YAAY,aAAa,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK;IACjE,IAAI,oBAAoB,gBAAgB;IACxC,IAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,YAAY,IAAI,IAAI,kBAAkB,WAAW,IAAI,IAAI;IAC/H,IAAI,oBAAoB,UAAU,IAAI,YAAY,GAAG,yEAAyE;IAC9H,+BAA+B;IAE/B,IAAI,MAAM,aAAa,CAAC,QAAQ;IAChC,IAAI,MAAM,aAAa,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC,QAAQ;IAC9D,IAAI,SAAS,aAAa,IAAI,SAAS,CAAC,IAAI,GAAG,IAAI;IACnD,IAAI,SAAS,OAAO,KAAK,QAAQ,MAAM,2CAA2C;IAElF,IAAI,WAAW;IACf,MAAM,aAAa,CAAC,KAAK,GAAG,CAAC,wBAAwB,CAAC,GAAG,qBAAqB,CAAC,SAAS,GAAG,QAAQ,sBAAsB,YAAY,GAAG,SAAS,QAAQ,qBAAqB;AAChL;AAEA,SAAS,SAAS,KAAK;IACrB,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO;IAC3B,IAAI,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,wBAAwB;IAEzE,IAAI,gBAAgB,MAAM;QACxB;IACF,EAAE,eAAe;IAGjB,IAAI,OAAO,iBAAiB,UAAU;QACpC,eAAe,MAAM,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QAEnD,IAAI,CAAC,cAAc;YACjB;QACF;IACF;IAEA,IAAI,CAAC,SAAS,MAAM,QAAQ,CAAC,MAAM,EAAE,eAAe;QAClD;IACF;IAEA,MAAM,QAAQ,CAAC,KAAK,GAAG;AACzB,EAAE,oDAAoD;AAGtD,IAAI,UAAU;IACZ,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,UAAU;QAAC;KAAgB;IAC3B,kBAAkB;QAAC;KAAkB;AACvC;AAEA,SAAS,aAAa,SAAS;IAC7B,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AAEA,IAAI,aAAa;IACf,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR,GAAG,uEAAuE;AAC1E,uEAAuE;AACvE,4DAA4D;AAE5D,SAAS,kBAAkB,IAAI,EAAE,GAAG;IAClC,IAAI,IAAI,KAAK,CAAC,EACV,IAAI,KAAK,CAAC;IACd,IAAI,MAAM,IAAI,gBAAgB,IAAI;IAClC,OAAO;QACL,GAAG,MAAM,IAAI,OAAO,OAAO;QAC3B,GAAG,MAAM,IAAI,OAAO,OAAO;IAC7B;AACF;AAEA,SAAS,YAAY,KAAK;IACxB,IAAI;IAEJ,IAAI,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,eAAe,EACvC,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,YAAY,EACjC,UAAU,MAAM,OAAO;IAC3B,IAAI,aAAa,QAAQ,CAAC,EACtB,IAAI,eAAe,KAAK,IAAI,IAAI,YAChC,aAAa,QAAQ,CAAC,EACtB,IAAI,eAAe,KAAK,IAAI,IAAI;IAEpC,IAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;QAC5D,GAAG;QACH,GAAG;IACL,KAAK;QACH,GAAG;QACH,GAAG;IACL;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,CAAC;IACX,IAAI,OAAO,QAAQ,cAAc,CAAC;IAClC,IAAI,OAAO,QAAQ,cAAc,CAAC;IAClC,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,MAAM;IAEV,IAAI,UAAU;QACZ,IAAI,eAAe,gBAAgB;QACnC,IAAI,aAAa;QACjB,IAAI,YAAY;QAEhB,IAAI,iBAAiB,UAAU,SAAS;YACtC,eAAe,mBAAmB;YAElC,IAAI,iBAAiB,cAAc,QAAQ,KAAK,YAAY,aAAa,YAAY;gBACnF,aAAa;gBACb,YAAY;YACd;QACF,EAAE,8HAA8H;QAGhI,eAAe;QAEf,IAAI,cAAc,OAAO,CAAC,cAAc,QAAQ,cAAc,KAAK,KAAK,cAAc,KAAK;YACzF,QAAQ;YACR,IAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM,GAC/F,YAAY,CAAC,WAAW;YACxB,KAAK,UAAU,WAAW,MAAM;YAChC,KAAK,kBAAkB,IAAI,CAAC;QAC9B;QAEA,IAAI,cAAc,QAAQ,CAAC,cAAc,OAAO,cAAc,MAAM,KAAK,cAAc,KAAK;YAC1F,QAAQ;YACR,IAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,KAAK,GAC9F,YAAY,CAAC,UAAU;YACvB,KAAK,UAAU,WAAW,KAAK;YAC/B,KAAK,kBAAkB,IAAI,CAAC;QAC9B;IACF;IAEA,IAAI,eAAe,OAAO,MAAM,CAAC;QAC/B,UAAU;IACZ,GAAG,YAAY;IAEf,IAAI,QAAQ,iBAAiB,OAAO,kBAAkB;QACpD,GAAG;QACH,GAAG;IACL,GAAG,UAAU,WAAW;QACtB,GAAG;QACH,GAAG;IACL;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,CAAC;IAEX,IAAI,iBAAiB;QACnB,IAAI;QAEJ,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,OAAO,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,OAAO,MAAM,IAAI,eAAe,SAAS,GAAG,CAAC,IAAI,gBAAgB,IAAI,CAAC,KAAK,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,cAAc;IACjT;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,eAAe,CAAC,MAAM,GAAG,OAAO,IAAI,OAAO,IAAI,gBAAgB,SAAS,GAAG,IAAI,eAAe;AAC7M;AAEA,SAAS,cAAc,KAAK;IAC1B,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO;IAC3B,IAAI,wBAAwB,QAAQ,eAAe,EAC/C,kBAAkB,0BAA0B,KAAK,IAAI,OAAO,uBAC5D,oBAAoB,QAAQ,QAAQ,EACpC,WAAW,sBAAsB,KAAK,IAAI,OAAO,mBACjD,wBAAwB,QAAQ,YAAY,EAC5C,eAAe,0BAA0B,KAAK,IAAI,OAAO;IAC7D,IAAI,eAAe;QACjB,WAAW,iBAAiB,MAAM,SAAS;QAC3C,WAAW,aAAa,MAAM,SAAS;QACvC,QAAQ,MAAM,QAAQ,CAAC,MAAM;QAC7B,YAAY,MAAM,KAAK,CAAC,MAAM;QAC9B,iBAAiB;QACjB,SAAS,MAAM,OAAO,CAAC,QAAQ,KAAK;IACtC;IAEA,IAAI,MAAM,aAAa,CAAC,aAAa,IAAI,MAAM;QAC7C,MAAM,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;YACvG,SAAS,MAAM,aAAa,CAAC,aAAa;YAC1C,UAAU,MAAM,OAAO,CAAC,QAAQ;YAChC,UAAU;YACV,cAAc;QAChB;IACF;IAEA,IAAI,MAAM,aAAa,CAAC,KAAK,IAAI,MAAM;QACrC,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;YACrG,SAAS,MAAM,aAAa,CAAC,KAAK;YAClC,UAAU;YACV,UAAU;YACV,cAAc;QAChB;IACF;IAEA,MAAM,UAAU,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE;QACnE,yBAAyB,MAAM,SAAS;IAC1C;AACF,EAAE,oDAAoD;AAGtD,IAAI,kBAAkB;IACpB,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,MAAM,CAAC;AACT;AAEA,IAAI,UAAU;IACZ,SAAS;AACX;AAEA,SAAS,OAAO,IAAI;IAClB,IAAI,QAAQ,KAAK,KAAK,EAClB,WAAW,KAAK,QAAQ,EACxB,UAAU,KAAK,OAAO;IAC1B,IAAI,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO,iBAC7C,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO;IACjD,IAAI,UAAS,UAAU,MAAM,QAAQ,CAAC,MAAM;IAC5C,IAAI,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,aAAa,CAAC,SAAS,EAAE,MAAM,aAAa,CAAC,MAAM;IAEvF,IAAI,QAAQ;QACV,cAAc,OAAO,CAAC,SAAU,YAAY;YAC1C,aAAa,gBAAgB,CAAC,UAAU,SAAS,MAAM,EAAE;QAC3D;IACF;IAEA,IAAI,QAAQ;QACV,QAAO,gBAAgB,CAAC,UAAU,SAAS,MAAM,EAAE;IACrD;IAEA,OAAO;QACL,IAAI,QAAQ;YACV,cAAc,OAAO,CAAC,SAAU,YAAY;gBAC1C,aAAa,mBAAmB,CAAC,UAAU,SAAS,MAAM,EAAE;YAC9D;QACF;QAEA,IAAI,QAAQ;YACV,QAAO,mBAAmB,CAAC,UAAU,SAAS,MAAM,EAAE;QACxD;IACF;AACF,EAAE,oDAAoD;AAGtD,IAAI,iBAAiB;IACnB,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI,SAAS,MAAM;IACnB,QAAQ;IACR,MAAM,CAAC;AACT;AAEA,IAAI,SAAS;IACX,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;AACP;AACA,SAAS,qBAAqB,SAAS;IACrC,OAAO,UAAU,OAAO,CAAC,0BAA0B,SAAU,OAAO;QAClE,OAAO,MAAM,CAAC,QAAQ;IACxB;AACF;AAEA,IAAI,OAAO;IACT,OAAO;IACP,KAAK;AACP;AACA,SAAS,8BAA8B,SAAS;IAC9C,OAAO,UAAU,OAAO,CAAC,cAAc,SAAU,OAAO;QACtD,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AAEA,SAAS,gBAAgB,IAAI;IAC3B,IAAI,MAAM,UAAU;IACpB,IAAI,aAAa,IAAI,WAAW;IAChC,IAAI,YAAY,IAAI,WAAW;IAC/B,OAAO;QACL,YAAY;QACZ,WAAW;IACb;AACF;AAEA,SAAS,oBAAoB,OAAO;IAClC,yEAAyE;IACzE,qBAAqB;IACrB,6EAA6E;IAC7E,yEAAyE;IACzE,UAAU;IACV,0EAA0E;IAC1E,sCAAsC;IACtC,OAAO,sBAAsB,mBAAmB,UAAU,IAAI,GAAG,gBAAgB,SAAS,UAAU;AACtG;AAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,IAAI,MAAM,UAAU;IACpB,IAAI,OAAO,mBAAmB;IAC9B,IAAI,iBAAiB,IAAI,cAAc;IACvC,IAAI,QAAQ,KAAK,WAAW;IAC5B,IAAI,SAAS,KAAK,YAAY;IAC9B,IAAI,IAAI;IACR,IAAI,IAAI;IAER,IAAI,gBAAgB;QAClB,QAAQ,eAAe,KAAK;QAC5B,SAAS,eAAe,MAAM;QAC9B,IAAI,iBAAiB;QAErB,IAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;YAC7D,IAAI,eAAe,UAAU;YAC7B,IAAI,eAAe,SAAS;QAC9B;IACF;IAEA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,GAAG,IAAI,oBAAoB;QAC3B,GAAG;IACL;AACF;AAEA,sEAAsE;AAEtE,SAAS,gBAAgB,OAAO;IAC9B,IAAI;IAEJ,IAAI,OAAO,mBAAmB;IAC9B,IAAI,YAAY,gBAAgB;IAChC,IAAI,OAAO,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;IACxG,IAAI,QAAQ,IAAI,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,OAAO,KAAK,WAAW,GAAG,GAAG,OAAO,KAAK,WAAW,GAAG;IAC3G,IAAI,SAAS,IAAI,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,OAAO,KAAK,YAAY,GAAG,GAAG,OAAO,KAAK,YAAY,GAAG;IAChH,IAAI,IAAI,CAAC,UAAU,UAAU,GAAG,oBAAoB;IACpD,IAAI,IAAI,CAAC,UAAU,SAAS;IAE5B,IAAI,iBAAiB,QAAQ,MAAM,SAAS,KAAK,OAAO;QACtD,KAAK,IAAI,KAAK,WAAW,EAAE,OAAO,KAAK,WAAW,GAAG,KAAK;IAC5D;IAEA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,GAAG;QACH,GAAG;IACL;AACF;AAEA,SAAS,eAAe,OAAO;IAC7B,6DAA6D;IAC7D,IAAI,oBAAoB,iBAAiB,UACrC,WAAW,kBAAkB,QAAQ,EACrC,YAAY,kBAAkB,SAAS,EACvC,YAAY,kBAAkB,SAAS;IAE3C,OAAO,6BAA6B,IAAI,CAAC,WAAW,YAAY;AAClE;AAEA,SAAS,gBAAgB,IAAI;IAC3B,IAAI;QAAC;QAAQ;QAAQ;KAAY,CAAC,OAAO,CAAC,YAAY,UAAU,GAAG;QACjE,mEAAmE;QACnE,OAAO,KAAK,aAAa,CAAC,IAAI;IAChC;IAEA,IAAI,cAAc,SAAS,eAAe,OAAO;QAC/C,OAAO;IACT;IAEA,OAAO,gBAAgB,cAAc;AACvC;AAEA;;;;;AAKA,GAEA,SAAS,kBAAkB,OAAO,EAAE,IAAI;IACtC,IAAI;IAEJ,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,EAAE;IACX;IAEA,IAAI,eAAe,gBAAgB;IACnC,IAAI,SAAS,iBAAiB,CAAC,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;IAC5H,IAAI,MAAM,UAAU;IACpB,IAAI,SAAS,SAAS;QAAC;KAAI,CAAC,MAAM,CAAC,IAAI,cAAc,IAAI,EAAE,EAAE,eAAe,gBAAgB,eAAe,EAAE,IAAI;IACjH,IAAI,cAAc,KAAK,MAAM,CAAC;IAC9B,OAAO,SAAS,cAChB,YAAY,MAAM,CAAC,kBAAkB,cAAc;AACrD;AAEA,SAAS,iBAAiB,IAAI;IAC5B,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC7B,MAAM,KAAK,CAAC;QACZ,KAAK,KAAK,CAAC;QACX,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;QAC1B,QAAQ,KAAK,CAAC,GAAG,KAAK,MAAM;IAC9B;AACF;AAEA,SAAS,2BAA2B,OAAO,EAAE,QAAQ;IACnD,IAAI,OAAO,sBAAsB,SAAS,OAAO,aAAa;IAC9D,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,QAAQ,SAAS;IACvC,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,QAAQ,UAAU;IAC1C,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,QAAQ,YAAY;IAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG,QAAQ,WAAW;IAC5C,KAAK,KAAK,GAAG,QAAQ,WAAW;IAChC,KAAK,MAAM,GAAG,QAAQ,YAAY;IAClC,KAAK,CAAC,GAAG,KAAK,IAAI;IAClB,KAAK,CAAC,GAAG,KAAK,GAAG;IACjB,OAAO;AACT;AAEA,SAAS,2BAA2B,OAAO,EAAE,cAAc,EAAE,QAAQ;IACnE,OAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,SAAS,aAAa,UAAU,kBAAkB,2BAA2B,gBAAgB,YAAY,iBAAiB,gBAAgB,mBAAmB;AACrO,EAAE,8EAA8E;AAChF,2EAA2E;AAC3E,YAAY;AAGZ,SAAS,mBAAmB,OAAO;IACjC,IAAI,kBAAkB,kBAAkB,cAAc;IACtD,IAAI,oBAAoB;QAAC;QAAY;KAAQ,CAAC,OAAO,CAAC,iBAAiB,SAAS,QAAQ,KAAK;IAC7F,IAAI,iBAAiB,qBAAqB,cAAc,WAAW,gBAAgB,WAAW;IAE9F,IAAI,CAAC,UAAU,iBAAiB;QAC9B,OAAO,EAAE;IACX,EAAE,gFAAgF;IAGlF,OAAO,gBAAgB,MAAM,CAAC,SAAU,cAAc;QACpD,OAAO,UAAU,mBAAmB,SAAS,gBAAgB,mBAAmB,YAAY,oBAAoB;IAClH;AACF,EAAE,4EAA4E;AAC9E,mBAAmB;AAGnB,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ;IAChE,IAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,WAAW,EAAE,CAAC,MAAM,CAAC;IACnG,IAAI,kBAAkB,EAAE,CAAC,MAAM,CAAC,qBAAqB;QAAC;KAAa;IACnE,IAAI,sBAAsB,eAAe,CAAC,EAAE;IAC5C,IAAI,eAAe,gBAAgB,MAAM,CAAC,SAAU,OAAO,EAAE,cAAc;QACzE,IAAI,OAAO,2BAA2B,SAAS,gBAAgB;QAC/D,QAAQ,GAAG,GAAG,IAAI,KAAK,GAAG,EAAE,QAAQ,GAAG;QACvC,QAAQ,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE,QAAQ,KAAK;QAC7C,QAAQ,MAAM,GAAG,IAAI,KAAK,MAAM,EAAE,QAAQ,MAAM;QAChD,QAAQ,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,QAAQ,IAAI;QAC1C,OAAO;IACT,GAAG,2BAA2B,SAAS,qBAAqB;IAC5D,aAAa,KAAK,GAAG,aAAa,KAAK,GAAG,aAAa,IAAI;IAC3D,aAAa,MAAM,GAAG,aAAa,MAAM,GAAG,aAAa,GAAG;IAC5D,aAAa,CAAC,GAAG,aAAa,IAAI;IAClC,aAAa,CAAC,GAAG,aAAa,GAAG;IACjC,OAAO;AACT;AAEA,SAAS,eAAe,IAAI;IAC1B,IAAI,YAAY,KAAK,SAAS,EAC1B,UAAU,KAAK,OAAO,EACtB,YAAY,KAAK,SAAS;IAC9B,IAAI,gBAAgB,YAAY,iBAAiB,aAAa;IAC9D,IAAI,YAAY,YAAY,aAAa,aAAa;IACtD,IAAI,UAAU,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;IAClE,IAAI,UAAU,UAAU,CAAC,GAAG,UAAU,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;IACpE,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,UAAU;gBACR,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,QAAQ,MAAM;YACjC;YACA;QAEF,KAAK;YACH,UAAU;gBACR,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,UAAU,MAAM;YACnC;YACA;QAEF,KAAK;YACH,UAAU;gBACR,GAAG,UAAU,CAAC,GAAG,UAAU,KAAK;gBAChC,GAAG;YACL;YACA;QAEF,KAAK;YACH,UAAU;gBACR,GAAG,UAAU,CAAC,GAAG,QAAQ,KAAK;gBAC9B,GAAG;YACL;YACA;QAEF;YACE,UAAU;gBACR,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;YAChB;IACJ;IAEA,IAAI,WAAW,gBAAgB,yBAAyB,iBAAiB;IAEzE,IAAI,YAAY,MAAM;QACpB,IAAI,MAAM,aAAa,MAAM,WAAW;QAExC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC;gBAC9E;YAEF,KAAK;gBACH,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC;gBAC9E;QACJ;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO;IACpC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IAEA,IAAI,WAAW,SACX,qBAAqB,SAAS,SAAS,EACvC,YAAY,uBAAuB,KAAK,IAAI,MAAM,SAAS,GAAG,oBAC9D,oBAAoB,SAAS,QAAQ,EACrC,WAAW,sBAAsB,KAAK,IAAI,MAAM,QAAQ,GAAG,mBAC3D,oBAAoB,SAAS,QAAQ,EACrC,WAAW,sBAAsB,KAAK,IAAI,kBAAkB,mBAC5D,wBAAwB,SAAS,YAAY,EAC7C,eAAe,0BAA0B,KAAK,IAAI,WAAW,uBAC7D,wBAAwB,SAAS,cAAc,EAC/C,iBAAiB,0BAA0B,KAAK,IAAI,SAAS,uBAC7D,uBAAuB,SAAS,WAAW,EAC3C,cAAc,yBAAyB,KAAK,IAAI,QAAQ,sBACxD,mBAAmB,SAAS,OAAO,EACnC,UAAU,qBAAqB,KAAK,IAAI,IAAI;IAChD,IAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS;IACxG,IAAI,aAAa,mBAAmB,SAAS,YAAY;IACzD,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,UAAU,MAAM,QAAQ,CAAC,cAAc,aAAa,eAAe;IACvE,IAAI,qBAAqB,gBAAgB,UAAU,WAAW,UAAU,QAAQ,cAAc,IAAI,mBAAmB,MAAM,QAAQ,CAAC,MAAM,GAAG,UAAU,cAAc;IACrK,IAAI,sBAAsB,sBAAsB,MAAM,QAAQ,CAAC,SAAS;IACxE,IAAI,gBAAgB,eAAe;QACjC,WAAW;QACX,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA,IAAI,mBAAmB,iBAAiB,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;IACtE,IAAI,oBAAoB,mBAAmB,SAAS,mBAAmB,qBAAqB,2CAA2C;IACvI,2CAA2C;IAE3C,IAAI,kBAAkB;QACpB,KAAK,mBAAmB,GAAG,GAAG,kBAAkB,GAAG,GAAG,cAAc,GAAG;QACvE,QAAQ,kBAAkB,MAAM,GAAG,mBAAmB,MAAM,GAAG,cAAc,MAAM;QACnF,MAAM,mBAAmB,IAAI,GAAG,kBAAkB,IAAI,GAAG,cAAc,IAAI;QAC3E,OAAO,kBAAkB,KAAK,GAAG,mBAAmB,KAAK,GAAG,cAAc,KAAK;IACjF;IACA,IAAI,aAAa,MAAM,aAAa,CAAC,MAAM,EAAE,oDAAoD;IAEjG,IAAI,mBAAmB,UAAU,YAAY;QAC3C,IAAI,SAAS,UAAU,CAAC,UAAU;QAClC,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,SAAU,GAAG;YAChD,IAAI,WAAW;gBAAC;gBAAO;aAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;YACxD,IAAI,OAAO;gBAAC;gBAAK;aAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,MAAM;YACnD,eAAe,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG;QACzC;IACF;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,KAAK,EAAE,OAAO;IAC1C,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IAEA,IAAI,WAAW,SACX,YAAY,SAAS,SAAS,EAC9B,WAAW,SAAS,QAAQ,EAC5B,eAAe,SAAS,YAAY,EACpC,UAAU,SAAS,OAAO,EAC1B,iBAAiB,SAAS,cAAc,EACxC,wBAAwB,SAAS,qBAAqB,EACtD,wBAAwB,0BAA0B,KAAK,IAAI,aAAa;IAC5E,IAAI,YAAY,aAAa;IAC7B,IAAI,eAAe,YAAY,iBAAiB,sBAAsB,oBAAoB,MAAM,CAAC,SAAU,SAAS;QAClH,OAAO,aAAa,eAAe;IACrC,KAAK;IACL,IAAI,oBAAoB,aAAa,MAAM,CAAC,SAAU,SAAS;QAC7D,OAAO,sBAAsB,OAAO,CAAC,cAAc;IACrD;IAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAClC,oBAAoB;IACtB,EAAE,sFAAsF;IAGxF,IAAI,YAAY,kBAAkB,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QAC/D,GAAG,CAAC,UAAU,GAAG,eAAe,OAAO;YACrC,WAAW;YACX,UAAU;YACV,cAAc;YACd,SAAS;QACX,EAAE,CAAC,iBAAiB,WAAW;QAC/B,OAAO;IACT,GAAG,CAAC;IACJ,OAAO,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAC/C,OAAO,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IACpC;AACF;AAEA,SAAS,8BAA8B,SAAS;IAC9C,IAAI,iBAAiB,eAAe,MAAM;QACxC,OAAO,EAAE;IACX;IAEA,IAAI,oBAAoB,qBAAqB;IAC7C,OAAO;QAAC,8BAA8B;QAAY;QAAmB,8BAA8B;KAAmB;AACxH;AAEA,SAAS,KAAK,IAAI;IAChB,IAAI,QAAQ,KAAK,KAAK,EAClB,UAAU,KAAK,OAAO,EACtB,OAAO,KAAK,IAAI;IAEpB,IAAI,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE;QACnC;IACF;IAEA,IAAI,oBAAoB,QAAQ,QAAQ,EACpC,gBAAgB,sBAAsB,KAAK,IAAI,OAAO,mBACtD,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,OAAO,kBACpD,8BAA8B,QAAQ,kBAAkB,EACxD,UAAU,QAAQ,OAAO,EACzB,WAAW,QAAQ,QAAQ,EAC3B,eAAe,QAAQ,YAAY,EACnC,cAAc,QAAQ,WAAW,EACjC,wBAAwB,QAAQ,cAAc,EAC9C,iBAAiB,0BAA0B,KAAK,IAAI,OAAO,uBAC3D,wBAAwB,QAAQ,qBAAqB;IACzD,IAAI,qBAAqB,MAAM,OAAO,CAAC,SAAS;IAChD,IAAI,gBAAgB,iBAAiB;IACrC,IAAI,kBAAkB,kBAAkB;IACxC,IAAI,qBAAqB,+BAA+B,CAAC,mBAAmB,CAAC,iBAAiB;QAAC,qBAAqB;KAAoB,GAAG,8BAA8B,mBAAmB;IAC5L,IAAI,aAAa;QAAC;KAAmB,CAAC,MAAM,CAAC,oBAAoB,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QAC9F,OAAO,IAAI,MAAM,CAAC,iBAAiB,eAAe,OAAO,qBAAqB,OAAO;YACnF,WAAW;YACX,UAAU;YACV,cAAc;YACd,SAAS;YACT,gBAAgB;YAChB,uBAAuB;QACzB,KAAK;IACP,GAAG,EAAE;IACL,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,YAAY,IAAI;IACpB,IAAI,qBAAqB;IACzB,IAAI,wBAAwB,UAAU,CAAC,EAAE;IAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,IAAI,YAAY,UAAU,CAAC,EAAE;QAE7B,IAAI,iBAAiB,iBAAiB;QAEtC,IAAI,mBAAmB,aAAa,eAAe;QACnD,IAAI,aAAa;YAAC;YAAK;SAAO,CAAC,OAAO,CAAC,mBAAmB;QAC1D,IAAI,MAAM,aAAa,UAAU;QACjC,IAAI,WAAW,eAAe,OAAO;YACnC,WAAW;YACX,UAAU;YACV,cAAc;YACd,aAAa;YACb,SAAS;QACX;QACA,IAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;QAEnG,IAAI,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE;YACxC,oBAAoB,qBAAqB;QAC3C;QAEA,IAAI,mBAAmB,qBAAqB;QAC5C,IAAI,SAAS,EAAE;QAEf,IAAI,eAAe;YACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI;QAC1C;QAEA,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,IAAI,GAAG,QAAQ,CAAC,iBAAiB,IAAI;QAC9E;QAEA,IAAI,OAAO,KAAK,CAAC,SAAU,KAAK;YAC9B,OAAO;QACT,IAAI;YACF,wBAAwB;YACxB,qBAAqB;YACrB;QACF;QAEA,UAAU,GAAG,CAAC,WAAW;IAC3B;IAEA,IAAI,oBAAoB;QACtB,oDAAoD;QACpD,IAAI,iBAAiB,iBAAiB,IAAI;QAE1C,IAAI,QAAQ,SAAS,MAAM,EAAE;YAC3B,IAAI,mBAAmB,WAAW,IAAI,CAAC,SAAU,SAAS;gBACxD,IAAI,SAAS,UAAU,GAAG,CAAC;gBAE3B,IAAI,QAAQ;oBACV,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,SAAU,KAAK;wBAC9C,OAAO;oBACT;gBACF;YACF;YAEA,IAAI,kBAAkB;gBACpB,wBAAwB;gBACxB,OAAO;YACT;QACF;QAEA,IAAK,IAAI,KAAK,gBAAgB,KAAK,GAAG,KAAM;YAC1C,IAAI,OAAO,MAAM;YAEjB,IAAI,SAAS,SAAS;QACxB;IACF;IAEA,IAAI,MAAM,SAAS,KAAK,uBAAuB;QAC7C,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;QAClC,MAAM,SAAS,GAAG;QAClB,MAAM,KAAK,GAAG;IAChB;AACF,EAAE,oDAAoD;AAGtD,IAAI,SAAS;IACX,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,kBAAkB;QAAC;KAAS;IAC5B,MAAM;QACJ,OAAO;IACT;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,IAAI,EAAE,gBAAgB;IACtD,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;YACjB,GAAG;YACH,GAAG;QACL;IACF;IAEA,OAAO;QACL,KAAK,SAAS,GAAG,GAAG,KAAK,MAAM,GAAG,iBAAiB,CAAC;QACpD,OAAO,SAAS,KAAK,GAAG,KAAK,KAAK,GAAG,iBAAiB,CAAC;QACvD,QAAQ,SAAS,MAAM,GAAG,KAAK,MAAM,GAAG,iBAAiB,CAAC;QAC1D,MAAM,SAAS,IAAI,GAAG,KAAK,KAAK,GAAG,iBAAiB,CAAC;IACvD;AACF;AAEA,SAAS,sBAAsB,QAAQ;IACrC,OAAO;QAAC;QAAK;QAAO;QAAQ;KAAK,CAAC,IAAI,CAAC,SAAU,IAAI;QACnD,OAAO,QAAQ,CAAC,KAAK,IAAI;IAC3B;AACF;AAEA,SAAS,KAAK,IAAI;IAChB,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;IACpB,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,mBAAmB,MAAM,aAAa,CAAC,eAAe;IAC1D,IAAI,oBAAoB,eAAe,OAAO;QAC5C,gBAAgB;IAClB;IACA,IAAI,oBAAoB,eAAe,OAAO;QAC5C,aAAa;IACf;IACA,IAAI,2BAA2B,eAAe,mBAAmB;IACjE,IAAI,sBAAsB,eAAe,mBAAmB,YAAY;IACxE,IAAI,oBAAoB,sBAAsB;IAC9C,IAAI,mBAAmB,sBAAsB;IAC7C,MAAM,aAAa,CAAC,KAAK,GAAG;QAC1B,0BAA0B;QAC1B,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;IACpB;IACA,MAAM,UAAU,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE;QACnE,gCAAgC;QAChC,uBAAuB;IACzB;AACF,EAAE,oDAAoD;AAGtD,IAAI,SAAS;IACX,MAAM;IACN,SAAS;IACT,OAAO;IACP,kBAAkB;QAAC;KAAkB;IACrC,IAAI;AACN;AAEA,SAAS,wBAAwB,SAAS,EAAE,KAAK,EAAE,MAAM;IACvD,IAAI,gBAAgB,iBAAiB;IACrC,IAAI,iBAAiB;QAAC;QAAM;KAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,IAAI;IAEpE,IAAI,OAAO,OAAO,WAAW,aAAa,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QACxE,WAAW;IACb,MAAM,QACF,WAAW,IAAI,CAAC,EAAE,EAClB,WAAW,IAAI,CAAC,EAAE;IAEtB,WAAW,YAAY;IACvB,WAAW,CAAC,YAAY,CAAC,IAAI;IAC7B,OAAO;QAAC;QAAM;KAAM,CAAC,OAAO,CAAC,kBAAkB,IAAI;QACjD,GAAG;QACH,GAAG;IACL,IAAI;QACF,GAAG;QACH,GAAG;IACL;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,IAAI,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO,EACvB,OAAO,MAAM,IAAI;IACrB,IAAI,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI;QAAC;QAAG;KAAE,GAAG;IACnD,IAAI,OAAO,WAAW,MAAM,CAAC,SAAU,GAAG,EAAE,SAAS;QACnD,GAAG,CAAC,UAAU,GAAG,wBAAwB,WAAW,MAAM,KAAK,EAAE;QACjE,OAAO;IACT,GAAG,CAAC;IACJ,IAAI,wBAAwB,IAAI,CAAC,MAAM,SAAS,CAAC,EAC7C,IAAI,sBAAsB,CAAC,EAC3B,IAAI,sBAAsB,CAAC;IAE/B,IAAI,MAAM,aAAa,CAAC,aAAa,IAAI,MAAM;QAC7C,MAAM,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI;QACvC,MAAM,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI;IACzC;IAEA,MAAM,aAAa,CAAC,KAAK,GAAG;AAC9B,EAAE,oDAAoD;AAGtD,IAAI,WAAW;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,UAAU;QAAC;KAAgB;IAC3B,IAAI;AACN;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;IACpB,iEAAiE;IACjE,iDAAiD;IACjD,4DAA4D;IAC5D,iCAAiC;IACjC,MAAM,aAAa,CAAC,KAAK,GAAG,eAAe;QACzC,WAAW,MAAM,KAAK,CAAC,SAAS;QAChC,SAAS,MAAM,KAAK,CAAC,MAAM;QAC3B,UAAU;QACV,WAAW,MAAM,SAAS;IAC5B;AACF,EAAE,oDAAoD;AAGtD,IAAI,kBAAkB;IACpB,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,MAAM,CAAC;AACT;AAEA,SAAS,WAAW,IAAI;IACtB,OAAO,SAAS,MAAM,MAAM;AAC9B;AAEA,SAAS,gBAAgB,IAAI;IAC3B,IAAI,QAAQ,KAAK,KAAK,EAClB,UAAU,KAAK,OAAO,EACtB,OAAO,KAAK,IAAI;IACpB,IAAI,oBAAoB,QAAQ,QAAQ,EACpC,gBAAgB,sBAAsB,KAAK,IAAI,OAAO,mBACtD,mBAAmB,QAAQ,OAAO,EAClC,eAAe,qBAAqB,KAAK,IAAI,QAAQ,kBACrD,WAAW,QAAQ,QAAQ,EAC3B,eAAe,QAAQ,YAAY,EACnC,cAAc,QAAQ,WAAW,EACjC,UAAU,QAAQ,OAAO,EACzB,kBAAkB,QAAQ,MAAM,EAChC,SAAS,oBAAoB,KAAK,IAAI,OAAO,iBAC7C,wBAAwB,QAAQ,YAAY,EAC5C,eAAe,0BAA0B,KAAK,IAAI,IAAI;IAC1D,IAAI,WAAW,eAAe,OAAO;QACnC,UAAU;QACV,cAAc;QACd,SAAS;QACT,aAAa;IACf;IACA,IAAI,gBAAgB,iBAAiB,MAAM,SAAS;IACpD,IAAI,YAAY,aAAa,MAAM,SAAS;IAC5C,IAAI,kBAAkB,CAAC;IACvB,IAAI,WAAW,yBAAyB;IACxC,IAAI,UAAU,WAAW;IACzB,IAAI,gBAAgB,MAAM,aAAa,CAAC,aAAa;IACrD,IAAI,gBAAgB,MAAM,KAAK,CAAC,SAAS;IACzC,IAAI,aAAa,MAAM,KAAK,CAAC,MAAM;IACnC,IAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE;QACvG,WAAW,MAAM,SAAS;IAC5B,MAAM;IACN,IAAI,8BAA8B,OAAO,sBAAsB,WAAW;QACxE,UAAU;QACV,SAAS;IACX,IAAI,OAAO,MAAM,CAAC;QAChB,UAAU;QACV,SAAS;IACX,GAAG;IACH,IAAI,sBAAsB,MAAM,aAAa,CAAC,MAAM,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG;IACrG,IAAI,OAAO;QACT,GAAG;QACH,GAAG;IACL;IAEA,IAAI,CAAC,eAAe;QAClB;IACF;IAEA,IAAI,eAAe;QACjB,IAAI;QAEJ,IAAI,WAAW,aAAa,MAAM,MAAM;QACxC,IAAI,UAAU,aAAa,MAAM,SAAS;QAC1C,IAAI,MAAM,aAAa,MAAM,WAAW;QACxC,IAAI,SAAS,aAAa,CAAC,SAAS;QACpC,IAAI,QAAQ,SAAS,QAAQ,CAAC,SAAS;QACvC,IAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ;QACtC,IAAI,WAAW,SAAS,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI;QAC/C,IAAI,SAAS,cAAc,QAAQ,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;QACvE,IAAI,SAAS,cAAc,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,0EAA0E;QACrJ,+BAA+B;QAE/B,IAAI,eAAe,MAAM,QAAQ,CAAC,KAAK;QACvC,IAAI,YAAY,UAAU,eAAe,cAAc,gBAAgB;YACrE,OAAO;YACP,QAAQ;QACV;QACA,IAAI,qBAAqB,MAAM,aAAa,CAAC,mBAAmB,GAAG,MAAM,aAAa,CAAC,mBAAmB,CAAC,OAAO,GAAG;QACrH,IAAI,kBAAkB,kBAAkB,CAAC,SAAS;QAClD,IAAI,kBAAkB,kBAAkB,CAAC,QAAQ,EAAE,0EAA0E;QAC7H,yEAAyE;QACzE,uEAAuE;QACvE,sEAAsE;QACtE,mBAAmB;QAEnB,IAAI,WAAW,OAAO,GAAG,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI;QAC3D,IAAI,YAAY,kBAAkB,aAAa,CAAC,IAAI,GAAG,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,QAAQ,GAAG,SAAS,WAAW,kBAAkB,4BAA4B,QAAQ;QACpN,IAAI,YAAY,kBAAkB,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,QAAQ,GAAG,SAAS,WAAW,kBAAkB,4BAA4B,QAAQ;QACrN,IAAI,oBAAoB,MAAM,QAAQ,CAAC,KAAK,IAAI,gBAAgB,MAAM,QAAQ,CAAC,KAAK;QACpF,IAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,SAAS,IAAI,IAAI,kBAAkB,UAAU,IAAI,IAAI;QACjI,IAAI,sBAAsB,CAAC,wBAAwB,uBAAuB,OAAO,KAAK,IAAI,mBAAmB,CAAC,SAAS,KAAK,OAAO,wBAAwB;QAC3J,IAAI,YAAY,SAAS,YAAY,sBAAsB;QAC3D,IAAI,YAAY,SAAS,YAAY;QACrC,IAAI,kBAAkB,OAAO,SAAS,IAAI,OAAO,aAAa,OAAO,QAAQ,SAAS,IAAI,OAAO,aAAa;QAC9G,aAAa,CAAC,SAAS,GAAG;QAC1B,IAAI,CAAC,SAAS,GAAG,kBAAkB;IACrC;IAEA,IAAI,cAAc;QAChB,IAAI;QAEJ,IAAI,YAAY,aAAa,MAAM,MAAM;QAEzC,IAAI,WAAW,aAAa,MAAM,SAAS;QAE3C,IAAI,UAAU,aAAa,CAAC,QAAQ;QAEpC,IAAI,OAAO,YAAY,MAAM,WAAW;QAExC,IAAI,OAAO,UAAU,QAAQ,CAAC,UAAU;QAExC,IAAI,OAAO,UAAU,QAAQ,CAAC,SAAS;QAEvC,IAAI,eAAe;YAAC;YAAK;SAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAE3D,IAAI,uBAAuB,CAAC,yBAAyB,uBAAuB,OAAO,KAAK,IAAI,mBAAmB,CAAC,QAAQ,KAAK,OAAO,yBAAyB;QAE7J,IAAI,aAAa,eAAe,OAAO,UAAU,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,uBAAuB,4BAA4B,OAAO;QAEpJ,IAAI,aAAa,eAAe,UAAU,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,uBAAuB,4BAA4B,OAAO,GAAG;QAEhJ,IAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,cAAc,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa;QAEpK,aAAa,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG,mBAAmB;IACrC;IAEA,MAAM,aAAa,CAAC,KAAK,GAAG;AAC9B,EAAE,oDAAoD;AAGtD,IAAI,oBAAoB;IACtB,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI;IACJ,kBAAkB;QAAC;KAAS;AAC9B;AAEA,SAAS,qBAAqB,OAAO;IACnC,OAAO;QACL,YAAY,QAAQ,UAAU;QAC9B,WAAW,QAAQ,SAAS;IAC9B;AACF;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,SAAS,UAAU,SAAS,CAAC,cAAc,OAAO;QACpD,OAAO,gBAAgB;IACzB,OAAO;QACL,OAAO,qBAAqB;IAC9B;AACF;AAEA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,OAAO,QAAQ,qBAAqB;IACxC,IAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,WAAW,IAAI;IACxD,IAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,YAAY,IAAI;IAC1D,OAAO,WAAW,KAAK,WAAW;AACpC,EAAE,yEAAyE;AAC3E,sEAAsE;AAGtE,SAAS,iBAAiB,uBAAuB,EAAE,YAAY,EAAE,OAAO;IACtE,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IAEA,IAAI,0BAA0B,cAAc;IAC5C,IAAI,uBAAuB,cAAc,iBAAiB,gBAAgB;IAC1E,IAAI,kBAAkB,mBAAmB;IACzC,IAAI,OAAO,sBAAsB,yBAAyB,sBAAsB;IAChF,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,IAAI,UAAU;QACZ,GAAG;QACH,GAAG;IACL;IAEA,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,YAAY,kBAAkB,UAAU,sDAAsD;QAClG,eAAe,kBAAkB;YAC/B,SAAS,cAAc;QACzB;QAEA,IAAI,cAAc,eAAe;YAC/B,UAAU,sBAAsB,cAAc;YAC9C,QAAQ,CAAC,IAAI,aAAa,UAAU;YACpC,QAAQ,CAAC,IAAI,aAAa,SAAS;QACrC,OAAO,IAAI,iBAAiB;YAC1B,QAAQ,CAAC,GAAG,oBAAoB;QAClC;IACF;IAEA,OAAO;QACL,GAAG,KAAK,IAAI,GAAG,OAAO,UAAU,GAAG,QAAQ,CAAC;QAC5C,GAAG,KAAK,GAAG,GAAG,OAAO,SAAS,GAAG,QAAQ,CAAC;QAC1C,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;IACrB;AACF;AAEA,SAAS,MAAM,SAAS;IACtB,IAAI,MAAM,IAAI;IACd,IAAI,UAAU,IAAI;IAClB,IAAI,SAAS,EAAE;IACf,UAAU,OAAO,CAAC,SAAU,QAAQ;QAClC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE;IACzB,IAAI,4EAA4E;IAEhF,SAAS,KAAK,QAAQ;QACpB,QAAQ,GAAG,CAAC,SAAS,IAAI;QACzB,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,SAAS,QAAQ,IAAI,EAAE,EAAE,SAAS,gBAAgB,IAAI,EAAE;QACjF,SAAS,OAAO,CAAC,SAAU,GAAG;YAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,MAAM;gBACrB,IAAI,cAAc,IAAI,GAAG,CAAC;gBAE1B,IAAI,aAAa;oBACf,KAAK;gBACP;YACF;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,UAAU,OAAO,CAAC,SAAU,QAAQ;QAClC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,IAAI,GAAG;YAC/B,2BAA2B;YAC3B,KAAK;QACP;IACF;IACA,OAAO;AACT;AAEA,SAAS,eAAe,SAAS;IAC/B,8BAA8B;IAC9B,IAAI,mBAAmB,MAAM,YAAY,uBAAuB;IAEhE,OAAO,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,KAAK;QAC/C,OAAO,IAAI,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAU,QAAQ;YAC1D,OAAO,SAAS,KAAK,KAAK;QAC5B;IACF,GAAG,EAAE;AACP;AAEA,SAAS,SAAS,EAAE;IAClB,IAAI;IACJ,OAAO;QACL,IAAI,CAAC,SAAS;YACZ,UAAU,IAAI,QAAQ,SAAU,OAAO;gBACrC,QAAQ,OAAO,GAAG,IAAI,CAAC;oBACrB,UAAU;oBACV,QAAQ;gBACV;YACF;QACF;QAEA,OAAO;IACT;AACF;AAEA,SAAS,YAAY,SAAS;IAC5B,IAAI,SAAS,UAAU,MAAM,CAAC,SAAU,MAAM,EAAE,OAAO;QACrD,IAAI,WAAW,MAAM,CAAC,QAAQ,IAAI,CAAC;QACnC,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,SAAS;YACrE,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,OAAO,EAAE,QAAQ,OAAO;YAC5D,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,QAAQ,IAAI;QACrD,KAAK;QACL,OAAO;IACT,GAAG,CAAC,IAAI,sCAAsC;IAE9C,OAAO,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAU,GAAG;QAC1C,OAAO,MAAM,CAAC,IAAI;IACpB;AACF;AAEA,IAAI,kBAAkB;IACpB,WAAW;IACX,WAAW,EAAE;IACb,UAAU;AACZ;AAEA,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC9B;IAEA,OAAO,CAAC,KAAK,IAAI,CAAC,SAAU,OAAO;QACjC,OAAO,CAAC,CAAC,WAAW,OAAO,QAAQ,qBAAqB,KAAK,UAAU;IACzE;AACF;AAEA,SAAS,gBAAgB,gBAAgB;IACvC,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB,CAAC;IACtB;IAEA,IAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,gBAAgB,EAC1D,mBAAmB,0BAA0B,KAAK,IAAI,EAAE,GAAG,uBAC3D,yBAAyB,kBAAkB,cAAc,EACzD,iBAAiB,2BAA2B,KAAK,IAAI,kBAAkB;IAC3E,OAAO,SAAS,aAAa,SAAS,EAAE,MAAM,EAAE,OAAO;QACrD,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU;QACZ;QAEA,IAAI,QAAQ;YACV,WAAW;YACX,kBAAkB,EAAE;YACpB,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,iBAAiB;YAC5C,eAAe,CAAC;YAChB,UAAU;gBACR,WAAW;gBACX,QAAQ;YACV;YACA,YAAY,CAAC;YACb,QAAQ,CAAC;QACX;QACA,IAAI,mBAAmB,EAAE;QACzB,IAAI,cAAc;QAClB,IAAI,WAAW;YACb,OAAO;YACP,YAAY,SAAS,WAAW,gBAAgB;gBAC9C,IAAI,UAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;gBACzF;gBACA,MAAM,OAAO,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,MAAM,OAAO,EAAE;gBACjE,MAAM,aAAa,GAAG;oBACpB,WAAW,UAAU,aAAa,kBAAkB,aAAa,UAAU,cAAc,GAAG,kBAAkB,UAAU,cAAc,IAAI,EAAE;oBAC5I,QAAQ,kBAAkB;gBAC5B,GAAG,+DAA+D;gBAClE,aAAa;gBAEb,IAAI,mBAAmB,eAAe,YAAY,EAAE,CAAC,MAAM,CAAC,kBAAkB,MAAM,OAAO,CAAC,SAAS,KAAK,+BAA+B;gBAEzI,MAAM,gBAAgB,GAAG,iBAAiB,MAAM,CAAC,SAAU,CAAC;oBAC1D,OAAO,EAAE,OAAO;gBAClB;gBACA;gBACA,OAAO,SAAS,MAAM;YACxB;YACA,wEAAwE;YACxE,yEAAyE;YACzE,SAAS;YACT,yEAAyE;YACzE,wCAAwC;YACxC,aAAa,SAAS;gBACpB,IAAI,aAAa;oBACf;gBACF;gBAEA,IAAI,kBAAkB,MAAM,QAAQ,EAChC,YAAY,gBAAgB,SAAS,EACrC,SAAS,gBAAgB,MAAM,EAAE,kEAAkE;gBACvG,UAAU;gBAEV,IAAI,CAAC,iBAAiB,WAAW,SAAS;oBACxC;gBACF,EAAE,+DAA+D;gBAGjE,MAAM,KAAK,GAAG;oBACZ,WAAW,iBAAiB,WAAW,gBAAgB,SAAS,MAAM,OAAO,CAAC,QAAQ,KAAK;oBAC3F,QAAQ,cAAc;gBACxB,GAAG,oEAAoE;gBACvE,oEAAoE;gBACpE,uEAAuE;gBACvE,uEAAuE;gBACvE,kBAAkB;gBAElB,MAAM,KAAK,GAAG;gBACd,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,uEAAuE;gBAClH,wEAAwE;gBACxE,kDAAkD;gBAClD,sDAAsD;gBAEtD,MAAM,gBAAgB,CAAC,OAAO,CAAC,SAAU,QAAQ;oBAC/C,OAAO,MAAM,aAAa,CAAC,SAAS,IAAI,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,IAAI;gBAC7E;gBAEA,IAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,gBAAgB,CAAC,MAAM,EAAE,QAAS;oBAClE,IAAI,MAAM,KAAK,KAAK,MAAM;wBACxB,MAAM,KAAK,GAAG;wBACd,QAAQ,CAAC;wBACT;oBACF;oBAEA,IAAI,wBAAwB,MAAM,gBAAgB,CAAC,MAAM,EACrD,KAAK,sBAAsB,EAAE,EAC7B,yBAAyB,sBAAsB,OAAO,EACtD,WAAW,2BAA2B,KAAK,IAAI,CAAC,IAAI,wBACpD,OAAO,sBAAsB,IAAI;oBAErC,IAAI,OAAO,OAAO,YAAY;wBAC5B,QAAQ,GAAG;4BACT,OAAO;4BACP,SAAS;4BACT,MAAM;4BACN,UAAU;wBACZ,MAAM;oBACR;gBACF;YACF;YACA,yEAAyE;YACzE,yDAAyD;YACzD,QAAQ,SAAS;gBACf,OAAO,IAAI,QAAQ,SAAU,OAAO;oBAClC,SAAS,WAAW;oBACpB,QAAQ;gBACV;YACF;YACA,SAAS,SAAS;gBAChB;gBACA,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,iBAAiB,WAAW,SAAS;YACxC,OAAO;QACT;QAEA,SAAS,UAAU,CAAC,SAAS,IAAI,CAAC,SAAU,KAAK;YAC/C,IAAI,CAAC,eAAe,QAAQ,aAAa,EAAE;gBACzC,QAAQ,aAAa,CAAC;YACxB;QACF,IAAI,wEAAwE;QAC5E,2EAA2E;QAC3E,uEAAuE;QACvE,2EAA2E;QAC3E,OAAO;QAEP,SAAS;YACP,MAAM,gBAAgB,CAAC,OAAO,CAAC,SAAU,IAAI;gBAC3C,IAAI,OAAO,KAAK,IAAI,EAChB,eAAe,KAAK,OAAO,EAC3B,UAAU,iBAAiB,KAAK,IAAI,CAAC,IAAI,cACzC,SAAS,KAAK,MAAM;gBAExB,IAAI,OAAO,WAAW,YAAY;oBAChC,IAAI,YAAY,OAAO;wBACrB,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,SAAS;oBACX;oBAEA,IAAI,SAAS,SAAS,UAAU;oBAEhC,iBAAiB,IAAI,CAAC,aAAa;gBACrC;YACF;QACF;QAEA,SAAS;YACP,iBAAiB,OAAO,CAAC,SAAU,EAAE;gBACnC,OAAO;YACT;YACA,mBAAmB,EAAE;QACvB;QAEA,OAAO;IACT;AACF;AAEA,gFAAgF;AAChF,+FAA+F;AAC/F,MAAM,eAAe,gBAAgB;IACnC,kBAAkB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,QAAQ,YAAY,GAAG;AACvB,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/usePopper.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _react = require(\"react\");\nvar _dequal = require(\"dequal\");\nvar _useSafeState = _interopRequireDefault(require(\"@restart/hooks/useSafeState\"));\nvar _popper = require(\"./popper\");\nconst _excluded = [\"enabled\", \"placement\", \"strategy\", \"modifiers\"];\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nconst disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false,\n  phase: 'afterWrite',\n  fn: () => undefined\n};\n\n// until docjs supports type exports...\n\nconst ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: ({\n    state\n  }) => () => {\n    const {\n      reference,\n      popper\n    } = state.elements;\n    if ('removeAttribute' in reference) {\n      const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(id => id.trim() !== popper.id);\n      if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n    }\n  },\n  fn: ({\n    state\n  }) => {\n    var _popper$getAttribute;\n    const {\n      popper,\n      reference\n    } = state.elements;\n    const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      const ids = reference.getAttribute('aria-describedby');\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n      reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);\n    }\n  }\n};\nconst EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\nfunction usePopper(referenceElement, popperElement, _ref = {}) {\n  let {\n      enabled = true,\n      placement = 'bottom',\n      strategy = 'absolute',\n      modifiers = EMPTY_MODIFIERS\n    } = _ref,\n    config = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const prevModifiers = (0, _react.useRef)(modifiers);\n  const popperInstanceRef = (0, _react.useRef)();\n  const update = (0, _react.useCallback)(() => {\n    var _popperInstanceRef$cu;\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  const forceUpdate = (0, _react.useCallback)(() => {\n    var _popperInstanceRef$cu2;\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n  const [popperState, setState] = (0, _useSafeState.default)((0, _react.useState)({\n    placement,\n    update,\n    forceUpdate,\n    attributes: {},\n    styles: {\n      popper: {},\n      arrow: {}\n    }\n  }));\n  const updateModifier = (0, _react.useMemo)(() => ({\n    name: 'updateStateModifier',\n    enabled: true,\n    phase: 'write',\n    requires: ['computeStyles'],\n    fn: ({\n      state\n    }) => {\n      const styles = {};\n      const attributes = {};\n      Object.keys(state.elements).forEach(element => {\n        styles[element] = state.styles[element];\n        attributes[element] = state.attributes[element];\n      });\n      setState({\n        state,\n        styles,\n        attributes,\n        update,\n        forceUpdate,\n        placement: state.placement\n      });\n    }\n  }), [update, forceUpdate, setState]);\n  const nextModifiers = (0, _react.useMemo)(() => {\n    if (!(0, _dequal.dequal)(prevModifiers.current, modifiers)) {\n      prevModifiers.current = modifiers;\n    }\n    return prevModifiers.current;\n  }, [modifiers]);\n  (0, _react.useEffect)(() => {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, updateModifier, disabledApplyStylesModifier]\n    });\n  }, [strategy, placement, updateModifier, enabled, nextModifiers]);\n  (0, _react.useEffect)(() => {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n    popperInstanceRef.current = (0, _popper.createPopper)(referenceElement, popperElement, Object.assign({}, config, {\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, ariaDescribedByModifier, updateModifier]\n    }));\n    return () => {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(s => Object.assign({}, s, {\n          attributes: {},\n          styles: {\n            popper: {}\n          }\n        }));\n      }\n    };\n    // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\nvar _default = exports.default = usePopper;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI;AACJ,IAAI;AACJ,IAAI,gBAAgB;AACpB,IAAI;AACJ,MAAM,YAAY;IAAC;IAAW;IAAa;IAAY;CAAY;AACnE,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,MAAM,8BAA8B;IAClC,MAAM;IACN,SAAS;IACT,OAAO;IACP,IAAI,IAAM;AACZ;AAEA,uCAAuC;AAEvC,MAAM,0BAA0B;IAC9B,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ,CAAC,EACP,KAAK,EACN,GAAK;YACJ,MAAM,EACJ,SAAS,EACT,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,IAAI,qBAAqB,WAAW;gBAClC,MAAM,MAAM,CAAC,UAAU,YAAY,CAAC,uBAAuB,EAAE,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,KAAM,GAAG,IAAI,OAAO,OAAO,EAAE;gBAC9G,IAAI,CAAC,IAAI,MAAM,EAAE,UAAU,eAAe,CAAC;qBAAyB,UAAU,YAAY,CAAC,oBAAoB,IAAI,IAAI,CAAC;YAC1H;QACF;IACA,IAAI,CAAC,EACH,KAAK,EACN;QACC,IAAI;QACJ,MAAM,EACJ,MAAM,EACN,SAAS,EACV,GAAG,MAAM,QAAQ;QAClB,MAAM,OAAO,CAAC,uBAAuB,OAAO,YAAY,CAAC,OAAO,KAAK,OAAO,KAAK,IAAI,qBAAqB,WAAW;QACrH,IAAI,OAAO,EAAE,IAAI,SAAS,aAAa,kBAAkB,WAAW;YAClE,MAAM,MAAM,UAAU,YAAY,CAAC;YACnC,IAAI,OAAO,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG;gBACnD;YACF;YACA,UAAU,YAAY,CAAC,oBAAoB,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;QACpF;IACF;AACF;AACA,MAAM,kBAAkB,EAAE;AAC1B;;;;;;;;;;;;;;CAcC,GACD,SAAS,UAAU,gBAAgB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC3D,IAAI,EACA,UAAU,IAAI,EACd,YAAY,QAAQ,EACpB,WAAW,UAAU,EACrB,YAAY,eAAe,EAC5B,GAAG,MACJ,SAAS,8BAA8B,MAAM;IAC/C,MAAM,gBAAgB,CAAC,GAAG,OAAO,MAAM,EAAE;IACzC,MAAM,oBAAoB,CAAC,GAAG,OAAO,MAAM;IAC3C,MAAM,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE;QACrC,IAAI;QACJ,CAAC,wBAAwB,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM;IACrG,GAAG,EAAE;IACL,MAAM,cAAc,CAAC,GAAG,OAAO,WAAW,EAAE;QAC1C,IAAI;QACJ,CAAC,yBAAyB,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,uBAAuB,WAAW;IAC5G,GAAG,EAAE;IACL,MAAM,CAAC,aAAa,SAAS,GAAG,CAAC,GAAG,cAAc,OAAO,EAAE,CAAC,GAAG,OAAO,QAAQ,EAAE;QAC9E;QACA;QACA;QACA,YAAY,CAAC;QACb,QAAQ;YACN,QAAQ,CAAC;YACT,OAAO,CAAC;QACV;IACF;IACA,MAAM,iBAAiB,CAAC,GAAG,OAAO,OAAO,EAAE,IAAM,CAAC;YAChD,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;gBAAC;aAAgB;YAC3B,IAAI,CAAC,EACH,KAAK,EACN;gBACC,MAAM,SAAS,CAAC;gBAChB,MAAM,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE,OAAO,CAAC,CAAA;oBAClC,MAAM,CAAC,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ;oBACvC,UAAU,CAAC,QAAQ,GAAG,MAAM,UAAU,CAAC,QAAQ;gBACjD;gBACA,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA,WAAW,MAAM,SAAS;gBAC5B;YACF;QACF,CAAC,GAAG;QAAC;QAAQ;QAAa;KAAS;IACnC,MAAM,gBAAgB,CAAC,GAAG,OAAO,OAAO,EAAE;QACxC,IAAI,CAAC,CAAC,GAAG,QAAQ,MAAM,EAAE,cAAc,OAAO,EAAE,YAAY;YAC1D,cAAc,OAAO,GAAG;QAC1B;QACA,OAAO,cAAc,OAAO;IAC9B,GAAG;QAAC;KAAU;IACd,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,SAAS;QAC5C,kBAAkB,OAAO,CAAC,UAAU,CAAC;YACnC;YACA;YACA,WAAW;mBAAI;gBAAe;gBAAgB;aAA4B;QAC5E;IACF,GAAG;QAAC;QAAU;QAAW;QAAgB;QAAS;KAAc;IAChE,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,CAAC,WAAW,oBAAoB,QAAQ,iBAAiB,MAAM;YACjE,OAAO;QACT;QACA,kBAAkB,OAAO,GAAG,CAAC,GAAG,QAAQ,YAAY,EAAE,kBAAkB,eAAe,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;YAC/G;YACA;YACA,WAAW;mBAAI;gBAAe;gBAAyB;aAAe;QACxE;QACA,OAAO;YACL,IAAI,kBAAkB,OAAO,IAAI,MAAM;gBACrC,kBAAkB,OAAO,CAAC,OAAO;gBACjC,kBAAkB,OAAO,GAAG;gBAC5B,SAAS,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG;wBACjC,YAAY,CAAC;wBACb,QAAQ;4BACN,QAAQ,CAAC;wBACX;oBACF;YACF;QACF;IACA,+CAA+C;IAC/C,uDAAuD;IACzD,GAAG;QAAC;QAAS;QAAkB;KAAc;IAC7C,OAAO;AACT;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/useClickOutside.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.getRefTarget = exports.default = void 0;\nvar _contains = _interopRequireDefault(require(\"dom-helpers/contains\"));\nvar _listen = _interopRequireDefault(require(\"dom-helpers/listen\"));\nvar _ownerDocument = _interopRequireDefault(require(\"dom-helpers/ownerDocument\"));\nvar _react = require(\"react\");\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _warning = _interopRequireDefault(require(\"warning\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nconst noop = () => {};\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nconst getRefTarget = ref => ref && ('current' in ref ? ref.current : ref);\nexports.getRefTarget = getRefTarget;\nconst InitialTriggerEvents = {\n  click: 'mousedown',\n  mouseup: 'mousedown',\n  pointerup: 'pointerdown'\n};\n\n/**\n * The `useClickOutside` hook registers your callback on the document that fires\n * when a pointer event is registered outside of the provided ref or element.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onClickOutside\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useClickOutside(ref, onClickOutside = noop, {\n  disabled,\n  clickTrigger = 'click'\n} = {}) {\n  const preventMouseClickOutsideRef = (0, _react.useRef)(false);\n  const waitingForTrigger = (0, _react.useRef)(false);\n  const handleMouseCapture = (0, _react.useCallback)(e => {\n    const currentTarget = getRefTarget(ref);\n    (0, _warning.default)(!!currentTarget, 'ClickOutside captured a close event but does not have a ref to compare it to. ' + 'useClickOutside(), should be passed a ref that resolves to a DOM node');\n    preventMouseClickOutsideRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!(0, _contains.default)(currentTarget, e.target) || waitingForTrigger.current;\n    waitingForTrigger.current = false;\n  }, [ref]);\n  const handleInitialMouse = (0, _useEventCallback.default)(e => {\n    const currentTarget = getRefTarget(ref);\n    if (currentTarget && (0, _contains.default)(currentTarget, e.target)) {\n      waitingForTrigger.current = true;\n    } else {\n      // When clicking on scrollbars within current target, click events are not triggered, so this ref\n      // is never reset inside `handleMouseCapture`. This would cause a bug where it requires 2 clicks\n      // to close the overlay.\n      waitingForTrigger.current = false;\n    }\n  });\n  const handleMouse = (0, _useEventCallback.default)(e => {\n    if (!preventMouseClickOutsideRef.current) {\n      onClickOutside(e);\n    }\n  });\n  (0, _react.useEffect)(() => {\n    var _ownerWindow$event, _ownerWindow$parent;\n    if (disabled || ref == null) return undefined;\n    const doc = (0, _ownerDocument.default)(getRefTarget(ref));\n    const ownerWindow = doc.defaultView || window;\n\n    // Store the current event to avoid triggering handlers immediately\n    // For things rendered in an iframe, the event might originate on the parent window\n    // so we should fall back to that global event if the local one doesn't exist\n    // https://github.com/facebook/react/issues/20074\n    let currentEvent = (_ownerWindow$event = ownerWindow.event) != null ? _ownerWindow$event : (_ownerWindow$parent = ownerWindow.parent) == null ? void 0 : _ownerWindow$parent.event;\n    let removeInitialTriggerListener = null;\n    if (InitialTriggerEvents[clickTrigger]) {\n      removeInitialTriggerListener = (0, _listen.default)(doc, InitialTriggerEvents[clickTrigger], handleInitialMouse, true);\n    }\n\n    // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n    const removeMouseCaptureListener = (0, _listen.default)(doc, clickTrigger, handleMouseCapture, true);\n    const removeMouseListener = (0, _listen.default)(doc, clickTrigger, e => {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n      handleMouse(e);\n    });\n    let mobileSafariHackListeners = [];\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(el => (0, _listen.default)(el, 'mousemove', noop));\n    }\n    return () => {\n      removeInitialTriggerListener == null ? void 0 : removeInitialTriggerListener();\n      removeMouseCaptureListener();\n      removeMouseListener();\n      mobileSafariHackListeners.forEach(remove => remove());\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleInitialMouse, handleMouse]);\n}\nvar _default = exports.default = useClickOutside;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,YAAY,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC9C,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI,oBAAoB;AACxB,IAAI,WAAW;AACf,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,MAAM,OAAO,KAAO;AACpB,SAAS,iBAAiB,KAAK;IAC7B,OAAO,MAAM,MAAM,KAAK;AAC1B;AACA,SAAS,gBAAgB,KAAK;IAC5B,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ;AAC5E;AACA,MAAM,eAAe,CAAA,MAAO,OAAO,CAAC,aAAa,MAAM,IAAI,OAAO,GAAG,GAAG;AACxE,QAAQ,YAAY,GAAG;AACvB,MAAM,uBAAuB;IAC3B,OAAO;IACP,SAAS;IACT,WAAW;AACb;AAEA;;;;;;;;;CASC,GACD,SAAS,gBAAgB,GAAG,EAAE,iBAAiB,IAAI,EAAE,EACnD,QAAQ,EACR,eAAe,OAAO,EACvB,GAAG,CAAC,CAAC;IACJ,MAAM,8BAA8B,CAAC,GAAG,OAAO,MAAM,EAAE;IACvD,MAAM,oBAAoB,CAAC,GAAG,OAAO,MAAM,EAAE;IAC7C,MAAM,qBAAqB,CAAC,GAAG,OAAO,WAAW,EAAE,CAAA;QACjD,MAAM,gBAAgB,aAAa;QACnC,CAAC,GAAG,SAAS,OAAO,EAAE,CAAC,CAAC,eAAe,mFAAmF;QAC1H,4BAA4B,OAAO,GAAG,CAAC,iBAAiB,gBAAgB,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,OAAO,EAAE,eAAe,EAAE,MAAM,KAAK,kBAAkB,OAAO;QACpL,kBAAkB,OAAO,GAAG;IAC9B,GAAG;QAAC;KAAI;IACR,MAAM,qBAAqB,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAA;QACxD,MAAM,gBAAgB,aAAa;QACnC,IAAI,iBAAiB,CAAC,GAAG,UAAU,OAAO,EAAE,eAAe,EAAE,MAAM,GAAG;YACpE,kBAAkB,OAAO,GAAG;QAC9B,OAAO;YACL,iGAAiG;YACjG,gGAAgG;YAChG,wBAAwB;YACxB,kBAAkB,OAAO,GAAG;QAC9B;IACF;IACA,MAAM,cAAc,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAA;QACjD,IAAI,CAAC,4BAA4B,OAAO,EAAE;YACxC,eAAe;QACjB;IACF;IACA,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,oBAAoB;QACxB,IAAI,YAAY,OAAO,MAAM,OAAO;QACpC,MAAM,MAAM,CAAC,GAAG,eAAe,OAAO,EAAE,aAAa;QACrD,MAAM,cAAc,IAAI,WAAW,IAAI;QAEvC,mEAAmE;QACnE,mFAAmF;QACnF,6EAA6E;QAC7E,iDAAiD;QACjD,IAAI,eAAe,CAAC,qBAAqB,YAAY,KAAK,KAAK,OAAO,qBAAqB,CAAC,sBAAsB,YAAY,MAAM,KAAK,OAAO,KAAK,IAAI,oBAAoB,KAAK;QAClL,IAAI,+BAA+B;QACnC,IAAI,oBAAoB,CAAC,aAAa,EAAE;YACtC,+BAA+B,CAAC,GAAG,QAAQ,OAAO,EAAE,KAAK,oBAAoB,CAAC,aAAa,EAAE,oBAAoB;QACnH;QAEA,wEAAwE;QACxE,wEAAwE;QACxE,kDAAkD;QAClD,MAAM,6BAA6B,CAAC,GAAG,QAAQ,OAAO,EAAE,KAAK,cAAc,oBAAoB;QAC/F,MAAM,sBAAsB,CAAC,GAAG,QAAQ,OAAO,EAAE,KAAK,cAAc,CAAA;YAClE,+EAA+E;YAC/E,IAAI,MAAM,cAAc;gBACtB,eAAe;gBACf;YACF;YACA,YAAY;QACd;QACA,IAAI,4BAA4B,EAAE;QAClC,IAAI,kBAAkB,IAAI,eAAe,EAAE;YACzC,4BAA4B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA,KAAM,CAAC,GAAG,QAAQ,OAAO,EAAE,IAAI,aAAa;QAC/G;QACA,OAAO;YACL,gCAAgC,OAAO,KAAK,IAAI;YAChD;YACA;YACA,0BAA0B,OAAO,CAAC,CAAA,SAAU;QAC9C;IACF,GAAG;QAAC;QAAK;QAAU;QAAc;QAAoB;QAAoB;KAAY;AACvF;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/mergeOptionsWithPopperConfig.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = mergeOptionsWithPopperConfig;\nexports.toModifierArray = toModifierArray;\nexports.toModifierMap = toModifierMap;\nfunction toModifierMap(modifiers) {\n  const result = {};\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  }\n\n  // eslint-disable-next-line no-unused-expressions\n  modifiers == null ? void 0 : modifiers.forEach(m => {\n    result[m.name] = m;\n  });\n  return result;\n}\nfunction toModifierArray(map = {}) {\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(k => {\n    map[k].name = k;\n    return map[k];\n  });\n}\nfunction mergeOptionsWithPopperConfig({\n  enabled,\n  enableEvents,\n  placement,\n  flip,\n  offset,\n  fixed,\n  containerPadding,\n  arrowElement,\n  popperConfig = {}\n}) {\n  var _modifiers$eventListe, _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n  const modifiers = toModifierMap(popperConfig.modifiers);\n  return Object.assign({}, popperConfig, {\n    placement,\n    enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray(Object.assign({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents,\n        options: (_modifiers$eventListe = modifiers.eventListeners) == null ? void 0 : _modifiers$eventListe.options\n      },\n      preventOverflow: Object.assign({}, modifiers.preventOverflow, {\n        options: containerPadding ? Object.assign({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: Object.assign({\n          offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: Object.assign({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: Object.assign({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: Object.assign({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG;AAClB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,aAAa,GAAG;AACxB,SAAS,cAAc,SAAS;IAC9B,MAAM,SAAS,CAAC;IAChB,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;QAC7B,OAAO,aAAa;IACtB;IAEA,iDAAiD;IACjD,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,CAAC,CAAA;QAC7C,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG;IACnB;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,MAAM,CAAC,CAAC;IAC/B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;IAC/B,OAAO,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAC1B,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG;QACd,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AACA,SAAS,6BAA6B,EACpC,OAAO,EACP,YAAY,EACZ,SAAS,EACT,IAAI,EACJ,MAAM,EACN,KAAK,EACL,gBAAgB,EAChB,YAAY,EACZ,eAAe,CAAC,CAAC,EAClB;IACC,IAAI,uBAAuB,uBAAuB,wBAAwB,mBAAmB;IAC7F,MAAM,YAAY,cAAc,aAAa,SAAS;IACtD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;QACrC;QACA;QACA,UAAU,QAAQ,UAAU,aAAa,QAAQ;QACjD,WAAW,gBAAgB,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW;YACtD,gBAAgB;gBACd,SAAS;gBACT,SAAS,CAAC,wBAAwB,UAAU,cAAc,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO;YAC9G;YACA,iBAAiB,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,eAAe,EAAE;gBAC5D,SAAS,mBAAmB,OAAO,MAAM,CAAC;oBACxC,SAAS;gBACX,GAAG,CAAC,wBAAwB,UAAU,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,IAAI,CAAC,yBAAyB,UAAU,eAAe,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO;YACnN;YACA,QAAQ;gBACN,SAAS,OAAO,MAAM,CAAC;oBACrB;gBACF,GAAG,CAAC,oBAAoB,UAAU,MAAM,KAAK,OAAO,KAAK,IAAI,kBAAkB,OAAO;YACxF;YACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,KAAK,EAAE;gBACxC,SAAS,CAAC,CAAC;gBACX,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,mBAAmB,UAAU,KAAK,KAAK,OAAO,KAAK,IAAI,iBAAiB,OAAO,EAAE;oBAC3G,SAAS;gBACX;YACF;YACA,MAAM,OAAO,MAAM,CAAC;gBAClB,SAAS,CAAC,CAAC;YACb,GAAG,UAAU,IAAI;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/DropdownMenu.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nexports.useDropdownMenu = useDropdownMenu;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _useCallbackRef = _interopRequireDefault(require(\"@restart/hooks/useCallbackRef\"));\nvar _DropdownContext = _interopRequireDefault(require(\"./DropdownContext\"));\nvar _usePopper = _interopRequireDefault(require(\"./usePopper\"));\nvar _useClickOutside = _interopRequireDefault(require(\"./useClickOutside\"));\nvar _mergeOptionsWithPopperConfig = _interopRequireDefault(require(\"./mergeOptionsWithPopperConfig\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"children\", \"usePopper\"];\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nconst noop = () => {};\n\n/**\n * @memberOf Dropdown\n * @param {object}  options\n * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection\n * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle\n * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`\n * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.\n * @param {string}  options.rootCloseEvent The pointer event to listen for when determining \"clicks outside\" the menu for triggering a close.\n * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.\n */\nfunction useDropdownMenu(options = {}) {\n  const context = (0, _react.useContext)(_DropdownContext.default);\n  const [arrowElement, attachArrowRef] = (0, _useCallbackRef.default)();\n  const hasShownRef = (0, _react.useRef)(false);\n  const {\n    flip,\n    offset,\n    rootCloseEvent,\n    fixed = false,\n    placement: placementOverride,\n    popperConfig = {},\n    enableEventListeners = true,\n    usePopper: shouldUsePopper = !!context\n  } = options;\n  const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;\n  if (show && !hasShownRef.current) {\n    hasShownRef.current = true;\n  }\n  const handleClose = e => {\n    context == null ? void 0 : context.toggle(false, e);\n  };\n  const {\n    placement,\n    setMenu,\n    menuElement,\n    toggleElement\n  } = context || {};\n  const popper = (0, _usePopper.default)(toggleElement, menuElement, (0, _mergeOptionsWithPopperConfig.default)({\n    placement: placementOverride || placement || 'bottom-start',\n    enabled: shouldUsePopper,\n    enableEvents: enableEventListeners == null ? show : enableEventListeners,\n    offset,\n    flip,\n    fixed,\n    arrowElement,\n    popperConfig\n  }));\n  const menuProps = Object.assign({\n    ref: setMenu || noop,\n    'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id\n  }, popper.attributes.popper, {\n    style: popper.styles.popper\n  });\n  const metadata = {\n    show,\n    placement,\n    hasShown: hasShownRef.current,\n    toggle: context == null ? void 0 : context.toggle,\n    popper: shouldUsePopper ? popper : null,\n    arrowProps: shouldUsePopper ? Object.assign({\n      ref: attachArrowRef\n    }, popper.attributes.arrow, {\n      style: popper.styles.arrow\n    }) : {}\n  };\n  (0, _useClickOutside.default)(menuElement, handleClose, {\n    clickTrigger: rootCloseEvent,\n    disabled: !show\n  });\n  return [menuProps, metadata];\n}\n/**\n * Also exported as `<Dropdown.Menu>` from `Dropdown`.\n *\n * @displayName DropdownMenu\n * @memberOf Dropdown\n */\nfunction DropdownMenu(_ref) {\n  let {\n      children,\n      usePopper: usePopperProp = true\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useDropdownMenu(Object.assign({}, options, {\n    usePopper: usePopperProp\n  }));\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownMenu.displayName = 'DropdownMenu';\n\n/** @component */\nvar _default = exports.default = DropdownMenu;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,eAAe,GAAG;AAC1B,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,mBAAmB;AACvB,IAAI,gCAAgC;AACpC,IAAI;AACJ,MAAM,YAAY;IAAC;IAAY;CAAY;AAC3C,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,MAAM,OAAO,KAAO;AAEpB;;;;;;;;;CASC,GACD,SAAS,gBAAgB,UAAU,CAAC,CAAC;IACnC,MAAM,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE,iBAAiB,OAAO;IAC/D,MAAM,CAAC,cAAc,eAAe,GAAG,CAAC,GAAG,gBAAgB,OAAO;IAClE,MAAM,cAAc,CAAC,GAAG,OAAO,MAAM,EAAE;IACvC,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,cAAc,EACd,QAAQ,KAAK,EACb,WAAW,iBAAiB,EAC5B,eAAe,CAAC,CAAC,EACjB,uBAAuB,IAAI,EAC3B,WAAW,kBAAkB,CAAC,CAAC,OAAO,EACvC,GAAG;IACJ,MAAM,OAAO,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,IAAI,KAAK,OAAO,CAAC,CAAC,QAAQ,IAAI,GAAG,QAAQ,IAAI;IAC9F,IAAI,QAAQ,CAAC,YAAY,OAAO,EAAE;QAChC,YAAY,OAAO,GAAG;IACxB;IACA,MAAM,cAAc,CAAA;QAClB,WAAW,OAAO,KAAK,IAAI,QAAQ,MAAM,CAAC,OAAO;IACnD;IACA,MAAM,EACJ,SAAS,EACT,OAAO,EACP,WAAW,EACX,aAAa,EACd,GAAG,WAAW,CAAC;IAChB,MAAM,SAAS,CAAC,GAAG,WAAW,OAAO,EAAE,eAAe,aAAa,CAAC,GAAG,8BAA8B,OAAO,EAAE;QAC5G,WAAW,qBAAqB,aAAa;QAC7C,SAAS;QACT,cAAc,wBAAwB,OAAO,OAAO;QACpD;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,YAAY,OAAO,MAAM,CAAC;QAC9B,KAAK,WAAW;QAChB,mBAAmB,iBAAiB,OAAO,KAAK,IAAI,cAAc,EAAE;IACtE,GAAG,OAAO,UAAU,CAAC,MAAM,EAAE;QAC3B,OAAO,OAAO,MAAM,CAAC,MAAM;IAC7B;IACA,MAAM,WAAW;QACf;QACA;QACA,UAAU,YAAY,OAAO;QAC7B,QAAQ,WAAW,OAAO,KAAK,IAAI,QAAQ,MAAM;QACjD,QAAQ,kBAAkB,SAAS;QACnC,YAAY,kBAAkB,OAAO,MAAM,CAAC;YAC1C,KAAK;QACP,GAAG,OAAO,UAAU,CAAC,KAAK,EAAE;YAC1B,OAAO,OAAO,MAAM,CAAC,KAAK;QAC5B,KAAK,CAAC;IACR;IACA,CAAC,GAAG,iBAAiB,OAAO,EAAE,aAAa,aAAa;QACtD,cAAc;QACd,UAAU,CAAC;IACb;IACA,OAAO;QAAC;QAAW;KAAS;AAC9B;AACA;;;;;CAKC,GACD,SAAS,aAAa,IAAI;IACxB,IAAI,EACA,QAAQ,EACR,WAAW,gBAAgB,IAAI,EAChC,GAAG,MACJ,UAAU,8BAA8B,MAAM;IAChD,MAAM,CAAC,OAAO,KAAK,GAAG,gBAAgB,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QAC/D,WAAW;IACb;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,YAAY,QAAQ,EAAE;QAC7D,UAAU,SAAS,OAAO;IAC5B;AACF;AACA,aAAa,WAAW,GAAG;AAE3B,eAAe,GACf,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/ssr.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nvar _ssr = require(\"@react-aria/ssr\");\nexports.useSSRSafeId = _ssr.useSSRSafeId;\nexports.useIsSSR = _ssr.useIsSSR;\nexports.SSRProvider = _ssr.SSRProvider;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,IAAI;AACJ,QAAQ,YAAY,GAAG,KAAK,YAAY;AACxC,QAAQ,QAAQ,GAAG,KAAK,QAAQ;AAChC,QAAQ,WAAW,GAAG,KAAK,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/DropdownToggle.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.isRoleMenu = exports.default = void 0;\nexports.useDropdownToggle = useDropdownToggle;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _ssr = require(\"./ssr\");\nvar _DropdownContext = _interopRequireDefault(require(\"./DropdownContext\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nconst isRoleMenu = el => {\n  var _el$getAttribute;\n  return ((_el$getAttribute = el.getAttribute('role')) == null ? void 0 : _el$getAttribute.toLowerCase()) === 'menu';\n};\nexports.isRoleMenu = isRoleMenu;\nconst noop = () => {};\n\n/**\n * Wires up Dropdown toggle functionality, returning a set a props to attach\n * to the element that functions as the dropdown toggle (generally a button).\n *\n * @memberOf Dropdown\n */\nfunction useDropdownToggle() {\n  const id = (0, _ssr.useSSRSafeId)();\n  const {\n    show = false,\n    toggle = noop,\n    setToggle,\n    menuElement\n  } = (0, _react.useContext)(_DropdownContext.default) || {};\n  const handleClick = (0, _react.useCallback)(e => {\n    toggle(!show, e);\n  }, [show, toggle]);\n  const props = {\n    id,\n    ref: setToggle || noop,\n    onClick: handleClick,\n    'aria-expanded': !!show\n  };\n\n  // This is maybe better down in an effect, but\n  // the component is going to update anyway when the menu element\n  // is set so might return new props.\n  if (menuElement && isRoleMenu(menuElement)) {\n    props['aria-haspopup'] = true;\n  }\n  return [props, {\n    show,\n    toggle\n  }];\n}\n/**\n * Also exported as `<Dropdown.Toggle>` from `Dropdown`.\n *\n * @displayName DropdownToggle\n * @memberOf Dropdown\n */\nfunction DropdownToggle({\n  children\n}) {\n  const [props, meta] = useDropdownToggle();\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownToggle.displayName = 'DropdownToggle';\n\n/** @component */\nvar _default = exports.default = DropdownToggle;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,UAAU,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC5C,QAAQ,iBAAiB,GAAG;AAC5B,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,mBAAmB;AACvB,IAAI;AACJ,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,MAAM,aAAa,CAAA;IACjB,IAAI;IACJ,OAAO,CAAC,CAAC,mBAAmB,GAAG,YAAY,CAAC,OAAO,KAAK,OAAO,KAAK,IAAI,iBAAiB,WAAW,EAAE,MAAM;AAC9G;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,OAAO,KAAO;AAEpB;;;;;CAKC,GACD,SAAS;IACP,MAAM,KAAK,CAAC,GAAG,KAAK,YAAY;IAChC,MAAM,EACJ,OAAO,KAAK,EACZ,SAAS,IAAI,EACb,SAAS,EACT,WAAW,EACZ,GAAG,CAAC,GAAG,OAAO,UAAU,EAAE,iBAAiB,OAAO,KAAK,CAAC;IACzD,MAAM,cAAc,CAAC,GAAG,OAAO,WAAW,EAAE,CAAA;QAC1C,OAAO,CAAC,MAAM;IAChB,GAAG;QAAC;QAAM;KAAO;IACjB,MAAM,QAAQ;QACZ;QACA,KAAK,aAAa;QAClB,SAAS;QACT,iBAAiB,CAAC,CAAC;IACrB;IAEA,8CAA8C;IAC9C,gEAAgE;IAChE,oCAAoC;IACpC,IAAI,eAAe,WAAW,cAAc;QAC1C,KAAK,CAAC,gBAAgB,GAAG;IAC3B;IACA,OAAO;QAAC;QAAO;YACb;YACA;QACF;KAAE;AACJ;AACA;;;;;CAKC,GACD,SAAS,eAAe,EACtB,QAAQ,EACT;IACC,MAAM,CAAC,OAAO,KAAK,GAAG;IACtB,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,YAAY,QAAQ,EAAE;QAC7D,UAAU,SAAS,OAAO;IAC5B;AACF;AACA,eAAe,WAAW,GAAG;AAE7B,eAAe,GACf,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/NavContext.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nconst NavContext = /*#__PURE__*/React.createContext(null);\nNavContext.displayName = 'NavContext';\nvar _default = exports.default = NavContext;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,QAAQ;AACZ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,MAAM,aAAa,WAAW,GAAE,MAAM,aAAa,CAAC;AACpD,WAAW,WAAW,GAAG;AACzB,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/Button.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nexports.isTrivialHref = isTrivialHref;\nexports.useButtonProps = useButtonProps;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"as\", \"disabled\"];\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nfunction isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\nfunction useButtonProps({\n  tagName,\n  disabled,\n  href,\n  target,\n  rel,\n  role,\n  onClick,\n  tabIndex = 0,\n  type\n}) {\n  if (!tagName) {\n    if (href != null || target != null || rel != null) {\n      tagName = 'a';\n    } else {\n      tagName = 'button';\n    }\n  }\n  const meta = {\n    tagName\n  };\n  if (tagName === 'button') {\n    return [{\n      type: type || 'button',\n      disabled\n    }, meta];\n  }\n  const handleClick = event => {\n    if (disabled || tagName === 'a' && isTrivialHref(href)) {\n      event.preventDefault();\n    }\n    if (disabled) {\n      event.stopPropagation();\n      return;\n    }\n    onClick == null ? void 0 : onClick(event);\n  };\n  const handleKeyDown = event => {\n    if (event.key === ' ') {\n      event.preventDefault();\n      handleClick(event);\n    }\n  };\n  if (tagName === 'a') {\n    // Ensure there's a href so Enter can trigger anchor button.\n    href || (href = '#');\n    if (disabled) {\n      href = undefined;\n    }\n  }\n  return [{\n    role: role != null ? role : 'button',\n    // explicitly undefined so that it overrides the props disabled in a spread\n    // e.g. <Tag {...props} {...hookProps} />\n    disabled: undefined,\n    tabIndex: disabled ? undefined : tabIndex,\n    href,\n    target: tagName === 'a' ? target : undefined,\n    'aria-disabled': !disabled ? undefined : disabled,\n    rel: tagName === 'a' ? rel : undefined,\n    onClick: handleClick,\n    onKeyDown: handleKeyDown\n  }, meta];\n}\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: asProp,\n      disabled\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps, {\n    tagName: Component\n  }] = useButtonProps(Object.assign({\n    tagName: asProp,\n    disabled\n  }, props));\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, Object.assign({}, props, buttonProps, {\n    ref: ref\n  }));\n});\nButton.displayName = 'Button';\nvar _default = exports.default = Button;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,aAAa,GAAG;AACxB,QAAQ,cAAc,GAAG;AACzB,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,YAAY;IAAC;IAAM;CAAW;AACpC,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,SAAS,cAAc,IAAI;IACzB,OAAO,CAAC,QAAQ,KAAK,IAAI,OAAO;AAClC;AACA,SAAS,eAAe,EACtB,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,IAAI,EACJ,OAAO,EACP,WAAW,CAAC,EACZ,IAAI,EACL;IACC,IAAI,CAAC,SAAS;QACZ,IAAI,QAAQ,QAAQ,UAAU,QAAQ,OAAO,MAAM;YACjD,UAAU;QACZ,OAAO;YACL,UAAU;QACZ;IACF;IACA,MAAM,OAAO;QACX;IACF;IACA,IAAI,YAAY,UAAU;QACxB,OAAO;YAAC;gBACN,MAAM,QAAQ;gBACd;YACF;YAAG;SAAK;IACV;IACA,MAAM,cAAc,CAAA;QAClB,IAAI,YAAY,YAAY,OAAO,cAAc,OAAO;YACtD,MAAM,cAAc;QACtB;QACA,IAAI,UAAU;YACZ,MAAM,eAAe;YACrB;QACF;QACA,WAAW,OAAO,KAAK,IAAI,QAAQ;IACrC;IACA,MAAM,gBAAgB,CAAA;QACpB,IAAI,MAAM,GAAG,KAAK,KAAK;YACrB,MAAM,cAAc;YACpB,YAAY;QACd;IACF;IACA,IAAI,YAAY,KAAK;QACnB,4DAA4D;QAC5D,QAAQ,CAAC,OAAO,GAAG;QACnB,IAAI,UAAU;YACZ,OAAO;QACT;IACF;IACA,OAAO;QAAC;YACN,MAAM,QAAQ,OAAO,OAAO;YAC5B,2EAA2E;YAC3E,yCAAyC;YACzC,UAAU;YACV,UAAU,WAAW,YAAY;YACjC;YACA,QAAQ,YAAY,MAAM,SAAS;YACnC,iBAAiB,CAAC,WAAW,YAAY;YACzC,KAAK,YAAY,MAAM,MAAM;YAC7B,SAAS;YACT,WAAW;QACb;QAAG;KAAK;AACV;AACA,MAAM,SAAS,WAAW,GAAE,MAAM,UAAU,CAAC,CAAC,MAAM;IAClD,IAAI,EACA,IAAI,MAAM,EACV,QAAQ,EACT,GAAG,MACJ,QAAQ,8BAA8B,MAAM;IAC9C,MAAM,CAAC,aAAa,EAClB,SAAS,SAAS,EACnB,CAAC,GAAG,eAAe,OAAO,MAAM,CAAC;QAChC,SAAS;QACT;IACF,GAAG;IACH,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,aAAa;QACxF,KAAK;IACP;AACF;AACA,OAAO,WAAW,GAAG;AACrB,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/DropdownItem.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nexports.useDropdownItem = useDropdownItem;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _SelectableContext = _interopRequireWildcard(require(\"./SelectableContext\"));\nvar _NavContext = _interopRequireDefault(require(\"./NavContext\"));\nvar _Button = _interopRequireDefault(require(\"./Button\"));\nvar _DataKey = require(\"./DataKey\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"eventKey\", \"disabled\", \"onClick\", \"active\", \"as\"];\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\n/**\n * Create a dropdown item. Returns a set of props for the dropdown item component\n * including an `onClick` handler that prevents selection when the item is disabled\n */\nfunction useDropdownItem({\n  key,\n  href,\n  active,\n  disabled,\n  onClick\n}) {\n  const onSelectCtx = (0, _react.useContext)(_SelectableContext.default);\n  const navContext = (0, _react.useContext)(_NavContext.default);\n  const {\n    activeKey\n  } = navContext || {};\n  const eventKey = (0, _SelectableContext.makeEventKey)(key, href);\n  const isActive = active == null && key != null ? (0, _SelectableContext.makeEventKey)(activeKey) === eventKey : active;\n  const handleClick = (0, _useEventCallback.default)(event => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(event);\n    if (onSelectCtx && !event.isPropagationStopped()) {\n      onSelectCtx(eventKey, event);\n    }\n  });\n  return [{\n    onClick: handleClick,\n    'aria-disabled': disabled || undefined,\n    'aria-selected': isActive,\n    [(0, _DataKey.dataAttr)('dropdown-item')]: ''\n  }, {\n    isActive\n  }];\n}\nconst DropdownItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      eventKey,\n      disabled,\n      onClick,\n      active,\n      as: Component = _Button.default\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [dropdownItemProps] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, Object.assign({}, props, {\n    ref: ref\n  }, dropdownItemProps));\n});\nDropdownItem.displayName = 'DropdownItem';\nvar _default = exports.default = DropdownItem;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,eAAe,GAAG;AAC1B,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,oBAAoB;AACxB,IAAI,qBAAqB;AACzB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI;AACJ,IAAI;AACJ,MAAM,YAAY;IAAC;IAAY;IAAY;IAAW;IAAU;CAAK;AACrE,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM;;;CAGC,GACD,SAAS,gBAAgB,EACvB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,OAAO,EACR;IACC,MAAM,cAAc,CAAC,GAAG,OAAO,UAAU,EAAE,mBAAmB,OAAO;IACrE,MAAM,aAAa,CAAC,GAAG,OAAO,UAAU,EAAE,YAAY,OAAO;IAC7D,MAAM,EACJ,SAAS,EACV,GAAG,cAAc,CAAC;IACnB,MAAM,WAAW,CAAC,GAAG,mBAAmB,YAAY,EAAE,KAAK;IAC3D,MAAM,WAAW,UAAU,QAAQ,OAAO,OAAO,CAAC,GAAG,mBAAmB,YAAY,EAAE,eAAe,WAAW;IAChH,MAAM,cAAc,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAA;QACjD,IAAI,UAAU;QACd,WAAW,OAAO,KAAK,IAAI,QAAQ;QACnC,IAAI,eAAe,CAAC,MAAM,oBAAoB,IAAI;YAChD,YAAY,UAAU;QACxB;IACF;IACA,OAAO;QAAC;YACN,SAAS;YACT,iBAAiB,YAAY;YAC7B,iBAAiB;YACjB,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,iBAAiB,EAAE;QAC7C;QAAG;YACD;QACF;KAAE;AACJ;AACA,MAAM,eAAe,WAAW,GAAE,MAAM,UAAU,CAAC,CAAC,MAAM;IACxD,IAAI,EACA,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,IAAI,YAAY,QAAQ,OAAO,EAChC,GAAG,MACJ,QAAQ,8BAA8B,MAAM;IAC9C,MAAM,CAAC,kBAAkB,GAAG,gBAAgB;QAC1C,KAAK;QACL,MAAM,MAAM,IAAI;QAChB;QACA;QACA;IACF;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QAC3E,KAAK;IACP,GAAG;AACL;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/Dropdown.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _querySelectorAll = _interopRequireDefault(require(\"dom-helpers/querySelectorAll\"));\nvar _addEventListener = _interopRequireDefault(require(\"dom-helpers/addEventListener\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _uncontrollable = require(\"uncontrollable\");\nvar _usePrevious = _interopRequireDefault(require(\"@restart/hooks/usePrevious\"));\nvar _useForceUpdate = _interopRequireDefault(require(\"@restart/hooks/useForceUpdate\"));\nvar _useEventListener = _interopRequireDefault(require(\"@restart/hooks/useEventListener\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@restart/hooks/useEventCallback\"));\nvar _DropdownContext = _interopRequireDefault(require(\"./DropdownContext\"));\nvar _DropdownMenu = _interopRequireDefault(require(\"./DropdownMenu\"));\nvar _DropdownToggle = _interopRequireWildcard(require(\"./DropdownToggle\"));\nvar _DropdownItem = _interopRequireDefault(require(\"./DropdownItem\"));\nvar _SelectableContext = _interopRequireDefault(require(\"./SelectableContext\"));\nvar _DataKey = require(\"./DataKey\");\nvar _useWindow = _interopRequireDefault(require(\"./useWindow\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction useRefWithUpdate() {\n  const forceUpdate = (0, _useForceUpdate.default)();\n  const ref = (0, _react.useRef)(null);\n  const attachRef = (0, _react.useCallback)(element => {\n    ref.current = element;\n    // ensure that a menu set triggers an update for consumers\n    forceUpdate();\n  }, [forceUpdate]);\n  return [ref, attachRef];\n}\n\n/**\n * @displayName Dropdown\n * @public\n */\nfunction Dropdown({\n  defaultShow,\n  show: rawShow,\n  onSelect,\n  onToggle: rawOnToggle,\n  itemSelector = `* [${(0, _DataKey.dataAttr)('dropdown-item')}]`,\n  focusFirstItemOnShow,\n  placement = 'bottom-start',\n  children\n}) {\n  const window = (0, _useWindow.default)();\n  const [show, onToggle] = (0, _uncontrollable.useUncontrolledProp)(rawShow, defaultShow, rawOnToggle);\n\n  // We use normal refs instead of useCallbackRef in order to populate the\n  // the value as quickly as possible, otherwise the effect to focus the element\n  // may run before the state value is set\n  const [menuRef, setMenu] = useRefWithUpdate();\n  const menuElement = menuRef.current;\n  const [toggleRef, setToggle] = useRefWithUpdate();\n  const toggleElement = toggleRef.current;\n  const lastShow = (0, _usePrevious.default)(show);\n  const lastSourceEvent = (0, _react.useRef)(null);\n  const focusInDropdown = (0, _react.useRef)(false);\n  const onSelectCtx = (0, _react.useContext)(_SelectableContext.default);\n  const toggle = (0, _react.useCallback)((nextShow, event, source = event == null ? void 0 : event.type) => {\n    onToggle(nextShow, {\n      originalEvent: event,\n      source\n    });\n  }, [onToggle]);\n  const handleSelect = (0, _useEventCallback.default)((key, event) => {\n    onSelect == null ? void 0 : onSelect(key, event);\n    toggle(false, event, 'select');\n    if (!event.isPropagationStopped()) {\n      onSelectCtx == null ? void 0 : onSelectCtx(key, event);\n    }\n  });\n  const context = (0, _react.useMemo)(() => ({\n    toggle,\n    placement,\n    show,\n    menuElement,\n    toggleElement,\n    setMenu,\n    setToggle\n  }), [toggle, placement, show, menuElement, toggleElement, setMenu, setToggle]);\n  if (menuElement && lastShow && !show) {\n    focusInDropdown.current = menuElement.contains(menuElement.ownerDocument.activeElement);\n  }\n  const focusToggle = (0, _useEventCallback.default)(() => {\n    if (toggleElement && toggleElement.focus) {\n      toggleElement.focus();\n    }\n  });\n  const maybeFocusFirst = (0, _useEventCallback.default)(() => {\n    const type = lastSourceEvent.current;\n    let focusType = focusFirstItemOnShow;\n    if (focusType == null) {\n      focusType = menuRef.current && (0, _DropdownToggle.isRoleMenu)(menuRef.current) ? 'keyboard' : false;\n    }\n    if (focusType === false || focusType === 'keyboard' && !/^key.+$/.test(type)) {\n      return;\n    }\n    const first = (0, _querySelectorAll.default)(menuRef.current, itemSelector)[0];\n    if (first && first.focus) first.focus();\n  });\n  (0, _react.useEffect)(() => {\n    if (show) maybeFocusFirst();else if (focusInDropdown.current) {\n      focusInDropdown.current = false;\n      focusToggle();\n    }\n    // only `show` should be changing\n  }, [show, focusInDropdown, focusToggle, maybeFocusFirst]);\n  (0, _react.useEffect)(() => {\n    lastSourceEvent.current = null;\n  });\n  const getNextFocusedChild = (current, offset) => {\n    if (!menuRef.current) return null;\n    const items = (0, _querySelectorAll.default)(menuRef.current, itemSelector);\n    let index = items.indexOf(current) + offset;\n    index = Math.max(0, Math.min(index, items.length));\n    return items[index];\n  };\n  (0, _useEventListener.default)((0, _react.useCallback)(() => window.document, [window]), 'keydown', event => {\n    var _menuRef$current, _toggleRef$current;\n    const {\n      key\n    } = event;\n    const target = event.target;\n    const fromMenu = (_menuRef$current = menuRef.current) == null ? void 0 : _menuRef$current.contains(target);\n    const fromToggle = (_toggleRef$current = toggleRef.current) == null ? void 0 : _toggleRef$current.contains(target);\n\n    // Second only to https://github.com/twbs/bootstrap/blob/8cfbf6933b8a0146ac3fbc369f19e520bd1ebdac/js/src/dropdown.js#L400\n    // in inscrutability\n    const isInput = /input|textarea/i.test(target.tagName);\n    if (isInput && (key === ' ' || key !== 'Escape' && fromMenu || key === 'Escape' && target.type === 'search')) {\n      return;\n    }\n    if (!fromMenu && !fromToggle) {\n      return;\n    }\n    if (key === 'Tab' && (!menuRef.current || !show)) {\n      return;\n    }\n    lastSourceEvent.current = event.type;\n    const meta = {\n      originalEvent: event,\n      source: event.type\n    };\n    switch (key) {\n      case 'ArrowUp':\n        {\n          const next = getNextFocusedChild(target, -1);\n          if (next && next.focus) next.focus();\n          event.preventDefault();\n          return;\n        }\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!show) {\n          onToggle(true, meta);\n        } else {\n          const next = getNextFocusedChild(target, 1);\n          if (next && next.focus) next.focus();\n        }\n        return;\n      case 'Tab':\n        // on keydown the target is the element being tabbed FROM, we need that\n        // to know if this event is relevant to this dropdown (e.g. in this menu).\n        // On `keyup` the target is the element being tagged TO which we use to check\n        // if focus has left the menu\n        (0, _addEventListener.default)(target.ownerDocument, 'keyup', e => {\n          var _menuRef$current2;\n          if (e.key === 'Tab' && !e.target || !((_menuRef$current2 = menuRef.current) != null && _menuRef$current2.contains(e.target))) {\n            onToggle(false, meta);\n          }\n        }, {\n          once: true\n        });\n        break;\n      case 'Escape':\n        if (key === 'Escape') {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        onToggle(false, meta);\n        break;\n      default:\n    }\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_SelectableContext.default.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_DropdownContext.default.Provider, {\n      value: context,\n      children: children\n    })\n  });\n}\nDropdown.displayName = 'Dropdown';\nDropdown.Menu = _DropdownMenu.default;\nDropdown.Toggle = _DropdownToggle.default;\nDropdown.Item = _DropdownItem.default;\nvar _default = exports.default = Dropdown;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,IAAI;AACJ,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,uBAAuB,CAAC;IAAI,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAAE,SAAS;IAAE;AAAG;AACpF,SAAS;IACP,MAAM,cAAc,CAAC,GAAG,gBAAgB,OAAO;IAC/C,MAAM,MAAM,CAAC,GAAG,OAAO,MAAM,EAAE;IAC/B,MAAM,YAAY,CAAC,GAAG,OAAO,WAAW,EAAE,CAAA;QACxC,IAAI,OAAO,GAAG;QACd,0DAA0D;QAC1D;IACF,GAAG;QAAC;KAAY;IAChB,OAAO;QAAC;QAAK;KAAU;AACzB;AAEA;;;CAGC,GACD,SAAS,SAAS,EAChB,WAAW,EACX,MAAM,OAAO,EACb,QAAQ,EACR,UAAU,WAAW,EACrB,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,QAAQ,EAAE,iBAAiB,CAAC,CAAC,EAC/D,oBAAoB,EACpB,YAAY,cAAc,EAC1B,QAAQ,EACT;IACC,MAAM,SAAS,CAAC,GAAG,WAAW,OAAO;IACrC,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAG,gBAAgB,mBAAmB,EAAE,SAAS,aAAa;IAExF,wEAAwE;IACxE,8EAA8E;IAC9E,wCAAwC;IACxC,MAAM,CAAC,SAAS,QAAQ,GAAG;IAC3B,MAAM,cAAc,QAAQ,OAAO;IACnC,MAAM,CAAC,WAAW,UAAU,GAAG;IAC/B,MAAM,gBAAgB,UAAU,OAAO;IACvC,MAAM,WAAW,CAAC,GAAG,aAAa,OAAO,EAAE;IAC3C,MAAM,kBAAkB,CAAC,GAAG,OAAO,MAAM,EAAE;IAC3C,MAAM,kBAAkB,CAAC,GAAG,OAAO,MAAM,EAAE;IAC3C,MAAM,cAAc,CAAC,GAAG,OAAO,UAAU,EAAE,mBAAmB,OAAO;IACrE,MAAM,SAAS,CAAC,GAAG,OAAO,WAAW,EAAE,CAAC,UAAU,OAAO,SAAS,SAAS,OAAO,KAAK,IAAI,MAAM,IAAI;QACnG,SAAS,UAAU;YACjB,eAAe;YACf;QACF;IACF,GAAG;QAAC;KAAS;IACb,MAAM,eAAe,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAC,KAAK;QACxD,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK;QAC1C,OAAO,OAAO,OAAO;QACrB,IAAI,CAAC,MAAM,oBAAoB,IAAI;YACjC,eAAe,OAAO,KAAK,IAAI,YAAY,KAAK;QAClD;IACF;IACA,MAAM,UAAU,CAAC,GAAG,OAAO,OAAO,EAAE,IAAM,CAAC;YACzC;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAQ;QAAW;QAAM;QAAa;QAAe;QAAS;KAAU;IAC7E,IAAI,eAAe,YAAY,CAAC,MAAM;QACpC,gBAAgB,OAAO,GAAG,YAAY,QAAQ,CAAC,YAAY,aAAa,CAAC,aAAa;IACxF;IACA,MAAM,cAAc,CAAC,GAAG,kBAAkB,OAAO,EAAE;QACjD,IAAI,iBAAiB,cAAc,KAAK,EAAE;YACxC,cAAc,KAAK;QACrB;IACF;IACA,MAAM,kBAAkB,CAAC,GAAG,kBAAkB,OAAO,EAAE;QACrD,MAAM,OAAO,gBAAgB,OAAO;QACpC,IAAI,YAAY;QAChB,IAAI,aAAa,MAAM;YACrB,YAAY,QAAQ,OAAO,IAAI,CAAC,GAAG,gBAAgB,UAAU,EAAE,QAAQ,OAAO,IAAI,aAAa;QACjG;QACA,IAAI,cAAc,SAAS,cAAc,cAAc,CAAC,UAAU,IAAI,CAAC,OAAO;YAC5E;QACF;QACA,MAAM,QAAQ,CAAC,GAAG,kBAAkB,OAAO,EAAE,QAAQ,OAAO,EAAE,aAAa,CAAC,EAAE;QAC9E,IAAI,SAAS,MAAM,KAAK,EAAE,MAAM,KAAK;IACvC;IACA,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,IAAI,MAAM;aAAuB,IAAI,gBAAgB,OAAO,EAAE;YAC5D,gBAAgB,OAAO,GAAG;YAC1B;QACF;IACA,iCAAiC;IACnC,GAAG;QAAC;QAAM;QAAiB;QAAa;KAAgB;IACxD,CAAC,GAAG,OAAO,SAAS,EAAE;QACpB,gBAAgB,OAAO,GAAG;IAC5B;IACA,MAAM,sBAAsB,CAAC,SAAS;QACpC,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO;QAC7B,MAAM,QAAQ,CAAC,GAAG,kBAAkB,OAAO,EAAE,QAAQ,OAAO,EAAE;QAC9D,IAAI,QAAQ,MAAM,OAAO,CAAC,WAAW;QACrC,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,MAAM;QAChD,OAAO,KAAK,CAAC,MAAM;IACrB;IACA,CAAC,GAAG,kBAAkB,OAAO,EAAE,CAAC,GAAG,OAAO,WAAW,EAAE,IAAM,OAAO,QAAQ,EAAE;QAAC;KAAO,GAAG,WAAW,CAAA;QAClG,IAAI,kBAAkB;QACtB,MAAM,EACJ,GAAG,EACJ,GAAG;QACJ,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,WAAW,CAAC,mBAAmB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,iBAAiB,QAAQ,CAAC;QACnG,MAAM,aAAa,CAAC,qBAAqB,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,QAAQ,CAAC;QAE3G,yHAAyH;QACzH,oBAAoB;QACpB,MAAM,UAAU,kBAAkB,IAAI,CAAC,OAAO,OAAO;QACrD,IAAI,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,YAAY,QAAQ,YAAY,OAAO,IAAI,KAAK,QAAQ,GAAG;YAC5G;QACF;QACA,IAAI,CAAC,YAAY,CAAC,YAAY;YAC5B;QACF;QACA,IAAI,QAAQ,SAAS,CAAC,CAAC,QAAQ,OAAO,IAAI,CAAC,IAAI,GAAG;YAChD;QACF;QACA,gBAAgB,OAAO,GAAG,MAAM,IAAI;QACpC,MAAM,OAAO;YACX,eAAe;YACf,QAAQ,MAAM,IAAI;QACpB;QACA,OAAQ;YACN,KAAK;gBACH;oBACE,MAAM,OAAO,oBAAoB,QAAQ,CAAC;oBAC1C,IAAI,QAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;oBAClC,MAAM,cAAc;oBACpB;gBACF;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,MAAM;oBACT,SAAS,MAAM;gBACjB,OAAO;oBACL,MAAM,OAAO,oBAAoB,QAAQ;oBACzC,IAAI,QAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;gBACpC;gBACA;YACF,KAAK;gBACH,uEAAuE;gBACvE,0EAA0E;gBAC1E,6EAA6E;gBAC7E,6BAA6B;gBAC7B,CAAC,GAAG,kBAAkB,OAAO,EAAE,OAAO,aAAa,EAAE,SAAS,CAAA;oBAC5D,IAAI;oBACJ,IAAI,EAAE,GAAG,KAAK,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,oBAAoB,QAAQ,OAAO,KAAK,QAAQ,kBAAkB,QAAQ,CAAC,EAAE,MAAM,CAAC,GAAG;wBAC5H,SAAS,OAAO;oBAClB;gBACF,GAAG;oBACD,MAAM;gBACR;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,UAAU;oBACpB,MAAM,cAAc;oBACpB,MAAM,eAAe;gBACvB;gBACA,SAAS,OAAO;gBAChB;YACF;QACF;IACF;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,mBAAmB,OAAO,CAAC,QAAQ,EAAE;QAC5E,OAAO;QACP,UAAU,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,iBAAiB,OAAO,CAAC,QAAQ,EAAE;YAC7E,OAAO;YACP,UAAU;QACZ;IACF;AACF;AACA,SAAS,WAAW,GAAG;AACvB,SAAS,IAAI,GAAG,cAAc,OAAO;AACrC,SAAS,MAAM,GAAG,gBAAgB,OAAO;AACzC,SAAS,IAAI,GAAG,cAAc,OAAO;AACrC,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/%40restart/ui/cjs/Anchor.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nexports.isTrivialHref = isTrivialHref;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _hooks = require(\"@restart/hooks\");\nvar _Button = require(\"./Button\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"onKeyDown\"];\n/* eslint-disable jsx-a11y/no-static-element-interactions */\n/* eslint-disable jsx-a11y/anchor-has-content */\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nfunction isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\n/**\n * An generic `<a>` component that covers a few A11y cases, ensuring that\n * cases where the `href` is missing or trivial like \"#\" are treated like buttons.\n */\nconst Anchor = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps] = (0, _Button.useButtonProps)(Object.assign({\n    tagName: 'a'\n  }, props));\n  const handleKeyDown = (0, _hooks.useEventCallback)(e => {\n    buttonProps.onKeyDown(e);\n    onKeyDown == null ? void 0 : onKeyDown(e);\n  });\n  if (isTrivialHref(props.href) || props.role === 'button') {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(\"a\", Object.assign({\n      ref: ref\n    }, props, buttonProps, {\n      onKeyDown: handleKeyDown\n    }));\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(\"a\", Object.assign({\n    ref: ref\n  }, props, {\n    onKeyDown: onKeyDown\n  }));\n});\nAnchor.displayName = 'Anchor';\nvar _default = exports.default = Anchor;"], "names": [], "mappings": "AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,aAAa,GAAG;AACxB,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,MAAM,YAAY;IAAC;CAAY;AAC/B,0DAA0D,GAC1D,8CAA8C,GAC9C,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAU,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AAC3M,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,OAAO,KAAK,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AAClkB,SAAS,8BAA8B,CAAC,EAAE,CAAC;IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC;IAAG,IAAI,IAAI,CAAC;IAAG,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;QAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO;AAAG;AACpM,SAAS,cAAc,IAAI;IACzB,OAAO,CAAC,QAAQ,KAAK,IAAI,OAAO;AAClC;AACA;;;CAGC,GACD,MAAM,SAAS,WAAW,GAAE,MAAM,UAAU,CAAC,CAAC,MAAM;IAClD,IAAI,EACA,SAAS,EACV,GAAG,MACJ,QAAQ,8BAA8B,MAAM;IAC9C,MAAM,CAAC,YAAY,GAAG,CAAC,GAAG,QAAQ,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,SAAS;IACX,GAAG;IACH,MAAM,gBAAgB,CAAC,GAAG,OAAO,gBAAgB,EAAE,CAAA;QACjD,YAAY,SAAS,CAAC;QACtB,aAAa,OAAO,KAAK,IAAI,UAAU;IACzC;IACA,IAAI,cAAc,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,UAAU;QACxD,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,KAAK,OAAO,MAAM,CAAC;YAC1D,KAAK;QACP,GAAG,OAAO,aAAa;YACrB,WAAW;QACb;IACF;IACA,OAAO,WAAW,GAAE,CAAC,GAAG,YAAY,GAAG,EAAE,KAAK,OAAO,MAAM,CAAC;QAC1D,KAAK;IACP,GAAG,OAAO;QACR,WAAW;IACb;AACF;AACA,OAAO,WAAW,GAAG;AACrB,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}