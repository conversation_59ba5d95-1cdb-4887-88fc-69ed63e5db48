import React, { useState, useEffect } from 'react';
import toast from "react-hot-toast";
import { Col, Row } from 'react-bootstrap';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CommonTooltip from '@/Components/UI/CommonTooltip';

const ManualDepositGroupRow = ({
  depositValue,
  depositType,
  depositTypeOptions = [],
  onChange,
  onSubmit,
  depositDataType = '',
  depositSummary = '',
  depositSummaryType = '',
  depositTypeDataType = '',
  manualError
}) => {
  const formatDisplay = (val, type) => {
    if (type === 'Currency' && val !== '') return `$${val}`;
    if (type?.toLowerCase().includes('percentage') && val !== '') return `${val}%`;
    if (type?.toLowerCase().includes('ratio') && !val.toString().toLowerCase().includes('x') && val !== '') return `${parseFloat(val).toFixed(2)}X`;
    return val;
  };

  const parseValue = (val) => val.replace(/[^0-9.]/g, '');

  return (
    <>
      <div className='mt-2 special-fields position-relative'>
        <Row className="align-items-center gx-2">
          <Col md={6} xxl={7} xs={7}>
            <div className="blueCard">
              <p className="mb-0">MANUAL DEPOSIT</p>
              <CommonTooltip content={depositSummary} position="top-left">
                <SolidInfoIcon />
              </CommonTooltip>
            </div>
            <div className="mt-2 blueCard">
              <p className="mb-0">MANUAL DEPOSIT TYPE</p>
              <CommonTooltip content={depositSummaryType} position="top-left">
                <SolidInfoIcon />
              </CommonTooltip>
            </div>
          </Col>

          <Col md={3} xxl={3} xs={5}>
            {manualError && (
              <div className="special-fields-error">
                <span>Manual Deposit and Manual Deposit Type must be submitted together.</span>
              </div>
            )}
            <div className="whiteCard" title={formatDisplay(depositValue, depositDataType) || ''}>
              <input
                type="text"
                value={formatDisplay(depositValue, depositDataType)}
                onChange={(e) => onChange('portfolio_manual_deposit', parseValue(e.target.value))}
                placeholder="$0"
              />
            </div>

            <div className="whiteCard mt-2" title={depositType}>
              <select
                value={depositType}
                onChange={(e) => onChange('portfolio_manual_deposit_type', e.target.value)}
              >
                <option value="">Select</option>
                {depositTypeOptions.map((opt, i) => (
                  <option key={i} value={opt}>{opt}</option>
                ))}
              </select>
            </div>
          </Col>

          <Col md={3} xxl={2} className='mt-2 mt-md-0'>
            <button className="submit-manual-btn w-100" onClick={onSubmit}>
              Submit
            </button>
          </Col>
        </Row>
      </div>
    </>
  );
};

const CapitalFieldRow = ({
  label,
  summary,
  inputValue,
  placeholder,
  options = [],
  databaseField,
  data_type,
  hasFormula,
  isDisabled,
  onChange,
  showSubmit,
  onSubmit
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const formatDisplay = (val) => {
    if (val === '' || val === null || val === undefined) return val;

    const strVal = val.toString();

    if (data_type === 'Currency' && !strVal.trim().startsWith('$')) {
      return `$${strVal}`;
    }

    if (data_type?.toLowerCase().includes('percentage') && !strVal.includes('%')) {
      return `${strVal}%`;
    }

    if (data_type?.toLowerCase().includes('ratio') && !strVal.toLowerCase().endsWith('x')) {
      return `${strVal}X`;
    }

    return strVal;
  };

  const parseValue = (val) => {
    if (data_type === 'Currency') return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('percentage')) return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('ratio')) return val.replace(/[^0-9.]/g, '');
    return val;
  };

  const handleChange = (val) => {
    let cleaned = val.replace(/[^0-9.]/g, '');

    const dotCount = (cleaned.match(/\./g) || []).length;
    if (dotCount > 1) return;

    const [intPart, decimalPart = ''] = cleaned.split('.');
    let limitedDecimal = decimalPart;

    if (data_type === 'Currency') {
      limitedDecimal = decimalPart.slice(0, 8);
    } else if (data_type?.toLowerCase().includes('percentage')) {
      limitedDecimal = decimalPart.slice(0, 6);
    }

    let limited = intPart;
    if (cleaned.includes('.')) {
      limited += '.' + limitedDecimal;
    }

    setInputValue(limited);

    if (!hasFormula && /^[0-9]+(\.[0-9]+)?$/.test(limited)) {
      onChange(parseValue(limited));
    }
  };

  const isForcedInput = databaseField === 'portfolio_account_initial_balance_type';
  const showAsInput = isForcedInput;

  return (
    <div className={showSubmit ? "special-fields mt-2" : ""}>
      <Row className={showSubmit ? "align-items-center gx-2" : "gx-2 mt-2"}>
        <Col md={showSubmit ? 6 : 9} xxl={showSubmit ? 7 : 9} xs={7}>
          <div className="blueCard d-flex justify-content-between align-items-center">
            <p className="mb-0">{label}</p>
            <CommonTooltip
              className="subTooltip"
              content={
                <>
                  <p>{summary}</p>
                </>
              }
              position="top-left"
            >
              <SolidInfoIcon />
            </CommonTooltip>
          </div>
        </Col>

        <Col md={showSubmit ? 3 : 3} xxl={3} xs={5}>
          <div className={isDisabled || hasFormula ? "DisabledCard" : "whiteCard"}
            title={formatDisplay(inputValue)}>
            {placeholder === 'Select' ? (
              showAsInput ? (
                <input
                  type="text"
                  value={isFocused ? inputValue : formatDisplay(inputValue)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  onChange={(e) => handleChange(e.target.value)}
                  placeholder={(!inputValue || inputValue === '0') ? 'No Data' : (placeholder || 'Enter value')}
                  disabled={true}
                />
              ) : (
                <select
                  value={inputValue}
                  onChange={(e) => handleChange(e.target.value)}
                  disabled={hasFormula}
                >
                  <option value="" disabled>
                    {(hasFormula && (!inputValue || inputValue === '0')) ? 'No Data' : 'Select'}
                  </option>
                  {options.map((option, index) => (
                    <option key={index} value={option}>{option}</option>
                  ))}
                </select>
              )
            ) : (
              <input
                type="text"
                value={formatDisplay(inputValue)}
                onChange={(e) => handleChange(e.target.value)}
                placeholder={placeholder}
                disabled={isDisabled}
              />
            )}
          </div>
        </Col>

        {showSubmit && (
          <Col md={3} xxl={2} className='mt-2 mt-md-0'>
            <button
              className="submit-manual-btn w-100"
              onClick={onSubmit}
              disabled={isDisabled}
            >
              Submit
            </button>
          </Col>
        )}
      </Row>
    </div >
  );
};

export default function CapitalCashFlow({ data = [], onChangeField }) {
  const [manualError, setManualError] = useState(false);
  const [formState, setFormState] = useState({
    portfolio_manual_deposit: '',
    portfolio_manual_deposit_type: '',
    portfolio_withdrawal: ''
  });

  useEffect(() => {
    setFormState({
      portfolio_manual_deposit: '',
      portfolio_manual_deposit_type: '',
      portfolio_withdrawal: ''
    });
  }, [data]);

  const handleFieldChange = (field, val) => {
    setFormState((prev) => ({ ...prev, [field]: val }));
  };

  const handleManualSubmit = async () => {
    const deposit = formState.portfolio_manual_deposit;
    const type = formState.portfolio_manual_deposit_type;

    if (!deposit || !type) {
      setManualError(true);
      setTimeout(() => {
        setManualError(false);
      }, 3000);
      return;
    }
    setManualError(false);


    for (const key of ['portfolio_manual_deposit', 'portfolio_manual_deposit_type']) {
      const field = data.find((f) => f.database_field === key);
      if (field) {
        await onChangeField(field.id, formState[key]);
      }
    }

    setFormState((prev) => ({
      ...prev,
      portfolio_manual_deposit: '',
      portfolio_manual_deposit_type: ''
    }));
  };

  const handleWithdrawalSubmit = () => {
    const field = data.find((f) => f.database_field === 'portfolio_withdrawal');
    if (field) {
      onChangeField(field.id, formState.portfolio_withdrawal);
    }
  };

  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Capital & Cash Flow</p>
      </div>

      {data.map((field, index) => {
        const key = field.database_field;

        if (key === 'portfolio_manual_deposit') return null;

        const isManual = ['portfolio_manual_deposit', 'portfolio_manual_deposit_type', 'portfolio_withdrawal'].includes(key);
        const isLockedField = ['PORTFOLIO_ACCOUNT_INITIAL_BALANCE', 'PORTFOLIO_ACCOUNT_INITIAL_BALANCE_TYPE'].includes(key.toUpperCase());
        const isDisabled = field.hasFormula || isLockedField;

        if (key === 'portfolio_manual_deposit_type') {
          return (
            <ManualDepositGroupRow
              key="manual-deposit-group"
              depositValue={formState.portfolio_manual_deposit}
              depositType={formState.portfolio_manual_deposit_type}
              depositTypeOptions={field.options ?? []}
              depositDataType={data.find(f => f.database_field === 'portfolio_manual_deposit')?.data_type || ''}
              depositSummary={data.find(f => f.database_field === 'portfolio_manual_deposit')?.summary || ''}
              depositSummaryType={data.find(f => f.database_field === 'portfolio_manual_deposit_type')?.summary || ''}
              depositTypeDataType={field.data_type}
              onChange={handleFieldChange}
              onSubmit={handleManualSubmit}
              manualError={manualError}
            />
          );
        }

        return (
          <CapitalFieldRow
            key={index}
            label={field.label ?? key}
            summary={field?.summary}
            inputValue={isManual ? formState[key] : field.value ?? ''}
            placeholder={field.account_field_placeholder}
            options={field.options ?? [field.account_field_value]}
            databaseField={key}
            data_type={field.data_type}
            hasFormula={field.hasFormula}
            isDisabled={isDisabled}
            onChange={handleFieldChange}
            showSubmit={key === 'portfolio_withdrawal'}
            onSubmit={handleWithdrawalSubmit}
          />
        );
      })}
    </div>
  );
}
