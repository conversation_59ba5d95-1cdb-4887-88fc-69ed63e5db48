{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Pricing.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.pricing {\r\n  position: relative;\r\n\r\n  &_banner {\r\n    padding: 100px 0 42px;\r\n\r\n    @media (max-width: 767px) {\r\n      padding: 50px 0 42px;\r\n    }\r\n\r\n    &_content {\r\n      h1 {\r\n        font-size: 80px;\r\n        font-weight: 800;\r\n        line-height: 90px;\r\n\r\n        @media screen and (max-width: 1199px) {\r\n          font-size: 60px;\r\n          line-height: 65px;\r\n        }\r\n\r\n        @media screen and (max-width: 991px) {\r\n          font-size: 48px;\r\n          line-height: 52.8px;\r\n          font-weight: 800;\r\n        }\r\n\r\n        @media screen and (max-width: 767px) {\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      p {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        line-height: 36px;\r\n        letter-spacing: -1px;\r\n        text-align: left;\r\n        padding-top: 2rem;\r\n\r\n        @media screen and (max-width: 991px) {\r\n          font-size: 18px;\r\n          line-height: 27px;\r\n        }\r\n\r\n        @media screen and (max-width: 767px) {\r\n          text-align: center;\r\n          margin-bottom: 20px;\r\n          padding-top: 1.25rem;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_forever {\r\n      background: linear-gradient(139.01deg,\r\n          #26334d 18.26%,\r\n          #4e5d7a 63.25%,\r\n          #26334d 100.07%),\r\n        radial-gradient(27.58% 27.58% at 50% 50%,\r\n          rgb(254, 165, 1) 0%,\r\n          rgb(254, 165, 1) 100%);\r\n      border-radius: 40px;\r\n      padding: 50px 20px;\r\n      text-align: center;\r\n      position: relative;\r\n      margin: auto;\r\n      max-width: 22em;\r\n      box-sizing: border-box;\r\n      background-clip: padding-box;\r\n      /* !importanté */\r\n      border: solid 2px transparent;\r\n      /* !importanté */\r\n\r\n      &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        z-index: -1;\r\n        margin: -2px;\r\n        /* !importanté */\r\n        border-radius: inherit;\r\n        /* !importanté */\r\n        background: linear-gradient(142.34deg,\r\n            #00adef 0.06%,\r\n            rgba(254, 165, 0, 0) 46.5%,\r\n            #00adef 98.85%);\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        h4 {\r\n          font-size: 20px;\r\n          line-height: 25px;\r\n        }\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding: 30px 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_table {\r\n    position: relative;\r\n    z-index: 1;\r\n    padding-bottom: 7rem;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      background-image: url(\"https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png\");\r\n      background-position: center;\r\n      background-repeat: no-repeat;\r\n      background-size: 100%;\r\n      border-radius: 2rem;\r\n      z-index: -1;\r\n    }\r\n\r\n    &_switch {\r\n      margin-bottom: 25px;\r\n\r\n      p {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        line-height: 36px;\r\n        text-align: left;\r\n      }\r\n\r\n      .checkbox_input {\r\n        margin: 0 20px;\r\n      }\r\n    }\r\n\r\n    &_col {\r\n      &:first-child {\r\n        .pricing_table_box {\r\n          border-right: 0;\r\n          border-top-left-radius: 50px;\r\n          border-bottom-left-radius: 50px;\r\n        }\r\n      }\r\n\r\n      &:last-child {\r\n        .pricing_table_box {\r\n          border-left: 0;\r\n          border-top-right-radius: 50px;\r\n          border-bottom-right-radius: 50px;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_box {\r\n      background: #00000033;\r\n      border: 3px solid #ffffff1a;\r\n      padding: 50px 20px;\r\n      width: 100%;\r\n\r\n      @media (min-width: 1400px) {\r\n        padding: 50px 30px;\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        border: 3px solid #ffffff1a !important;\r\n        border-radius: 30px;\r\n        margin-top: 30px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding: 30px 20px;\r\n      }\r\n\r\n      &_heading {\r\n        text-align: center;\r\n\r\n        h2 {\r\n          font-size: 48px;\r\n          font-weight: 800;\r\n          text-align: center;\r\n          margin: 20px 0;\r\n\r\n          @media screen and (max-width: 991px) {\r\n            font-size: 36px;\r\n          }\r\n\r\n          @media screen and (max-width: 767px) {\r\n            font-size: 28px;\r\n          }\r\n\r\n          span {\r\n            font-size: 24px;\r\n\r\n            @media screen and (max-width: 991px) {\r\n              font-size: 18px;\r\n            }\r\n\r\n            @media screen and (max-width: 767px) {\r\n              font-size: 16px;\r\n            }\r\n          }\r\n        }\r\n\r\n        p {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          line-height: 27px;\r\n          text-align: center;\r\n          margin: 20px 0;\r\n\r\n          a {\r\n            &:hover {\r\n              color: var.$green;\r\n            }\r\n          }\r\n        }\r\n\r\n        .green-btn {\r\n          @media screen and (max-width: 991px) {\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      ul {\r\n        margin-top: 30px;\r\n\r\n        @media (min-width: 768px) and (max-width: 991px) {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n        }\r\n\r\n        li {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          line-height: 27px;\r\n          margin: 20px 0;\r\n          color: var.$white;\r\n\r\n          @media (min-width: 768px) and (max-width: 991px) {\r\n            width: 50%;\r\n          }\r\n\r\n          svg {\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_col {\r\n    display: flex;\r\n  }\r\n\r\n  &_box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n  }\r\n\r\n  ul {\r\n    flex-grow: 1;\r\n  }\r\n\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAGE;;;;AAGE;EAHF;;;;;AAQI;;;;;;AAKE;EALF;;;;;;AAUE;EAVF;;;;;;;AAgBE;EAhBF;;;;;AAqBA;;;;;;;;;AAQE;EARF;;;;;;AAaE;EAbF;;;;;;;AAqBF;;;;;;;;;;;;;;;;;;;AAoBE;;;;;;;;;;;;;AAkBA;EACE;;;;;;AAMF;EA7CF;;;;;AAmDF;;;;;;AAKE;;;;;;;;;;;;;;;AAeA;;;;AAGE;;;;;;;AAOA;;;;AAOE;;;;;;AAQA;;;;;;AAQJ;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;;;AAgBE;EAhBF;;;;;AAoBE;;;;AAGE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAcE;;;;AAGE;EAHF;;;;;AAOE;EAPF;;;;;AAaF;;;;;;;;AAQI;;;;AAOF;EADF;;;;;AAOF;;;;AAGE;EAHF;;;;;;AAQE;;;;;;;;AAOE;EAPF;;;;;AAWE;;;;AAQR;;;;AAIA;;;;;;;AAOA"}}]}