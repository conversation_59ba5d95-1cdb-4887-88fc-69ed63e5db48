/* [project]/src/css/Home/Pricing.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.pricing {
  position: relative;
}

.pricing_banner {
  padding: 100px 0 42px;
}

@media (max-width: 767px) {
  .pricing_banner {
    padding: 50px 0 42px;
  }
}

.pricing_banner_content h1 {
  font-size: 80px;
  font-weight: 800;
  line-height: 90px;
}

@media screen and (max-width: 1199px) {
  .pricing_banner_content h1 {
    font-size: 60px;
    line-height: 65px;
  }
}

@media screen and (max-width: 991px) {
  .pricing_banner_content h1 {
    font-size: 48px;
    font-weight: 800;
    line-height: 52.8px;
  }
}

@media screen and (max-width: 767px) {
  .pricing_banner_content h1 {
    text-align: center;
  }
}

.pricing_banner_content p {
  letter-spacing: -1px;
  text-align: left;
  padding-top: 2rem;
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
}

@media screen and (max-width: 991px) {
  .pricing_banner_content p {
    font-size: 18px;
    line-height: 27px;
  }
}

@media screen and (max-width: 767px) {
  .pricing_banner_content p {
    text-align: center;
    margin-bottom: 20px;
    padding-top: 1.25rem;
  }
}

.pricing_banner_forever {
  text-align: center;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0);
  background-image: linear-gradient(139.01deg, #26334d 18.26%, #4e5d7a 63.25%, #26334d 100.07%), radial-gradient(27.58% 27.58%, #fea501 0%, #fea501 100%);
  background-position: 0 0, 0 0;
  background-repeat: repeat, repeat;
  background-size: auto, auto;
  background-attachment: scroll, scroll;
  background-origin: padding-box, padding-box;
  background-clip: padding-box;
  border: 2px solid rgba(0, 0, 0, 0);
  border-radius: 40px;
  max-width: 22em;
  margin: auto;
  padding: 50px 20px;
  position: relative;
}

.pricing_banner_forever:before {
  content: "";
  z-index: -1;
  border-radius: inherit;
  background: linear-gradient(142.34deg, #00adef .06%, rgba(254, 165, 0, 0) 46.5%, #00adef 98.85%);
  margin: -2px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

@media (max-width: 991px) {
  .pricing_banner_forever h4 {
    font-size: 20px;
    line-height: 25px;
  }
}

@media (max-width: 767px) {
  .pricing_banner_forever {
    padding: 30px 20px;
  }
}

.pricing_table {
  z-index: 1;
  padding-bottom: 7rem;
  position: relative;
}

.pricing_table:before {
  content: "";
  z-index: -1;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
  border-radius: 2rem;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.pricing_table_switch {
  margin-bottom: 25px;
}

.pricing_table_switch p {
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  line-height: 36px;
}

.pricing_table_switch .checkbox_input {
  margin: 0 20px;
}

.pricing_table_col:first-child .pricing_table_box {
  border-right: 0;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
}

.pricing_table_col:last-child .pricing_table_box {
  border-left: 0;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}

.pricing_table_box {
  background: rgba(0, 0, 0, .2);
  border: 3px solid rgba(255, 255, 255, .1);
  width: 100%;
  padding: 50px 20px;
}

@media (min-width: 1400px) {
  .pricing_table_box {
    padding: 50px 30px;
  }
}

@media (max-width: 991px) {
  .pricing_table_box {
    border-radius: 30px;
    margin-top: 30px;
    border: 3px solid rgba(255, 255, 255, .1) !important;
  }
}

@media (max-width: 767px) {
  .pricing_table_box {
    padding: 30px 20px;
  }
}

.pricing_table_box_heading {
  text-align: center;
}

.pricing_table_box_heading h2 {
  text-align: center;
  margin: 20px 0;
  font-size: 48px;
  font-weight: 800;
}

@media screen and (max-width: 991px) {
  .pricing_table_box_heading h2 {
    font-size: 36px;
  }
}

@media screen and (max-width: 767px) {
  .pricing_table_box_heading h2 {
    font-size: 28px;
  }
}

.pricing_table_box_heading h2 span {
  font-size: 24px;
}

@media screen and (max-width: 991px) {
  .pricing_table_box_heading h2 span {
    font-size: 18px;
  }
}

@media screen and (max-width: 767px) {
  .pricing_table_box_heading h2 span {
    font-size: 16px;
  }
}

.pricing_table_box_heading p {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
}

.pricing_table_box_heading p a:hover {
  color: #32cd33;
}

@media screen and (max-width: 991px) {
  .pricing_table_box_heading .green-btn {
    width: 100%;
  }
}

.pricing_table_box ul {
  margin-top: 30px;
}

@media (min-width: 768px) and (max-width: 991px) {
  .pricing_table_box ul {
    flex-wrap: wrap;
    display: flex;
  }
}

.pricing_table_box ul li {
  color: #fff;
  margin: 20px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
}

@media (min-width: 768px) and (max-width: 991px) {
  .pricing_table_box ul li {
    width: 50%;
  }
}

.pricing_table_box ul li svg {
  margin-right: 10px;
}

.pricing_col {
  display: flex;
}

.pricing_box {
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  display: flex;
}

.pricing ul {
  flex-grow: 1;
}

/*# sourceMappingURL=src_css_Home_Pricing_scss_css_e59ae46c._.single.css.map*/