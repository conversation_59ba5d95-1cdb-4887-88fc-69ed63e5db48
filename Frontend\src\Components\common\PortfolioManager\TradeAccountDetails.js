import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CommonTooltip from '@/Components/UI/CommonTooltip';
import PortfolioDropdown from './PortfolioDropdown';

const TradeFieldRow = ({
  label,
  summary,
  value,
  placeholder,
  options = [],
  isTimezone,
  data_type,
  hasFormula,
  onChange,
  isDisabled
}) => {
  const [inputValue, setInputValue] = useState(value ?? '');

  useEffect(() => {
    setInputValue(value ?? '');
  }, [value]);

  const formatOptions = (opts) =>
    opts.map((item) =>
      typeof item === 'string' ? { label: item, value: item } : item
    );

  const parseValue = (val) => {
    if (data_type === 'Currency') return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('percentage')) return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('ratio')) return val.replace(/[^0-9.]/g, '');
    return val;
  };

  const handleChange = (val) => {
    const raw = parseValue(val);
    setInputValue(raw);
    if (!hasFormula) {
      onChange(raw);
    }
  };

  return (
    <Row className="mt-2 gx-2">
      <Col md={9} xs={7}>
        <div className="blueCard">
          <p>{label}</p>
          <CommonTooltip
            className="subTooltip"
            content={
              <>
                {summary.split('⚠️  ').map((part, index) => (
                  <p key={index}>
                    {index === 0 ? part.trim() : `⚠️ ${part.trim()}`}
                  </p>
                ))}
              </>
            }
            position="top-left"
          >
            <SolidInfoIcon />
          </CommonTooltip>
        </div>
      </Col>
      <Col md={3} xs={5}>
        <PortfolioDropdown
          title={inputValue}
          defaultValue="Select"
          options={formatOptions(options)}
          optionLabelKey="label"
          value={inputValue}
          onSelect={(selectedOption) =>
            handleChange(typeof selectedOption === 'object' ? selectedOption.value : selectedOption)
          } showSearch={true}
        />
      </Col>
    </Row>
  );
};

export default function TradeAccountDetails({ data = [], onChangeField }) {
  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Trade Account Details</p>
      </div>

      {data.map((field, index) => (
        <TradeFieldRow
          key={index}
          label={field.label ?? field.database_field}
          summary={field?.summary}
          value={field.value ?? ''}
          placeholder={field.account_field_placeholder}
          options={field.options ?? [field.account_field_value]}
          isTimezone={field.database_field === 'PORTFOLIO_TIMEZONE'}
          data_type={field.data_type}
          hasFormula={field.hasFormula}
          onChange={(newValue) => onChangeField(field.id, newValue)}
        />
      ))}
    </div>
  );
}
