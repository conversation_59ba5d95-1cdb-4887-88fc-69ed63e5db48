<?php

namespace App\Console\Commands;

use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\PortfolioFieldDefinition;
use App\Models\FieldDefinition;

class AddNewFieldPortfolioManager extends Command
{
    protected $signature = 'create:portfolio-manager-value';
    protected $description = 'Create Portfolio manager value';

    public function handle()
    {
        //   $fieldDefinition = FieldDefinition::create([
        //      'field_name'      => 'NUMBER FORMAT DISPLAY',
        //      'database_field'  => 'number_format_display',
        //      'summary'         => 'Choose how numbers, such as prices and metrics, are displayed throughout your dashboard. This only affects visual formatting—not calculations or how you enter numbers.
        //                            • English format: 1,234.56
        //                            • European format: 1.234,56
        //                            Note: When entering values into input fields, always use the international format with a period (.) as the decimal separator. Commas are not allowed in number inputs.',
        //      'datatype'        => 'Text',
        //      'expected_values' => 'en-US, de-DE',
        //      'has_formula'     => false,
        //      'metric_dimension'=> 'metric'
        // ]);

        // PortfolioFieldDefinition::create([
        //      'field_definition_id'  => $fieldDefinition->id,
        //      'database_field'      => 'number_format_display',
        //      'summary'             => 'Choose how numbers, such as prices and metrics, are displayed throughout your dashboard. This only affects visual formatting—not calculations or how you enter numbers.
        //                                                                  • English format: 1,234.56
        //                                                                  • European format: 1.234,56
        //                                                                  Note: When entering values into input fields, always use the international format with a period (.) as the decimal separator. Commas are not allowed in number inputs.',
        //      'account_field'       => 'YES',
        //      'account_field_placeholder' => 'Select',
        //      'account_field_value' => 'en-US'
        // ]);

        $currencySummary = "Controls how many decimals are displayed for currency values across your dashboards and trade views.This setting does not affect storage or calculations — only how values appear when unfocused.\n⚠️ Some tools, such as Trade Builder, may override this setting using their own per-field selectors.";

        $currencyFieldDefinition = FieldDefinition::updateOrCreate(
            ['database_field' => 'currency_decimals'],
            [
                'field_name'       => 'CURRENCY DECIMALS',
                'summary'          => $currencySummary,
                'datatype'         => 'Text',
                'expected_values'  => '0,1,2,3,4,5,6,7,8',
                'has_formula'      => false,
                'metric_dimension' => 'metric'
            ]
        );

        PortfolioFieldDefinition::updateOrCreate(
            ['database_field' => 'currency_decimals'],
            [
                'field_definition_id'         => $currencyFieldDefinition->id,
                'summary'                     => $currencySummary,
                'account_field'               => 'YES',
                'account_field_placeholder'   => 'Select',
                'account_field_value'         => '2'
            ]
        );

        $percentageSummary = "Controls the number of decimal places shown for percentage fields. Actual values are stored with full precision. This only affects how values are displayed when fields are unfocused.\n⚠️ Some tools, such as Trade Builder, may override this setting using their own per-field selectors.";

        $percentageFieldDefinition = FieldDefinition::updateOrCreate(
            ['database_field' => 'percentage_decimals'],
            [
                'field_name'       => 'PERCENTAGE DECIMALS',
                'summary'          => $percentageSummary,
                'datatype'         => 'Text',
                'expected_values'  => '0,1,2,3,4,5,6',
                'has_formula'      => false,
                'metric_dimension' => 'metric'
            ]
        );

        PortfolioFieldDefinition::updateOrCreate(
            ['database_field' => 'percentage_decimals'],
            [
                'field_definition_id'         => $percentageFieldDefinition->id,
                'summary'                     => $percentageSummary,
                'account_field'               => 'YES',
                'account_field_placeholder'   => 'Select',
                'account_field_value'         => '2'
            ]
        );
    }
}
