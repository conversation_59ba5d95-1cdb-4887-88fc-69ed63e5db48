(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/jodit-react/build/jodit-react.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_jodit-react_build_jodit-react_4e06a632.js",
  "static/chunks/node_modules_jodit-react_build_jodit-react_e6c83fb3.js",
  "static/chunks/node_modules_jodit-react_build_jodit-react_039402e1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jodit-react/build/jodit-react.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/node_modules/react-select/dist/react-select.esm.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_c27e6691._.js",
  "static/chunks/node_modules_react-select_dist_react-select_esm_9c3d3d21.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-select/dist/react-select.esm.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);