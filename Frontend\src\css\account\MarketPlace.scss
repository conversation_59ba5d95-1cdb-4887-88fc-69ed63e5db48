@use "../theme/var";

.container {
  @media (width<=550px) {
    padding: 0 !important;
  }
}

.account {
  &_search {
    width: 100%;
    height: 72px;
    background-color: #ffffff33;
    padding: 10px 20px;
    border-radius: 15px;
    display: flex;
    gap: 16px;
    margin-bottom: 26px;

    input {
      background-color: transparent;
      width: 100%;
      height: 100%;
      color: #ffffff99;
      font-size: 20px;
      font-weight: 600;

      &:focus {
        box-shadow: none;
        outline: 0;
      }
    }
  }

  &_card {
    &_btnArrow {
      min-width: 30px;
      max-width: 30px;
      min-height: 29px;
      max-height: 29px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #00adef;

      svg {
        color: white !important;
        margin-left: 0px !important;
        transition: all ease-in-out 0.3s;

        path {
          fill: white !important;
        }

        @media (max-width: 767px) {
          height: 10px !important;
        }
      }

      &:hover {
        background-color: #0099d1;
      }
    }

    &_information {
      color: var.$black !important;

      .main_inform {
        display: flex;
        gap: 20px;
      }

      h4 {
        color: var.$black !important;
        font-size: 20px;
        font-weight: 600;

        @media (max-width: 991px) {
          font-size: 15px;
        }
      }

      .mail {
        color: var.$black !important;
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 7px;
        font-family: "Gilroy-Semibold", sans-serif;
      }

      .location,
      .transaction,
      .active_sign,
      .since {
        color: var.$black !important;
        font-size: 14px;
        font-family: "Gilroy-Semibold", sans-serif;
        font-weight: 400;
        margin-bottom: 6px;
        display: flex;
        gap: 9px;
      }

      .active_sign span {
        height: 12px;
        width: 12px;
        border-radius: 50%;
        background: #d9d9d9;
        align-self: center;
      }

      .profile_photo {
        background-color: #0000001a;
        height: 100px;
        width: 100px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .profile_updateBtn {
        font-size: 14px;
        color: #00adef;
        margin-top: 5px;
        cursor: pointer;
        font-weight: bold;
      }

      .round-bluefill-btn {
        background-color: var.$baseclr;
        color: white;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 15px;
        position: absolute;
        bottom: 0;
        width: 100%;
        transition: 0.3s ease-in-out;

        &:hover {
          background-color: var.$baseclrhover;
        }
      }
    }

    &_about {
      color: var.$black !important;

      .para_desc {
        color: var.$black !important;
        font-family: "Gilroy-Semibold", sans-serif;
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 7px;
        line-height: 26px;
      }

      .website-link {
        color: var.$baseclr;
        font-size: 14px;
        font-weight: 700;
        word-break: break-all;

        &:hover {
          color: #fea500;
        }
      }

      .table_form_textarea {
        background-color: #4f5e7a;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        font-weight: 600;
        width: 100%;
        padding: 5px 8px;

        &:focus {
          outline: none;
          background-color: #576887;
        }
      }

      .character-count {
        text-align: end;
        margin-top: 2px;
        color: #4f5e7a;
        font-size: 14px;
      }
    }

    &_followers,
    &_following {
      color: var.$black !important;

      .main_inform {
        display: flex;
        gap: 12px;
        align-items: center;
        margin-bottom: 6px;
      }

      h6 {
        color: var.$baseclr;
        font-size: 16px;
        font-weight: 600;
      }

      .profile_photo {
        background-color: #0000001a;
        height: 50px;
        width: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        flex-shrink: 0 !important;

        img {
          height: 20px !important;
          width: 20px !important;
        }
      }

      .unFollow_status {
        background-color: #ff696a;
        height: 43px;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        border-radius: 50px;
        flex-shrink: 0;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;

        &:hover {
          background-color: #fe5252;
        }
      }
    }

    &_marketplace {
      color: var.$black !important;

      .mini_card {
        border: 1px solid #06060666;
        padding: 10px;
        border-radius: 15px;
        margin-bottom: 10px;
      }

      .main_inform {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 6px;
      }

      .star-rating {
        font-size: 14px;
        font-weight: 600;
      }

      .most_recent,
      .time {
        color: var.$black !important;
        font-size: 14px;
        font-weight: 600;
      }

      .profile_photo {
        background-color: #0000001a;
        height: 50px;
        width: 50px;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        flex-shrink: 0;
      }

      h6 {
        color: var.$black !important;
        font-size: 18px;
        font-weight: 400;
        font-family: "Gilroy-Semibold", sans-serif;
      }

      .mini_sc_title {
        display: none;
      }

      @media (max-width: 767px) {
        h6 {
          display: none;
        }

        .mini_sc_title {
          display: block;
        }
      }

      .small_tag {
        color: var.$baseclr !important;
        font-size: 15px;
        font-weight: 600;
      }

      .time {
        white-space: nowrap;
      }

      .thumbs_text {
        color: var.$black !important;
        font-size: 14px;
        font-weight: 400;
        font-family: "Gilroy-Semibold", sans-serif;
      }
    }

    &_active_listing {
      color: var.$black !important;

      .mini_card {
        border: 1px solid #06060666;
        padding: 10px;
        border-radius: 15px;
        margin-bottom: 10px;
      }

      .main_inform {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 6px;
      }

      .star-rating {
        font-size: 14px;
        font-weight: 600;
      }

      .most_recent {
        color: var.$black !important;
        font-size: 14px;
        font-weight: 600;
      }

      .respon_sell_feedback {
        align-items: start;
        gap: 17px;

        @media (max-width: 900px) {
          display: block;

          h6 {
            margin-top: 10px;
          }

          .activeListing_photo {
            max-width: 100%;
            height: 150px;
          }
        }
      }

      .activeListing_photo {
        width: 100%;
        aspect-ratio: 1 / 1;
        max-width: 158px;
        border-radius: 5px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      h6 {
        color: var.$black !important;
        font-size: 18px;
        font-weight: 400;
        font-family: "Gilroy-Semibold", sans-serif;
      }

      .inner_price_text {
        color: var.$black !important;
        font-size: 15px;
        font-weight: 600;
      }

      .round-border-btn,
      .rounded-border-btn {
        border: 1px solid #00000033;
        background-color: #0000000d;
        padding: 10px 10px 10px 10px;
        gap: 5px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        display: flex;
        transition: all 0.4s ease-in-out;

        &:hover {
          border: 1px solid rgba(0, 0, 0, 0.477);
        }
      }

      .rounded-border-btn {
        padding: 10px 18px 10px 18px !important;
      }

      .round-bluefill-btn {
        background-color: var.$baseclr;
        color: white;
        padding: 5px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 50px;
        transition: 0.3s ease-in-out;

        &:hover {
          background-color: var.$baseclrhover;
        }
      }

      .dropdownlist {
        display: flex;
        align-items: center;
        gap: 12px;
        padding-top: 7px;
        padding-bottom: 7px;
        font-size: 14px;
        font-weight: 600;

        img {
          width: 18px !important;
          height: 18px !important;
          min-width: 18px !important;
        }

        span {
          text-align: left;
          flex: 1;
        }
      }
    }

    &_dash_listings {
      color: var.$black !important;

      .mini_card {
        border: 1px solid #06060666;
        padding: 10px;
        border-radius: 15px;
        margin-bottom: 10px;
      }

      .main_inform {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 6px;
      }

      .star-rating {
        font-size: 14px;
        font-weight: 600;
      }

      .most_recent {
        color: var.$black !important;
        font-size: 14px;
        font-weight: 600;
      }

      .respon_sell_feedback {
        align-items: start;
        gap: 17px;

        @media (max-width: 767px) {
          display: block;

          h6 {
            margin-top: 10px;
          }

          .activeListing_photo {
            max-width: 100%;
            height: 150px;
          }
        }
      }

      .activeListing_photo {
        width: 100%;
        aspect-ratio: 1 / 1;
        max-width: 158px;
        border-radius: 5px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      h6 {
        color: var.$black !important;
        font-size: 18px;
        font-weight: 400;
        font-family: "Gilroy-Semibold", sans-serif;
      }

      .inner_price_text {
        color: var.$black !important;
        font-size: 15px;
        font-weight: 600;
      }

      .actions_btn {
        width: 100%;
        justify-content: space-between;
        display: flex;
        margin-top: 50px;
        flex-direction: row-reverse;
        flex-wrap: wrap;

        @media (max-width: 576px) {
          margin-top: 12px;
        }

        .first_part {
          position: relative;
          display: flex;
          gap: 8px;
          margin-bottom: 12px;

          @media (max-width: 576px) {
            justify-content: end;
          }
        }

        .second_part {
          display: flex;
          gap: 8px;

          @media (max-width: 576px) {
            justify-content: end;
            width: 100%;

            button {
              width: 50%;
            }
          }
        }

        .round-border-btn,
        .rounded-border-btn {
          border: 1px solid #00000033;
          background-color: #0000000d;
          padding: 6px 12px 6px 12px;
          gap: 5px;
          font-size: 14px;
          font-weight: 600;
          transition: all 0.4s ease-in-out;
          border-radius: 50px;
          width: auto;
          height: 35px;
          display: flex;
          align-items: center;
          justify-content: center;
          display: inline-flex;

          &:hover {
            border: 1px solid rgba(0, 0, 0, 0.477);
          }
        }

        .rounded-border-btn {
          padding: 10px 15px !important;
        }

        .round-bluefill-btn {
          background-color: var.$baseclr;
          color: white;
          padding: 6px 18px;
          font-size: 14px;
          font-weight: 600;
          border-radius: 50px;
          transition: 0.3s ease-in-out;
          height: 35px;
          width: fit-content;
          align-items: center;
          justify-content: center;
          display: inline-flex;
          white-space: nowrap;

          &:hover {
            background-color: var.$baseclrhover;
          }
        }

        .dropdownlist {
          display: flex;
          align-items: center;
          gap: 14px;
          padding-top: 7px;
          padding-bottom: 7px;
          color: black;
          font-size: 14px;
          font-weight: 600;

          img {
            width: 18px !important;
            height: 18px !important;
            min-width: 18px !important;
          }

          span {
            text-align: left;
            flex: 1;
          }

          &:hover {
            color: #00adef;
          }
        }
      }
    }

    &_insight {
      color: var.$black !important;

      .row {
        border-bottom: 1px solid #00000033;
      }

      .row:last-child {
        border-bottom: none;
      }

      .wrap_div {
        display: flex;
        gap: 7px;
        align-items: start;
        padding: 10px 2px;

        @media (max-width: 323) {
          gap: 5px;
        }

        img {
          margin-top: 2.3px;
          flex-shrink: 0;
        }
      }

      p {
        color: var.$black;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        gap: 10px;
        align-items: start;
      }
    }

    &_disputes {
      color: var.$black !important;

      .lg_screen_table {
        table {
          border-collapse: separate;
          width: 100%;
          table-layout: fixed;
          margin-bottom: 0px;

          tr:last-child {
            border-bottom: 0px solid white;
          }

          th {
            color: white;
            vertical-align: middle;
            font-size: 14px;
            font-weight: 600;
            background-color: var.$clr031940;
            border-radius: 15px;
            padding: 10px;
            border-right: 4px solid white;
            border-left: 1.5px solid white;
            margin: 0px 10px;

            .th-inner {
              display: flex;
              gap: 8px;
            }
          }

          td {
            color: #000000;
            padding: 10px;
            font-size: 14px;
            font-weight: 600;
            border-right: 1px solid #00000033;
            margin-bottom: 5px;
            word-wrap: break-word;
          }

          td:last-child {
            border-right: none;
          }

          .view_res_btn {
            font-size: 14px;
            font-weight: 600;
            min-width: 100% !important;
            width: 100% !important;
            min-height: auto !important;
            height: auto !important;
            border-radius: 20px !important;
            padding: 8px 11px !important;
          }
        }

        .bullet-points {
          display: flex;
          align-items: start;
          gap: 8px;
          margin-bottom: 8px;

          img {
            flex-shrink: 0;
            margin-top: 7px;
          }
        }
      }

      .sm_screen_table {
        .wrap-div {
          border-bottom: 1px solid #00000066;
          margin-bottom: 10px;
        }

        .wrap-div:last-child {
          border-bottom: none;
        }

        .row {
          .colunm_head {
            color: white;
            background-color: #031940;
            padding: 10px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            gap: 4px;
            border-radius: 15px;
          }

          .colunm_value {
            color: black;
            font-size: 14px;
            font-weight: 600;
            border-right: 1px solid #00000033;
            margin-top: 3px;
            margin-bottom: 3px;
            padding: 0 7px;
            word-wrap: break-word;
          }

          .colunm_value:last-child,
          .colunm_value:nth-child(4) {
            border: none;
          }

          .arrow-header {
            width: 30px;
          }
        }

        .bullet-points {
          display: flex;
          align-items: start;
          gap: 8px;
          margin-bottom: 8px;

          img {
            flex-shrink: 0;
            margin-top: 7px;
          }
        }
      }

      .no_disputes {
        font-size: 18px;
        font-weight: 600;
        color: black;
      }

      .table_form_textarea {
        background-color: #4f5e7a;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        font-weight: 600;
        width: 100%;
        padding: 5px 8px;

        &:focus {
          outline: none;
          background-color: #576887;
        }
      }

      .character-count {
        text-align: end;
        margin-top: 2px;
        color: #4f5e7a;
        font-size: 14px;
      }

      .file-upload-wrapper {
        display: inline-block;
        width: 100%;
        border: 2px solid #ddd;
        border-radius: 9999px;
        padding: 6px 20px;
        font-size: 14px;
        font-weight: 600;
        background-color: white;
        color: black;
        cursor: pointer;
        transition: background-color 0.2s ease;
        height: 37px;
        text-align: center;
      }

      .file-upload-wrapper:hover {
        background-color: #f0f0f0;
      }

      .file-upload-wrapper input[type="file"] {
        display: none;
      }
    }

    &_timeline {
      color: var.$black !important;

      h2 {
        color: white;
        font-size: 14px;
        font-weight: 600;
        padding: 0;
        background-color: var.$clr031940;
        border-radius: 15px;
        padding: 12px;
        display: inline-flex;
      }

      ul {
        padding-left: 17px;
        margin: 13px 0px;

        li {
          color: black;
          font-size: 14px;
          font-weight: 600;
          border-radius: 15px;
          list-style-type: circle;
        }
      }
    }

    &_create_listing {
      .form-wrapper {

        .form-input,
        .form-select,
        .form-textarea {
          padding: 1rem;
          border-radius: 15px;
          border: none;
          width: 100%;
          color: black;
          font-size: 18px;
          margin-bottom: 12px;

          @media (max-width: 550px) {
            font-size: 14px;
          }

          &::placeholder {
            color: #000000cc;
          }

          &:focus {
            outline: none;
          }
        }

        .tags-input {
          background: white;
          display: flex;
          flex-wrap: wrap;
          padding: 0.8rem 1rem;
          border-radius: 15px;
          margin-bottom: 12px;
          gap: 10px;

          input {
            color: black;
            width: 100%;
            font-size: 18px;
            border: none;

            @media (max-width: 501px) {
              font-size: 15px;
            }

            &::placeholder {
              color: #000000cc;
            }

            &:focus {
              outline: none;
            }
          }

          .tag {
            background-color: #e6e6e6;
            padding: 5px 9px !important;
            color: black;
            border-radius: 50px !important;
            gap: 12px !important;
            display: flex;
            align-items: center;
            font-size: 18px;

            svg {
              cursor: pointer;
              font-weight: 700;
            }
          }
        }

        .checkbox-wrapper {
          display: flex;
          align-items: center;
          gap: 0.6rem;
          margin-bottom: 11px;

          .custom_checkbox_input {
            height: 20px;
            width: 20px;
          }

          .custom_checkbox_input:checked {
            background-color: #00adef !important;
            border: 1px solid #00adef !important;
          }
        }

        .file-upload-wrapper {
          display: flex;
          width: 100%;
          height: 46px;
          align-items: center;
          justify-content: center;
          border: 2px solid #ddd;
          border-radius: 9999px;
          padding: 6px 20px;
          margin-bottom: 12px;
          font-size: 16px;
          font-weight: 600;
          background-color: white;
          color: black;
          cursor: pointer;
          transition: background-color 0.2s ease;

          @media (max-width: 501px) {
            font-size: 15px;
          }
        }

        .file-upload-wrapper:hover {
          background-color: #f0f0f0;
        }

        .file-upload-wrapper input[type="file"] {
          display: none;
        }

        .character-count {
          font-size: 0.9rem;
          font-weight: 600;
          color: #000000cc;
          position: absolute;
          bottom: 18;
          right: 11;
        }

        .outer-character-count {
          text-align: end;
          margin-bottom: 12px;
          font-size: 0.9rem;
          font-weight: 600;
          color: #ffffffcc;
        }

        .upload-container {
          height: 100%;

          @media (width<=991px) {
            margin-bottom: 20px;
          }

          .upload-box {
            background: #fff;
            border-radius: 12px;
            padding: 0.5rem;
            text-align: center;
            cursor: pointer;
            height: 87%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            font-size: 20px;
            font-weight: 600;


            @media (767px<=width<=991px) {
              height: 300px;
            }

            @media (width<=768px) {
              height: 200px;
            }

            .upload-placeholder {
              img {
                margin: 0 auto;
              }

              p {
                font-size: 20px;
                font-weight: 600;
                margin: 0.35rem 0 0 0;
                color: black;
              }

              .sub-text {
                font-size: 15px !important;
                margin: 0;
              }
            }

            .preview {
              position: relative;
              width: 100%;
              height: 100%;
              overflow-y: hidden;
              display: flex;
              justify-content: center;
              align-items: center;

              img {
                width: 100%;
                height: 80%;
                object-fit: contain;
                border-radius: 8px;
              }

              .remove-btn {
                position: absolute;
                top: 55%;
                right: 46%;
                transform: translate(-50%, -50%);
                background: #ff4d4f;
                border: none;
                color: white;
                font-weight: bold;
                border-radius: 50%;
                width: 25px;
                height: 25px;
                cursor: pointer;
                font-size: 14px;
              }
            }
          }

          .image-count {
            margin: 0.5rem 0;
            font-size: 15px;
            font-weight: 600;
            color: white;
          }
        }

        .form-multi-select {
          color: black;
          font-size: 18px;
          border-radius: 15px;
        }

        // .form-select-badge {
        //   font-size: 18px;
        //   margin-top: 5px;
        //   margin-bottom: 5px;
        //   border-radius: 15px;
        //   color: black;
        // }
        .select__control {
          padding: 9px;
          border-radius: 15px;
          font-size: 18px;
        }

        .select__placeholder {
          color: black !important;
        }

        .select__multi-value {
          border-radius: 20px;
          padding: 2px 6px;
          font-size: 18px;
        }

        .select__multi-value__label {
          font-size: 18px;
          border-radius: 5px;
        }

        .select__multi-value__remove {
          color: #000;
          border-radius: 50%;
          padding-left: 9px;
          padding-right: 9px;
        }

        .select__multi-value__remove:hover {
          background-color: lightgray;
          border-radius: 50%;
          padding-left: 9px;
          padding-right: 9px;
        }
      }
    }
  }
}

.listing-rightside {
  @media (width>991px) {
    margin-top: 36px;
  }
}

.create-listing-categories {
  position: relative;

  .field {
    cursor: pointer;
    background-color: #fff;
    padding: 1rem;
    border-radius: 15px;
    border: none;
    width: 100%;
    color: #000;
    font-size: 18px;
    margin-bottom: 12px;
    display: flex;

    @media (max-width: 550px) {
      font-size: 14px;
    }
  }

  .category-toggle {
    width: 100%;
  }

  .dropdown {
    position: absolute;
    background-color: #fff;
    border-radius: 15px;
    border: none;
    width: 100%;
    color: #000;
    font-size: 18px;
    left: 0;
    top: 65px;
    z-index: 999;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;

    .dropdown-header {
      padding: 0.5rem;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e5e7eb;
      cursor: pointer;

      .dropdown-title {
        width: 100%;
        text-align: center;
      }
    }

    ul {
      li {
        padding: 1rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media (max-width: 550px) {
          padding: 0.75rem;
          font-size: 14px;
        }

        &:hover {
          background-color: #e6e6e6 !important;

          &:first-child {
            border-radius: 15px 15px 0 0;
          }

          &:last-child {
            border-radius: 0 0 15px 15px;
          }
        }
      }
    }

    .sub_list {
      li {
        &:hover {
          &:first-child {
            border-radius: 0 !important;
          }
        }
      }
    }
  }

  .selected-values-container {
    .selected-values {
      width: fit-content;
      padding: 5px 15px;
      background-color: #00adef;
      border-radius: 15px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: 10px;
      margin-bottom: 12px;

      .values {
        display: flex;
        align-items: center;
        gap: 8px;

        @media (max-width: 550px) {
          display: block;
        }

        span {
          gap: 5px;
          font-size: 14px;
          font-weight: 700;
          display: flex;
          align-items: center;
        }
      }

      .remove-selected {
        svg {
          cursor: pointer;
          width: 1.3rem;
          height: 1.3rem;
        }
      }
    }

    .clear-all {
      background-color: #ff696a;
      color: #fff;
      border-radius: 15px;
      margin-bottom: 12px;
      padding: 5px 10px;
      font-size: 14px;
      font-weight: 700;

      &:hover {
        background-color: #e65f60;
      }
    }
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}