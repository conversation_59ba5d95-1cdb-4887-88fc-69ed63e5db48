{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Blog.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.blog {\r\n  position: relative;\r\n  padding: 5rem 0;\r\n\r\n  &_cards {\r\n    margin-bottom: 50px !important;\r\n\r\n    @media (max-width: 767px) {\r\n      margin-bottom: 20px !important;\r\n      // max-width: 500px;\r\n      // margin: 0 auto;\r\n    }\r\n\r\n    @media (max-width: 575px) {\r\n      margin-bottom: 10px !important;\r\n      // max-width: 250px;\r\n      // margin: 0 auto;\r\n    }\r\n\r\n    .slider-container {\r\n      position: relative;\r\n\r\n      .slick-slider {\r\n        margin: 0 -15px;\r\n\r\n        .slick-list {\r\n          .slick-track {\r\n            display: flex;\r\n\r\n            .slick-slide {\r\n              padding: 0 15px;\r\n              display: flex;\r\n              height: auto;\r\n\r\n              &>div {\r\n                width: 100%;\r\n                display: flex;\r\n              }\r\n\r\n              &.slick-active {\r\n                padding-top: 0;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_postcard {\r\n    position: relative;\r\n    background-color: var.$clr032251;\r\n    border-radius: 1.25rem;\r\n    border: 1px solid rgba(255, 255, 255, 0.5);\r\n    cursor: pointer;\r\n    height: 100%;\r\n\r\n    @media (max-width: 767px) {\r\n      margin-bottom: 1.25rem;\r\n    }\r\n\r\n    &:hover {\r\n      .blog_postcard_img {\r\n        img {\r\n          transform: scale(1.1);\r\n        }\r\n      }\r\n    }\r\n\r\n    &_img {\r\n      border-top-left-radius: 1.25rem;\r\n      border-top-right-radius: 1.25rem;\r\n      overflow: hidden;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: \"\";\r\n        background-color: hsla(0, 0%, 100%, 0.6);\r\n        border-top-left-radius: 1.25rem;\r\n        border-top-right-radius: 1.25rem;\r\n        display: block;\r\n        padding-top: 54.25%;\r\n        width: 100%;\r\n      }\r\n\r\n      &_overlay {\r\n        bottom: 0;\r\n        display: block;\r\n        height: 100%;\r\n        left: 0;\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n        overflow: hidden;\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n      }\r\n\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: cover;\r\n        border-top-left-radius: 1.25rem;\r\n        border-top-right-radius: 1.25rem;\r\n        transition: all 0.3s ease-in-out;\r\n      }\r\n    }\r\n\r\n    &_content {\r\n      padding: 0.65rem 1.25rem 2rem;\r\n\r\n      @media (max-width: 1199px) {\r\n        padding: 0.65rem 1rem;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.25rem;\r\n        font-weight: 600;\r\n        line-height: 1.5rem;\r\n        letter-spacing: -0.1px;\r\n        margin-bottom: 0.625rem;\r\n        color: var.$clrc5c5d5;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 1rem;\r\n          line-height: 1.25rem;\r\n        }\r\n      }\r\n\r\n      p {\r\n        font-size: 1.5rem;\r\n        font-weight: 400;\r\n        line-height: 2rem;\r\n        letter-spacing: -0.1px;\r\n        color: var.$white;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 15px;\r\n          line-height: 23px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .slider-container .slick-slider .slick-arrow.slick-prev {\r\n    @media (max-width: 767px) {\r\n      left: -45px !important;\r\n      top: 100px !important;\r\n    }\r\n\r\n    &.slick-disabled {\r\n      background-color: #414c60;\r\n      opacity: 0.7;\r\n    }\r\n  }\r\n\r\n  .slider-container .slick-slider .slick-arrow.slick-next {\r\n    @media (max-width: 767px) {\r\n      right: -30px !important;\r\n      top: 100px !important;\r\n    }\r\n\r\n    &.slick-disabled {\r\n      background-color: #414c60;\r\n      opacity: 0.7;\r\n    }\r\n  }\r\n}\r\n\r\n.recent_post {\r\n  position: relative;\r\n  background-color: var.$clr032251;\r\n  border-radius: 1.25rem;\r\n  border: 1px solid rgba(8, 5, 5, 0.5);\r\n  margin-bottom: 30px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  cursor: pointer;\r\n  width: 100%;\r\n\r\n  h1 {\r\n    margin-bottom: 50px;\r\n    font-size: 3rem;\r\n    font-weight: 800;\r\n\r\n    @media (max-width: 767px) {\r\n      margin-bottom: 30px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    margin-bottom: 1.25rem;\r\n  }\r\n\r\n  &_img {\r\n    border-top-left-radius: 1.25rem;\r\n    border-bottom-left-radius: 1.25rem;\r\n    overflow: hidden;\r\n    position: relative;\r\n    width: 366px;\r\n\r\n    @media (max-width: 991px) {\r\n      width: 40%;\r\n    }\r\n\r\n    @media (max-width: 599px) {\r\n      width: 100%;\r\n      border-top-left-radius: 1.25rem;\r\n      border-top-right-radius: 1.25rem;\r\n      border-bottom-left-radius: 0;\r\n\r\n      &::before {\r\n        content: \"\";\r\n        background-color: hsla(0, 0%, 100%, 0);\r\n        border-top-left-radius: 1.25rem;\r\n        border-top-right-radius: 1.25rem;\r\n        display: block;\r\n        padding-top: 54.25%;\r\n        width: 100%;\r\n      }\r\n    }\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n\r\n      @media (max-width: 767px) {\r\n        bottom: 0;\r\n        display: block;\r\n        height: 100%;\r\n        left: 0;\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n        overflow: hidden;\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_content {\r\n    width: calc(100% - 366px);\r\n    padding: 2rem 1.25rem;\r\n    word-wrap: break-word; // Ensure long words break and wrap\r\n    overflow-wrap: break-word; // Ensure content wraps to the next line\r\n\r\n    @media (max-width: 991px) {\r\n      width: 60%;\r\n      padding: 1rem 1.25rem;\r\n    }\r\n\r\n    @media (max-width: 599px) {\r\n      width: 100%;\r\n      padding: 2rem 1rem;\r\n    }\r\n\r\n    small {\r\n      display: block;\r\n    }\r\n\r\n    h4 {\r\n      margin-block: 0.625rem;\r\n    }\r\n\r\n    p,\r\n    small {\r\n      font-size: 1.125rem;\r\n      font-weight: 400;\r\n      line-height: 27px;\r\n      color: var.$clrc5c5d5;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_time {\r\n    color: var.$baseclr;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    line-height: 26px;\r\n    display: block;\r\n    margin-top: 10px;\r\n\r\n    @media (max-width: 767px) {\r\n      font-size: 0.9rem;\r\n    }\r\n  }\r\n}\r\n\r\n.recontPostTitle {\r\n  margin-bottom: 50px;\r\n  font-size: 3rem;\r\n  font-weight: 800;\r\n\r\n  @media (max-width: 767px) {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\n\r\n.slick-slider .slick-prev,\r\n.slick-slider .slick-next {\r\n  position: absolute !important;\r\n  top: 50%;\r\n  display: block;\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 0;\r\n  -webkit-transform: translate(0, -50%) !important;\r\n  -ms-transform: translate(0, -50%) !important;\r\n  transform: translateY(-50%) !important;\r\n  cursor: pointer;\r\n  color: transparent;\r\n  border: none;\r\n  outline: none;\r\n  background: transparent;\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;AAIE;;;;AAGE;EAHF;;;;;AASE;EATF;;;;;AAeE;;;;AAGE;;;;AAII;;;;AAGE;;;;;;AAKE;;;;;AAKA;;;;AAUZ;;;;;;;;;AAQE;EARF;;;;;AAcM;;;;AAMJ;;;;;;;AAME;;;;;;;;;;AAUA;;;;;;;;;;;;;AAaA;;;;;;;;;AAUF;;;;AAGE;EAHF;;;;;AAOE;;;;;;;;;AAQE;EARF;;;;;;AAcA;;;;;;;;AAOE;EAPF;;;;;;AAiBF;EADF;;;;;;AAME;;;;;AAOA;EADF;;;;;;AAME;;;;;AAOJ;;;;;;;;;;;;AAWE;;;;;;AAKE;EALF;;;;EAMI;;;;;AAjBN;;;;;;;;AA6BI;EACA;;;;;AALF;EAQI;;;;;;;EAOA;;;;;;;;;;;AAQE;;;;;;AAOF;EACA;;;;;;;;;;;;;;AAUE;;;;;;;AAUJ;EACA;;;;;;AAGE;EACA;;;;;;AAIA;;;;AAIF;;;;;AAIA;;;;;;;AAQE;EAhOM;;;;;AA4NR;;;;;;;;;AAkBA;EACA;;;;;AANF;;;;;;AAgBA;EACA;;;;;;AAGE"}}]}