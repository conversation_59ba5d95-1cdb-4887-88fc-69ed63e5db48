/* [project]/src/css/Home/Switch.scss.css [app-client] (css) */
.form-check-input {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  transition: background-color .3s;
  position: relative;
  background-color: #fff !important;
  border: none !important;
  border-radius: 30px !important;
  width: 72px !important;
  height: 34px !important;
}

.form-check-input:before {
  content: "";
  background-color: #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  transition: transform .3s ease-in-out;
  position: absolute;
  top: 2px;
  left: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .3);
}

.form-check-input:checked {
  background-color: #00adef !important;
}

.form-check-input:checked:before {
  transform: translateX(38px);
}

.form-check-input:focus {
  box-shadow: none !important;
}

/*# sourceMappingURL=src_css_Home_Switch_scss_css_e59ae46c._.single.css.map*/