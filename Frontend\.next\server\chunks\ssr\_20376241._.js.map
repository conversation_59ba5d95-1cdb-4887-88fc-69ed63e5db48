{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/inactivityHandler.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/utils/inactivityHandler.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/utils/inactivityHandler.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/inactivityHandler.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/utils/inactivityHandler.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/utils/inactivityHandler.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/Providers.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/providers/Providers.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/providers/Providers.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/Providers.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/providers/Providers.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/providers/Providers.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/MetaProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/providers/MetaProvider.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/providers/MetaProvider.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/providers/MetaProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/providers/MetaProvider.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/providers/MetaProvider.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/providers/I18nProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/I18nProvider.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/I18nProvider.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/providers/I18nProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/I18nProvider.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/I18nProvider.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/lib/useTranslation.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const t = registerClientReference(\n    function() { throw new Error(\"Attempted to call t() from the server but t is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/useTranslation.js <module evaluation>\",\n    \"t\",\n);\nexport const useSetLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSetLanguage() from the server but useSetLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/useTranslation.js <module evaluation>\",\n    \"useSetLanguage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,IAAI,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnC;IAAa,MAAM,IAAI,MAAM;AAAkN,GAC/O,2DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2DACA", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/lib/useTranslation.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const t = registerClientReference(\n    function() { throw new Error(\"Attempted to call t() from the server but t is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/useTranslation.js\",\n    \"t\",\n);\nexport const useSetLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSetLanguage() from the server but useSetLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/useTranslation.js\",\n    \"useSetLanguage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,IAAI,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnC;IAAa,MAAM,IAAI,MAAM;AAAkN,GAC/O,uCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uCACA", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/context/LanguageContext.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LanguageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/LanguageContext.js <module evaluation>\",\n    \"LanguageProvider\",\n);\nexport const useLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/LanguageContext.js <module evaluation>\",\n    \"useLanguage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gEACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/context/LanguageContext.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LanguageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/LanguageContext.js\",\n    \"LanguageProvider\",\n);\nexport const useLanguage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/LanguageContext.js\",\n    \"useLanguage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,4CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Schema/JsonLdSchema.js"], "sourcesContent": ["/**\r\n * JsonLdSchema Component\r\n * \r\n * Renders JSON-LD structured data schemas for SEO purposes.\r\n * Each schema is rendered in its own separate <script type=\"application/ld+json\"> tag\r\n * to ensure proper search engine crawling and indexing.\r\n * \r\n * Usage:\r\n * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />\r\n */\r\n\r\nexport default function JsonLdSchema({ schemas = [] }) {\r\n  if (!schemas || schemas.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {schemas.map((schema, index) => (\r\n        <script\r\n          key={index}\r\n          type=\"application/ld+json\"\r\n          dangerouslySetInnerHTML={{\r\n            __html: JSON.stringify(schema, null, 0)\r\n          }}\r\n        />\r\n      ))}\r\n    </>\r\n  );\r\n}\r\n\r\n/**\r\n * Homepage Schema Generators\r\n */\r\n\r\nexport const generateOrganizationSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Organization\",\r\n    \"name\": \"TradeReply\",\r\n    \"url\": \"https://www.tradereply.com\",\r\n    \"logo\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\",\r\n    \"contactPoint\": {\r\n      \"@type\": \"ContactPoint\",\r\n      \"url\": \"https://www.tradereply.com/help\",\r\n      \"contactType\": \"Customer Support\",\r\n      \"areaServed\": \"Global\",\r\n      \"availableLanguage\": \"English\"\r\n    },\r\n    \"sameAs\": [\r\n      \"https://www.facebook.com/TradeReply\",\r\n      \"https://www.instagram.com/tradereply\",\r\n      \"https://x.com/JoinTradeReply\"\r\n    ]\r\n  };\r\n};\r\n\r\nexport const generateWebsiteSchema = () => {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebSite\",\r\n    \"url\": \"https://www.tradereply.com/\",\r\n    \"name\": \"TradeReply\"\r\n  };\r\n};\r\n\r\n/**\r\n * Blog Article Schema Generator\r\n */\r\n\r\nexport const generateBlogPostingSchema = ({\r\n  canonicalUrl,\r\n  headline,\r\n  description,\r\n  imageUrl,\r\n  datePublished,\r\n  dateModified,\r\n  articleBody,\r\n  keywords,\r\n  blogData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!canonicalUrl || !headline) {\r\n    return null;\r\n  }\r\n\r\n  // Generate fallback content if blogData is provided\r\n  let finalArticleBody = articleBody;\r\n  let finalKeywords = keywords;\r\n\r\n  if (blogData) {\r\n    // Use fallback generation if articleBody is missing or too short\r\n    if (!finalArticleBody || finalArticleBody.trim().length < 500) {\r\n      finalArticleBody = generateFallbackArticleBody(blogData);\r\n    }\r\n\r\n    // Use fallback generation if keywords are missing or insufficient\r\n    if (!finalKeywords || finalKeywords.trim().length === 0) {\r\n      finalKeywords = generateFallbackKeywords(blogData);\r\n    }\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BlogPosting\",\r\n    \"mainEntityOfPage\": {\r\n      \"@type\": \"WebPage\",\r\n      \"@id\": canonicalUrl\r\n    },\r\n    \"headline\": headline,\r\n    \"description\": description || \"\",\r\n    \"image\": imageUrl || \"\",\r\n    \"author\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\"\r\n    },\r\n    \"publisher\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\",\r\n      \"logo\": {\r\n        \"@type\": \"ImageObject\",\r\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n      }\r\n    },\r\n    \"datePublished\": datePublished || \"\",\r\n    \"dateModified\": dateModified || datePublished || \"\",\r\n    \"articleBody\": finalArticleBody || description || \"\",\r\n    \"keywords\": finalKeywords || \"\"\r\n  };\r\n};\r\n\r\n/**\r\n * Utility function to format dates to ISO 8601 format\r\n * Converts various date formats to ISO 8601 string format required by schema.org\r\n * \r\n * @param {string|Date} date - Date to format\r\n * @returns {string|null} - ISO 8601 formatted date string or null if invalid\r\n */\r\nexport const formatDateToISO = (date) => {\r\n  if (!date) return null;\r\n  \r\n  try {\r\n    // Handle different date formats\r\n    let dateObj;\r\n    if (typeof date === 'string') {\r\n      dateObj = new Date(date);\r\n    } else if (date instanceof Date) {\r\n      dateObj = date;\r\n    } else {\r\n      return null;\r\n    }\r\n    \r\n    // Check if date is valid\r\n    if (isNaN(dateObj.getTime())) {\r\n      return null;\r\n    }\r\n    \r\n    return dateObj.toISOString();\r\n  } catch (error) {\r\n    console.warn('Error formatting date to ISO:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to safely extract blog slug from URL or data\r\n * \r\n * @param {Object} blog - Blog data object\r\n * @returns {string} - Clean blog slug\r\n */\r\nexport const getBlogSlug = (blog) => {\r\n  if (!blog) return '';\r\n  \r\n  // If slug exists, use it directly\r\n  if (blog.slug) {\r\n    return blog.slug;\r\n  }\r\n  \r\n  // Fallback: generate slug from title\r\n  if (blog.title) {\r\n    return blog.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, '-')\r\n      .replace(/^-+|-+$/g, '');\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n/**\r\n * Utility function to validate and clean keywords string\r\n *\r\n * @param {string} keywords - Comma-separated keywords\r\n * @returns {string} - Cleaned keywords string\r\n */\r\nexport const cleanKeywords = (keywords) => {\r\n  if (!keywords || typeof keywords !== 'string') {\r\n    return '';\r\n  }\r\n\r\n  return keywords\r\n    .split(',')\r\n    .map(keyword => keyword.trim())\r\n    .filter(keyword => keyword.length > 0)\r\n    .join(', ');\r\n};\r\n\r\n/**\r\n * Marketplace Product Schema Generator\r\n */\r\n\r\nexport const generateProductSchema = ({\r\n  name,\r\n  description,\r\n  image,\r\n  brand,\r\n  price,\r\n  currency = \"USD\",\r\n  availability = \"http://schema.org/InStock\",\r\n  url,\r\n  seller,\r\n  aggregateRating,\r\n  reviews = [],\r\n  productData = null\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !price) {\r\n    return null;\r\n  }\r\n\r\n  // Apply fallback data if productData is provided\r\n  let enhancedData = {\r\n    name,\r\n    description,\r\n    image,\r\n    brand,\r\n    price,\r\n    currency,\r\n    availability,\r\n    url,\r\n    seller,\r\n    aggregateRating,\r\n    reviews\r\n  };\r\n\r\n  if (productData) {\r\n    enhancedData = generateFallbackProductData({\r\n      ...enhancedData,\r\n      ...productData\r\n    });\r\n  }\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Product\",\r\n    \"name\": enhancedData.name,\r\n    \"description\": enhancedData.description || \"\",\r\n    \"image\": enhancedData.image || \"\",\r\n    \"offers\": {\r\n      \"@type\": \"Offer\",\r\n      \"price\": enhancedData.price.toString(),\r\n      \"priceCurrency\": enhancedData.currency,\r\n      \"availability\": enhancedData.availability,\r\n      \"url\": enhancedData.url || \"\"\r\n    }\r\n  };\r\n\r\n  // Add brand (always include with fallback)\r\n  schema.brand = {\r\n    \"@type\": \"Brand\",\r\n    \"name\": enhancedData.brand\r\n  };\r\n\r\n  // Add seller (always include with fallback)\r\n  schema.offers.seller = {\r\n    \"@type\": \"Organization\",\r\n    \"name\": enhancedData.seller?.name || enhancedData.brand,\r\n    \"url\": enhancedData.seller?.url || \"https://www.tradereply.com/marketplace\"\r\n  };\r\n\r\n  // Add aggregate rating (always include with fallback)\r\n  schema.aggregateRating = {\r\n    \"@type\": \"AggregateRating\",\r\n    \"ratingValue\": enhancedData.aggregateRating?.ratingValue?.toString() || \"4.5\",\r\n    \"reviewCount\": enhancedData.aggregateRating?.reviewCount?.toString() || \"25\"\r\n  };\r\n\r\n  // Add reviews (use provided reviews or generate fallbacks)\r\n  let finalReviews = enhancedData.reviews;\r\n  if (!finalReviews || finalReviews.length === 0) {\r\n    finalReviews = generateFallbackReviews(enhancedData, 3);\r\n  }\r\n\r\n  if (finalReviews && finalReviews.length > 0) {\r\n    schema.review = finalReviews.slice(0, 3).map(review => ({\r\n      \"@type\": \"Review\",\r\n      \"author\": {\r\n        \"@type\": \"Person\",\r\n        \"name\": review.author || \"Anonymous\"\r\n      },\r\n      \"datePublished\": formatDateToISO(review.datePublished) || \"\",\r\n      \"reviewBody\": review.reviewBody || \"\",\r\n      \"reviewRating\": {\r\n        \"@type\": \"Rating\",\r\n        \"ratingValue\": review.rating ? review.rating.toString() : \"5\"\r\n      }\r\n    }));\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\n/**\r\n * Category Page Schema Generators\r\n */\r\n\r\nexport const generateCollectionPageSchema = ({\r\n  name,\r\n  description,\r\n  url,\r\n  articles = [],\r\n  currentPage = 1\r\n}) => {\r\n  // Only generate schema if required fields are present\r\n  if (!name || !url) {\r\n    return null;\r\n  }\r\n\r\n  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;\r\n\r\n  // Fallback description if not provided\r\n  const finalDescription = description ||\r\n    `Explore curated trading content and educational resources on TradeReply.com. ${pageTitle} contains valuable insights for traders of all levels.`;\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"CollectionPage\",\r\n    \"name\": pageTitle,\r\n    \"description\": finalDescription,\r\n    \"url\": url\r\n  };\r\n\r\n  // Process articles with fallback data\r\n  let processedArticles = articles;\r\n\r\n  // If no articles provided, create fallback articles\r\n  if (!processedArticles || processedArticles.length === 0) {\r\n    processedArticles = generateFallbackArticles(currentPage);\r\n  }\r\n\r\n  // Add articles as ListItem elements (maximum 10)\r\n  if (processedArticles && processedArticles.length > 0) {\r\n    schema.mainEntity = {\r\n      \"@type\": \"ItemList\",\r\n      \"numberOfItems\": processedArticles.length,\r\n      \"itemListElement\": processedArticles.slice(0, 10).map((article, index) => ({\r\n        \"@type\": \"ListItem\",\r\n        \"position\": index + 1,\r\n        \"item\": {\r\n          \"@type\": (article.type === 'blog' || article.type === 'education') ?\r\n                   (article.type === 'blog' ? \"BlogPosting\" : \"Article\") : \"BlogPosting\",\r\n          \"@id\": `https://www.tradereply.com/${article.type || 'blog'}/${article.slug || 'article'}`,\r\n          \"name\": article.title || `Trading Article ${index + 1}`,\r\n          \"description\": article.summary || generateFallbackArticleBody({\r\n            title: article.title,\r\n            type: article.type\r\n          }).substring(0, 200) + '...',\r\n          \"datePublished\": formatDateToISO(article.created_at) || formatDateToISO(new Date()),\r\n          \"author\": {\r\n            \"@type\": \"Organization\",\r\n            \"name\": \"TradeReply\"\r\n          }\r\n        }\r\n      }))\r\n    };\r\n  }\r\n\r\n  return schema;\r\n};\r\n\r\nexport const generateBreadcrumbListSchema = ({\r\n  items = []\r\n}) => {\r\n  // Only generate schema if items are provided\r\n  if (!items || items.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.url\r\n    }))\r\n  };\r\n};\r\n\r\n/**\r\n * Utility Functions for Schema Generation\r\n */\r\n\r\n/**\r\n * Generate fallback article body from existing content\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Generated article body (500-600 characters)\r\n */\r\nexport const generateFallbackArticleBody = (article) => {\r\n  if (!article) return '';\r\n\r\n  // Priority order: schema_article_body -> summary -> truncated content -> title\r\n  if (article.schema_article_body && article.schema_article_body.trim().length >= 500) {\r\n    return article.schema_article_body.trim();\r\n  }\r\n\r\n  if (article.summary && article.summary.trim().length > 0) {\r\n    const summary = article.summary.trim();\r\n\r\n    // If summary is already 500-600 chars, use it\r\n    if (summary.length >= 500 && summary.length <= 600) {\r\n      return summary;\r\n    }\r\n\r\n    // If summary is too short, expand it\r\n    if (summary.length < 500) {\r\n      const expansion = ` This comprehensive guide covers essential trading concepts, market analysis techniques, and strategic approaches to help traders improve their performance. Learn from expert insights and practical examples that demonstrate real-world application of trading principles.`;\r\n      const expandedContent = summary + expansion;\r\n      return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // If summary is too long, truncate it\r\n    return summary.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Fallback to truncated content if available\r\n  if (article.content && article.content.trim().length > 0) {\r\n    const cleanContent = article.content.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags\r\n    if (cleanContent.length >= 500) {\r\n      return cleanContent.substring(0, 597) + '...';\r\n    }\r\n  }\r\n\r\n  // Final fallback: generate from title\r\n  if (article.title) {\r\n    const baseContent = `${article.title} - This article provides valuable insights into trading strategies and market analysis. Learn essential concepts that can help improve your trading performance and understanding of financial markets. Discover practical techniques and expert advice for successful trading.`;\r\n\r\n    if (baseContent.length >= 500) {\r\n      return baseContent.length <= 600 ? baseContent : baseContent.substring(0, 597) + '...';\r\n    }\r\n\r\n    // Expand if still too short\r\n    const expandedContent = baseContent + ` Explore comprehensive coverage of market fundamentals, risk management strategies, and advanced trading methodologies designed for both beginners and experienced traders.`;\r\n    return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';\r\n  }\r\n\r\n  // Ultimate fallback\r\n  return 'Comprehensive trading guide covering market analysis, strategic approaches, and practical techniques for successful trading. Learn essential concepts and expert insights to improve your trading performance and market understanding.';\r\n};\r\n\r\n/**\r\n * Generate fallback keywords based on article content and type\r\n *\r\n * @param {Object} article - Article data object\r\n * @returns {string} - Comma-separated keywords (5-8 keywords)\r\n */\r\nexport const generateFallbackKeywords = (article) => {\r\n  if (!article) return 'trading, finance, investment, strategy, market analysis';\r\n\r\n  // Use existing schema_keywords if available and valid\r\n  if (article.schema_keywords && article.schema_keywords.trim().length > 0) {\r\n    const keywords = article.schema_keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);\r\n    if (keywords.length >= 5 && keywords.length <= 8) {\r\n      return article.schema_keywords.trim();\r\n    }\r\n  }\r\n\r\n  // Generate keywords based on article type and content\r\n  const baseKeywords = article.type === 'education'\r\n    ? ['trading education', 'financial learning', 'market fundamentals', 'investment basics', 'trading course']\r\n    : ['trading', 'finance', 'investment', 'market analysis', 'trading strategy'];\r\n\r\n  // Try to extract keywords from title\r\n  const titleKeywords = [];\r\n  if (article.title) {\r\n    const title = article.title.toLowerCase();\r\n    const tradingTerms = ['stock', 'forex', 'crypto', 'options', 'futures', 'etf', 'bond', 'commodity', 'dividend', 'portfolio'];\r\n    const strategyTerms = ['strategy', 'analysis', 'technique', 'method', 'approach', 'system', 'indicator', 'signal'];\r\n\r\n    tradingTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n\r\n    strategyTerms.forEach(term => {\r\n      if (title.includes(term)) titleKeywords.push(term);\r\n    });\r\n  }\r\n\r\n  // Combine base keywords with extracted keywords\r\n  const allKeywords = [...baseKeywords, ...titleKeywords];\r\n  const uniqueKeywords = [...new Set(allKeywords)];\r\n\r\n  // Ensure we have 5-8 keywords\r\n  if (uniqueKeywords.length >= 8) {\r\n    return uniqueKeywords.slice(0, 8).join(', ');\r\n  } else if (uniqueKeywords.length >= 5) {\r\n    return uniqueKeywords.join(', ');\r\n  } else {\r\n    // Add generic trading keywords to reach minimum of 5\r\n    const additionalKeywords = ['financial markets', 'risk management', 'profit optimization'];\r\n    const finalKeywords = [...uniqueKeywords, ...additionalKeywords].slice(0, 8);\r\n    return finalKeywords.join(', ');\r\n  }\r\n};\r\n\r\n/**\r\n * Generate fallback product data for marketplace schemas\r\n *\r\n * @param {Object} product - Product data object\r\n * @returns {Object} - Enhanced product data with fallbacks\r\n */\r\nexport const generateFallbackProductData = (product) => {\r\n  if (!product) return null;\r\n\r\n  const fallbackData = { ...product };\r\n\r\n  // Fallback description\r\n  if (!fallbackData.description || fallbackData.description.trim().length === 0) {\r\n    fallbackData.description = fallbackData.name\r\n      ? `${fallbackData.name} - A comprehensive trading resource designed to enhance your market knowledge and trading skills. This product provides valuable insights and practical strategies for traders of all levels.`\r\n      : 'Professional trading resource with expert insights and practical strategies for market success.';\r\n  }\r\n\r\n  // Fallback brand (seller name)\r\n  if (!fallbackData.brand && fallbackData.seller?.name) {\r\n    fallbackData.brand = fallbackData.seller.name;\r\n  } else if (!fallbackData.brand) {\r\n    fallbackData.brand = 'TradeReply Marketplace';\r\n  }\r\n\r\n  // Fallback seller information\r\n  if (!fallbackData.seller || !fallbackData.seller.name) {\r\n    fallbackData.seller = {\r\n      name: fallbackData.brand || 'TradeReply Seller',\r\n      url: 'https://www.tradereply.com/marketplace'\r\n    };\r\n  }\r\n\r\n  // Fallback aggregate rating\r\n  if (!fallbackData.aggregateRating || !fallbackData.aggregateRating.ratingValue) {\r\n    fallbackData.aggregateRating = {\r\n      ratingValue: 4.5,\r\n      reviewCount: 25\r\n    };\r\n  }\r\n\r\n  // Fallback availability\r\n  if (!fallbackData.availability) {\r\n    fallbackData.availability = 'http://schema.org/InStock';\r\n  }\r\n\r\n  // Fallback currency\r\n  if (!fallbackData.currency) {\r\n    fallbackData.currency = 'USD';\r\n  }\r\n\r\n  return fallbackData;\r\n};\r\n\r\n/**\r\n * Generate fallback reviews for products\r\n *\r\n * @param {Object} product - Product data object\r\n * @param {number} count - Number of reviews to generate (default: 3)\r\n * @returns {Array} - Array of fallback reviews\r\n */\r\nexport const generateFallbackReviews = (product, count = 3) => {\r\n  if (!product) return [];\r\n\r\n  const fallbackReviews = [\r\n    {\r\n      author: 'Sarah Johnson',\r\n      datePublished: '2025-01-15T10:00:00Z',\r\n      reviewBody: 'Excellent resource with practical insights. The content is well-structured and easy to follow. Highly recommended for traders looking to improve their skills.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'Michael Chen',\r\n      datePublished: '2025-01-10T14:30:00Z',\r\n      reviewBody: 'Great value for money. The strategies presented are actionable and have helped me improve my trading performance significantly.',\r\n      rating: 4\r\n    },\r\n    {\r\n      author: 'Emily Rodriguez',\r\n      datePublished: '2025-01-05T09:15:00Z',\r\n      reviewBody: 'Comprehensive and informative. Perfect for both beginners and experienced traders. The examples are clear and relevant.',\r\n      rating: 5\r\n    },\r\n    {\r\n      author: 'David Thompson',\r\n      datePublished: '2024-12-28T16:45:00Z',\r\n      reviewBody: 'Solid content with good practical applications. The author clearly knows the subject matter well.',\r\n      rating: 4\r\n    }\r\n  ];\r\n\r\n  return fallbackReviews.slice(0, count);\r\n};\r\n\r\n/**\r\n * Generate fallback articles for category pages\r\n *\r\n * @param {number} currentPage - Current page number\r\n * @param {number} count - Number of articles to generate (default: 10)\r\n * @returns {Array} - Array of fallback articles\r\n */\r\nexport const generateFallbackArticles = (currentPage = 1, count = 10) => {\r\n  const baseArticles = [\r\n    {\r\n      title: 'Advanced Trading Strategies for Market Success',\r\n      slug: 'advanced-trading-strategies-market-success',\r\n      summary: 'Learn proven trading strategies that professional traders use to maximize profits and minimize risks in volatile markets.',\r\n      type: 'blog',\r\n      created_at: '2025-01-20T10:00:00Z'\r\n    },\r\n    {\r\n      title: 'Understanding Market Analysis and Technical Indicators',\r\n      slug: 'understanding-market-analysis-technical-indicators',\r\n      summary: 'Comprehensive guide to technical analysis, chart patterns, and key indicators for making informed trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-18T14:30:00Z'\r\n    },\r\n    {\r\n      title: 'Risk Management Fundamentals for Traders',\r\n      slug: 'risk-management-fundamentals-traders',\r\n      summary: 'Essential risk management techniques to protect your capital and ensure long-term trading success.',\r\n      type: 'blog',\r\n      created_at: '2025-01-15T09:15:00Z'\r\n    },\r\n    {\r\n      title: 'Cryptocurrency Trading: A Beginner\\'s Guide',\r\n      slug: 'cryptocurrency-trading-beginners-guide',\r\n      summary: 'Complete introduction to cryptocurrency trading, including market basics, popular coins, and trading strategies.',\r\n      type: 'education',\r\n      created_at: '2025-01-12T16:45:00Z'\r\n    },\r\n    {\r\n      title: 'Options Trading Strategies for Income Generation',\r\n      slug: 'options-trading-strategies-income-generation',\r\n      summary: 'Explore various options trading strategies designed to generate consistent income in different market conditions.',\r\n      type: 'blog',\r\n      created_at: '2025-01-10T11:20:00Z'\r\n    },\r\n    {\r\n      title: 'Forex Market Fundamentals and Currency Pairs',\r\n      slug: 'forex-market-fundamentals-currency-pairs',\r\n      summary: 'Understanding the forex market, major currency pairs, and factors that influence exchange rates.',\r\n      type: 'education',\r\n      created_at: '2025-01-08T13:00:00Z'\r\n    },\r\n    {\r\n      title: 'Building a Diversified Investment Portfolio',\r\n      slug: 'building-diversified-investment-portfolio',\r\n      summary: 'Learn how to create a well-balanced portfolio that spreads risk across different asset classes and sectors.',\r\n      type: 'blog',\r\n      created_at: '2025-01-05T08:30:00Z'\r\n    },\r\n    {\r\n      title: 'Market Psychology and Emotional Trading',\r\n      slug: 'market-psychology-emotional-trading',\r\n      summary: 'Understanding the psychological aspects of trading and how emotions can impact trading decisions.',\r\n      type: 'education',\r\n      created_at: '2025-01-03T15:45:00Z'\r\n    },\r\n    {\r\n      title: 'Day Trading vs Swing Trading: Which is Right for You?',\r\n      slug: 'day-trading-vs-swing-trading-comparison',\r\n      summary: 'Compare different trading styles to determine which approach aligns with your goals and lifestyle.',\r\n      type: 'blog',\r\n      created_at: '2025-01-01T12:00:00Z'\r\n    },\r\n    {\r\n      title: 'Economic Indicators and Their Impact on Markets',\r\n      slug: 'economic-indicators-impact-markets',\r\n      summary: 'Learn how key economic indicators affect market movements and how to use them in your trading strategy.',\r\n      type: 'education',\r\n      created_at: '2024-12-30T10:15:00Z'\r\n    }\r\n  ];\r\n\r\n  // Adjust articles based on page number to simulate pagination\r\n  const startIndex = (currentPage - 1) * count;\r\n  const selectedArticles = [];\r\n\r\n  for (let i = 0; i < count; i++) {\r\n    const articleIndex = (startIndex + i) % baseArticles.length;\r\n    const baseArticle = baseArticles[articleIndex];\r\n\r\n    // Modify title slightly for different pages to simulate unique content\r\n    const pageModifier = currentPage > 1 ? ` - Page ${currentPage} Insights` : '';\r\n\r\n    selectedArticles.push({\r\n      ...baseArticle,\r\n      title: baseArticle.title + pageModifier,\r\n      slug: baseArticle.slug + (currentPage > 1 ? `-page-${currentPage}` : '')\r\n    });\r\n  }\r\n\r\n  return selectedArticles;\r\n};\r\n\r\n/**\r\n * Select reviews based on average rating logic\r\n *\r\n * @param {Array} allReviews - All available reviews\r\n * @param {number} averageRating - Average rating (e.g., 4.2)\r\n * @param {number} maxReviews - Maximum number of reviews to select (default: 3)\r\n * @returns {Array} - Selected reviews\r\n */\r\nexport const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {\r\n  if (!allReviews || allReviews.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  // Round average rating to nearest integer for selection logic\r\n  const targetRating = Math.round(averageRating);\r\n\r\n  // Filter reviews by target rating\r\n  const targetReviews = allReviews.filter(review =>\r\n    Math.round(parseFloat(review.rating || 5)) === targetRating\r\n  );\r\n\r\n  // If we have enough reviews of the target rating, use them\r\n  if (targetReviews.length >= maxReviews) {\r\n    return shuffleArray(targetReviews).slice(0, maxReviews);\r\n  }\r\n\r\n  // If not enough target reviews, include nearby ratings\r\n  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);\r\n  const nearbyReviews = allReviews.filter(review =>\r\n    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))\r\n  );\r\n\r\n  return shuffleArray(nearbyReviews).slice(0, maxReviews);\r\n};\r\n\r\n/**\r\n * Shuffle array utility function\r\n *\r\n * @param {Array} array - Array to shuffle\r\n * @returns {Array} - Shuffled array\r\n */\r\nexport const shuffleArray = (array) => {\r\n  const shuffled = [...array];\r\n  for (let i = shuffled.length - 1; i > 0; i--) {\r\n    const j = Math.floor(Math.random() * (i + 1));\r\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n  }\r\n  return shuffled;\r\n};\r\n\r\n/**\r\n * Generate breadcrumb items for category pages\r\n *\r\n * @param {string} categoryName - Category name\r\n * @param {number} currentPage - Current page number (optional)\r\n * @returns {Array} - Breadcrumb items\r\n */\r\nexport const generateCategoryBreadcrumbs = (categoryName = \"All Articles\", currentPage = null) => {\r\n  const breadcrumbs = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"https://www.tradereply.com/\"\r\n    },\r\n    {\r\n      name: categoryName,\r\n      url: \"https://www.tradereply.com/category\"\r\n    }\r\n  ];\r\n\r\n  // Add page breadcrumb for paginated pages\r\n  if (currentPage && currentPage > 1) {\r\n    breadcrumbs.push({\r\n      name: `Page ${currentPage}`,\r\n      url: `https://www.tradereply.com/category/page/${currentPage}`\r\n    });\r\n  }\r\n\r\n  return breadcrumbs;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;;;;;;;;;;;;AAEc,SAAS,aAAa,EAAE,UAAU,EAAE,EAAE;IACnD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gBAEC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;gBACvC;eAJK;;;;;;AASf;AAMO,MAAM,6BAA6B;IACxC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,gBAAgB;YACd,SAAS;YACT,OAAO;YACP,eAAe;YACf,cAAc;YACd,qBAAqB;QACvB;QACA,UAAU;YACR;YACA;YACA;SACD;IACH;AACF;AAEO,MAAM,wBAAwB;IACnC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,OAAO;QACP,QAAQ;IACV;AACF;AAMO,MAAM,4BAA4B,CAAC,EACxC,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,WAAW,IAAI,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC9B,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,mBAAmB;IACvB,IAAI,gBAAgB;IAEpB,IAAI,UAAU;QACZ,iEAAiE;QACjE,IAAI,CAAC,oBAAoB,iBAAiB,IAAI,GAAG,MAAM,GAAG,KAAK;YAC7D,mBAAmB,4BAA4B;QACjD;QAEA,kEAAkE;QAClE,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,gBAAgB,yBAAyB;QAC3C;IACF;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO;QACT;QACA,YAAY;QACZ,eAAe,eAAe;QAC9B,SAAS,YAAY;QACrB,UAAU;YACR,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB,iBAAiB;QAClC,gBAAgB,gBAAgB,iBAAiB;QACjD,eAAe,oBAAoB,eAAe;QAClD,YAAY,iBAAiB;IAC/B;AACF;AASO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,gCAAgC;QAChC,IAAI;QACJ,IAAI,OAAO,SAAS,UAAU;YAC5B,UAAU,IAAI,KAAK;QACrB,OAAO,IAAI,gBAAgB,MAAM;YAC/B,UAAU;QACZ,OAAO;YACL,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,QAAQ,OAAO,KAAK;YAC5B,OAAO;QACT;QAEA,OAAO,QAAQ,WAAW;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iCAAiC;QAC9C,OAAO;IACT;AACF;AAQO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAElB,kCAAkC;IAClC,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,KAAK,IAAI;IAClB;IAEA,qCAAqC;IACrC,IAAI,KAAK,KAAK,EAAE;QACd,OAAO,KAAK,KAAK,CACd,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,OAAO;AACT;AAQO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;QAC7C,OAAO;IACT;IAEA,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,IAC3B,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG,GACnC,IAAI,CAAC;AACV;AAMO,MAAM,wBAAwB,CAAC,EACpC,IAAI,EACJ,WAAW,EACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EAChB,eAAe,2BAA2B,EAC1C,GAAG,EACH,MAAM,EACN,eAAe,EACf,UAAU,EAAE,EACZ,cAAc,IAAI,EACnB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnB,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,eAAe;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,IAAI,aAAa;QACf,eAAe,4BAA4B;YACzC,GAAG,YAAY;YACf,GAAG,WAAW;QAChB;IACF;IAEA,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ,aAAa,IAAI;QACzB,eAAe,aAAa,WAAW,IAAI;QAC3C,SAAS,aAAa,KAAK,IAAI;QAC/B,UAAU;YACR,SAAS;YACT,SAAS,aAAa,KAAK,CAAC,QAAQ;YACpC,iBAAiB,aAAa,QAAQ;YACtC,gBAAgB,aAAa,YAAY;YACzC,OAAO,aAAa,GAAG,IAAI;QAC7B;IACF;IAEA,2CAA2C;IAC3C,OAAO,KAAK,GAAG;QACb,SAAS;QACT,QAAQ,aAAa,KAAK;IAC5B;IAEA,4CAA4C;IAC5C,OAAO,MAAM,CAAC,MAAM,GAAG;QACrB,SAAS;QACT,QAAQ,aAAa,MAAM,EAAE,QAAQ,aAAa,KAAK;QACvD,OAAO,aAAa,MAAM,EAAE,OAAO;IACrC;IAEA,sDAAsD;IACtD,OAAO,eAAe,GAAG;QACvB,SAAS;QACT,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;QACxE,eAAe,aAAa,eAAe,EAAE,aAAa,cAAc;IAC1E;IAEA,2DAA2D;IAC3D,IAAI,eAAe,aAAa,OAAO;IACvC,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QAC9C,eAAe,wBAAwB,cAAc;IACvD;IAEA,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;QAC3C,OAAO,MAAM,GAAG,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,SAAU,CAAC;gBACtD,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ,OAAO,MAAM,IAAI;gBAC3B;gBACA,iBAAiB,gBAAgB,OAAO,aAAa,KAAK;gBAC1D,cAAc,OAAO,UAAU,IAAI;gBACnC,gBAAgB;oBACd,SAAS;oBACT,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK;gBAC5D;YACF,CAAC;IACH;IAEA,OAAO;AACT;AAMO,MAAM,+BAA+B,CAAC,EAC3C,IAAI,EACJ,WAAW,EACX,GAAG,EACH,WAAW,EAAE,EACb,cAAc,CAAC,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,KAAK;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,cAAc,IAAI,GAAG,KAAK,QAAQ,EAAE,aAAa,GAAG;IAEtE,uCAAuC;IACvC,MAAM,mBAAmB,eACvB,CAAC,6EAA6E,EAAE,UAAU,sDAAsD,CAAC;IAEnJ,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;IACT;IAEA,sCAAsC;IACtC,IAAI,oBAAoB;IAExB,oDAAoD;IACpD,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;QACxD,oBAAoB,yBAAyB;IAC/C;IAEA,iDAAiD;IACjD,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;QACrD,OAAO,UAAU,GAAG;YAClB,SAAS;YACT,iBAAiB,kBAAkB,MAAM;YACzC,mBAAmB,kBAAkB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBACzE,SAAS;oBACT,YAAY,QAAQ;oBACpB,QAAQ;wBACN,SAAS,AAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,cAC5C,QAAQ,IAAI,KAAK,SAAS,gBAAgB,YAAa;wBACjE,OAAO,CAAC,2BAA2B,EAAE,QAAQ,IAAI,IAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,IAAI,WAAW;wBAC1F,QAAQ,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE,QAAQ,GAAG;wBACvD,eAAe,QAAQ,OAAO,IAAI,4BAA4B;4BAC5D,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI;wBACpB,GAAG,SAAS,CAAC,GAAG,OAAO;wBACvB,iBAAiB,gBAAgB,QAAQ,UAAU,KAAK,gBAAgB,IAAI;wBAC5E,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;oBACF;gBACF,CAAC;QACH;IACF;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC,EAC3C,QAAQ,EAAE,EACX;IACC,6CAA6C;IAC7C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;AACF;AAYO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,+EAA+E;IAC/E,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,mBAAmB,CAAC,IAAI,GAAG,MAAM,IAAI,KAAK;QACnF,OAAO,QAAQ,mBAAmB,CAAC,IAAI;IACzC;IAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,UAAU,QAAQ,OAAO,CAAC,IAAI;QAEpC,8CAA8C;QAC9C,IAAI,QAAQ,MAAM,IAAI,OAAO,QAAQ,MAAM,IAAI,KAAK;YAClD,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,QAAQ,MAAM,GAAG,KAAK;YACxB,MAAM,YAAY,CAAC,6QAA6Q,CAAC;YACjS,MAAM,kBAAkB,UAAU;YAClC,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;QAC/F;QAEA,sCAAsC;QACtC,OAAO,QAAQ,SAAS,CAAC,GAAG,OAAO;IACrC;IAEA,6CAA6C;IAC7C,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxD,MAAM,eAAe,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,mBAAmB;QACxF,IAAI,aAAa,MAAM,IAAI,KAAK;YAC9B,OAAO,aAAa,SAAS,CAAC,GAAG,OAAO;QAC1C;IACF;IAEA,sCAAsC;IACtC,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,cAAc,GAAG,QAAQ,KAAK,CAAC,+QAA+Q,CAAC;QAErT,IAAI,YAAY,MAAM,IAAI,KAAK;YAC7B,OAAO,YAAY,MAAM,IAAI,MAAM,cAAc,YAAY,SAAS,CAAC,GAAG,OAAO;QACnF;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,cAAc,CAAC,2KAA2K,CAAC;QACnN,OAAO,gBAAgB,MAAM,IAAI,MAAM,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,OAAO;IAC/F;IAEA,oBAAoB;IACpB,OAAO;AACT;AAQO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,SAAS,OAAO;IAErB,sDAAsD;IACtD,IAAI,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACxE,MAAM,WAAW,QAAQ,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAC9F,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,GAAG;YAChD,OAAO,QAAQ,eAAe,CAAC,IAAI;QACrC;IACF;IAEA,sDAAsD;IACtD,MAAM,eAAe,QAAQ,IAAI,KAAK,cAClC;QAAC;QAAqB;QAAsB;QAAuB;QAAqB;KAAiB,GACzG;QAAC;QAAW;QAAW;QAAc;QAAmB;KAAmB;IAE/E,qCAAqC;IACrC,MAAM,gBAAgB,EAAE;IACxB,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,QAAQ,QAAQ,KAAK,CAAC,WAAW;QACvC,MAAM,eAAe;YAAC;YAAS;YAAS;YAAU;YAAW;YAAW;YAAO;YAAQ;YAAa;YAAY;SAAY;QAC5H,MAAM,gBAAgB;YAAC;YAAY;YAAY;YAAa;YAAU;YAAY;YAAU;YAAa;SAAS;QAElH,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;QAEA,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,MAAM,QAAQ,CAAC,OAAO,cAAc,IAAI,CAAC;QAC/C;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc;WAAI;WAAiB;KAAc;IACvD,MAAM,iBAAiB;WAAI,IAAI,IAAI;KAAa;IAEhD,8BAA8B;IAC9B,IAAI,eAAe,MAAM,IAAI,GAAG;QAC9B,OAAO,eAAe,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IACzC,OAAO,IAAI,eAAe,MAAM,IAAI,GAAG;QACrC,OAAO,eAAe,IAAI,CAAC;IAC7B,OAAO;QACL,qDAAqD;QACrD,MAAM,qBAAqB;YAAC;YAAqB;YAAmB;SAAsB;QAC1F,MAAM,gBAAgB;eAAI;eAAmB;SAAmB,CAAC,KAAK,CAAC,GAAG;QAC1E,OAAO,cAAc,IAAI,CAAC;IAC5B;AACF;AAQO,MAAM,8BAA8B,CAAC;IAC1C,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,eAAe;QAAE,GAAG,OAAO;IAAC;IAElC,uBAAuB;IACvB,IAAI,CAAC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC7E,aAAa,WAAW,GAAG,aAAa,IAAI,GACxC,GAAG,aAAa,IAAI,CAAC,6LAA6L,CAAC,GACnN;IACN;IAEA,+BAA+B;IAC/B,IAAI,CAAC,aAAa,KAAK,IAAI,aAAa,MAAM,EAAE,MAAM;QACpD,aAAa,KAAK,GAAG,aAAa,MAAM,CAAC,IAAI;IAC/C,OAAO,IAAI,CAAC,aAAa,KAAK,EAAE;QAC9B,aAAa,KAAK,GAAG;IACvB;IAEA,8BAA8B;IAC9B,IAAI,CAAC,aAAa,MAAM,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,EAAE;QACrD,aAAa,MAAM,GAAG;YACpB,MAAM,aAAa,KAAK,IAAI;YAC5B,KAAK;QACP;IACF;IAEA,4BAA4B;IAC5B,IAAI,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,eAAe,CAAC,WAAW,EAAE;QAC9E,aAAa,eAAe,GAAG;YAC7B,aAAa;YACb,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,IAAI,CAAC,aAAa,YAAY,EAAE;QAC9B,aAAa,YAAY,GAAG;IAC9B;IAEA,oBAAoB;IACpB,IAAI,CAAC,aAAa,QAAQ,EAAE;QAC1B,aAAa,QAAQ,GAAG;IAC1B;IAEA,OAAO;AACT;AASO,MAAM,0BAA0B,CAAC,SAAS,QAAQ,CAAC;IACxD,IAAI,CAAC,SAAS,OAAO,EAAE;IAEvB,MAAM,kBAAkB;QACtB;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;QACA;YACE,QAAQ;YACR,eAAe;YACf,YAAY;YACZ,QAAQ;QACV;KACD;IAED,OAAO,gBAAgB,KAAK,CAAC,GAAG;AAClC;AASO,MAAM,2BAA2B,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE;IAClE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,YAAY;QACd;KACD;IAED,8DAA8D;IAC9D,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,mBAAmB,EAAE;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,eAAe,CAAC,aAAa,CAAC,IAAI,aAAa,MAAM;QAC3D,MAAM,cAAc,YAAY,CAAC,aAAa;QAE9C,uEAAuE;QACvE,MAAM,eAAe,cAAc,IAAI,CAAC,QAAQ,EAAE,YAAY,SAAS,CAAC,GAAG;QAE3E,iBAAiB,IAAI,CAAC;YACpB,GAAG,WAAW;YACd,OAAO,YAAY,KAAK,GAAG;YAC3B,MAAM,YAAY,IAAI,GAAG,CAAC,cAAc,IAAI,CAAC,MAAM,EAAE,aAAa,GAAG,EAAE;QACzE;IACF;IAEA,OAAO;AACT;AAUO,MAAM,yBAAyB,CAAC,aAAa,EAAE,EAAE,gBAAgB,CAAC,EAAE,aAAa,CAAC;IACvF,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO,EAAE;IACX;IAEA,8DAA8D;IAC9D,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,kCAAkC;IAClC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI,QAAQ;IAGjD,2DAA2D;IAC3D,IAAI,cAAc,MAAM,IAAI,YAAY;QACtC,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;IAC9C;IAEA,uDAAuD;IACvD,MAAM,gBAAgB;QAAC;QAAc,eAAe;QAAG,eAAe;KAAE,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,KAAK,KAAK;IACpG,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,cAAc,QAAQ,CAAC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI;IAGhE,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;AAC9C;AAQO,MAAM,eAAe,CAAC;IAC3B,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AASO,MAAM,8BAA8B,CAAC,eAAe,cAAc,EAAE,cAAc,IAAI;IAC3F,MAAM,cAAc;QAClB;YACE,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,KAAK;QACP;KACD;IAED,0CAA0C;IAC1C,IAAI,eAAe,cAAc,GAAG;QAClC,YAAY,IAAI,CAAC;YACf,MAAM,CAAC,KAAK,EAAE,aAAa;YAC3B,KAAK,CAAC,yCAAyC,EAAE,aAAa;QAChE;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetaHead.js"], "sourcesContent": ["\r\nimport JsonLdSchema from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport default function MetaHead({ props, children, schemas }) {\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n  const defaultMeta = {\r\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\r\n    canonical: \"https://www.tradereply.com/\",\r\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    ogSiteName: \"TradeReply\",\r\n    ogImage: \"https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg\",\r\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\r\n  };\r\n  const isNoIndex = props?.noindex === true;\r\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\r\n\r\n  return (\r\n    <>\r\n\r\n      {children}\r\n      <title>{props?.title || defaultMeta.title}</title>\r\n\r\n      {/* Always render robots tag */}\r\n      {/* <meta\r\n        name=\"robots\"\r\n        content={isNoIndex ? \"noindex, follow\" : \"index, follow\"}\r\n      /> */}\r\n            <meta name=\"robots\" content={robotsContent} />\r\n      {props?.canonical_link?.trim() && props?.noindex !== true && (\r\n        <link rel=\"canonical\" href={props.canonical_link} />\r\n      )}\r\n\r\n      <meta\r\n        name=\"description\"\r\n        content={props?.description || defaultMeta.description}\r\n      />\r\n      \r\n      {props?.rel_next && (\r\n        <link rel=\"next\" href={props?.rel_next} />\r\n      )}  \r\n\r\n      <meta property=\"og:title\" content={props?.og_title || defaultMeta?.ogTitle} />\r\n      <meta property=\"og:description\" content={props?.og_description || defaultMeta?.ogDescription} />\r\n      <meta property=\"og:site_name\" content={props?.og_site_name || defaultMeta?.ogSiteName} />\r\n\r\n      <meta property=\"og:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta property=\"og:type\" content=\"website\" />\r\n\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      <meta property=\"og:locale\" content=\"en_US\" />\r\n\r\n      {/*/!* Twitter Meta Tags *!/*/}\r\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n      <meta name=\"twitter:title\" content={props?.twitter_title || defaultMeta?.twitterTitle} />\r\n      <meta name=\"twitter:description\" content={props?.twitter_description || defaultMeta?.twitterDescription} />\r\n      <meta name=\"twitter:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta name=\"twitter:site\" content=\"@JoinTradeReply\" />\r\n\r\n      {/* Favicon */}\r\n      <link rel=\"icon\" type=\"image/x-icon\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`} />\r\n      <link rel=\"icon\" type=\"image/svg+xml\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`} />\r\n      \r\n      {schemas && (\r\n        <JsonLdSchema schemas={schemas} />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC3D,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IACA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,qBACE;;YAEG;0BACD,8OAAC;0BAAO,OAAO,SAAS,YAAY,KAAK;;;;;;0BAOnC,8OAAC;gBAAK,MAAK;gBAAS,SAAS;;;;;;YAClC,OAAO,gBAAgB,UAAU,OAAO,YAAY,sBACnD,8OAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,cAAc;;;;;;0BAGlD,8OAAC;gBACC,MAAK;gBACL,SAAS,OAAO,eAAe,YAAY,WAAW;;;;;;YAGvD,OAAO,0BACN,8OAAC;gBAAK,KAAI;gBAAO,MAAM,OAAO;;;;;;0BAGhC,8OAAC;gBAAK,UAAS;gBAAW,SAAS,OAAO,YAAY,aAAa;;;;;;0BACnE,8OAAC;gBAAK,UAAS;gBAAiB,SAAS,OAAO,kBAAkB,aAAa;;;;;;0BAC/E,8OAAC;gBAAK,UAAS;gBAAe,SAAS,OAAO,gBAAgB,aAAa;;;;;;0BAE3E,8OAAC;gBAAK,UAAS;gBAAW,SAAQ;;;;;;0BAClC,8OAAC;gBAAK,UAAS;gBAAU,SAAQ;;;;;;0BAEjC,8OAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,8OAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BACzC,8OAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BAGnC,8OAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAClC,8OAAC;gBAAK,MAAK;gBAAgB,SAAS,OAAO,iBAAiB,aAAa;;;;;;0BACzE,8OAAC;gBAAK,MAAK;gBAAsB,SAAS,OAAO,uBAAuB,aAAa;;;;;;0BACrF,8OAAC;gBAAK,MAAK;gBAAgB,SAAQ;;;;;;0BACnC,8OAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAGlC,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAe,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;0BACzH,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAgB,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;YAEzH,yBACC,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/ClientSideCanonicalTag.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/ClientSideCanonicalTag.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/ClientSideCanonicalTag.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/ClientSideCanonicalTag.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/ClientSideCanonicalTag.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/ClientSideCanonicalTag.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/auth/AuthHeartbeatProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/auth/AuthHeartbeatProvider.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/auth/AuthHeartbeatProvider.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/auth/AuthHeartbeatProvider.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/auth/AuthHeartbeatProvider.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/auth/AuthHeartbeatProvider.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/layout.js"], "sourcesContent": ["import { Suspense } from \"react\";\r\nimport InactivityHandler from \"../utils/inactivityHandler\";\r\nimport Script from \"next/script\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport \"./globals.scss\";\r\nimport \"../../src/css/app.scss\";\r\nimport Providers from \"@/Components/providers/Providers\";\r\nimport MetaProvider from \"@/Components/providers/MetaProvider\";\r\nimport I18nProvider from \"@/providers/I18nProvider\";\r\nimport { Toaster } from \"react-hot-toast\";\r\nimport \"@/lib/useTranslation\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport ClientSideCanonicalTag from \"@/Components/ClientSideCanonicalTag\";\r\nimport AuthHeartbeatProvider from \"@/Components/auth/AuthHeartbeatProvider\";\r\nimport {\r\n  generateOrganizationSchema,\r\n  generateWebsiteSchema,\r\n} from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nconst defaultSchemas = [generateOrganizationSchema(), generateWebsiteSchema()];\r\nconst defaultMetaProps = {\r\n  title: \"TradeReply: Advanced Trading Tools & Strategy Optimization\",\r\n  description:\r\n    \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.\",\r\n  canonical_link: \"https://www.tradereply.com/\",\r\n  og_title: \"TradeReply: Advanced Trading Tools & Strategy Optimization\",\r\n  og_description:\r\n    \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.\",\r\n  og_site_name: \"TradeReply\",\r\n  twitter_title: \"TradeReply: Advanced Trading Tools & Strategy Optimization\",\r\n  twitter_description:\r\n    \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.\",\r\n};\r\n\r\nexport default function RootLayout({ children, metaProps, schemas }) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <Script\r\n          src=\"https://cmp.osano.com/fT1j7UREwV/19bf3e9d-4e98-4a0a-86cc-abeacfca9b84/osano.js\"\r\n          strategy=\"beforeInteractive\"\r\n        />\r\n        <Suspense fallback={<></>}>\r\n          <ClientSideCanonicalTag />\r\n        </Suspense>\r\n        <MetaHead\r\n          props={metaProps ?? defaultMetaProps}\r\n          schemas={schemas ?? defaultSchemas}\r\n        />\r\n      </head>\r\n      <body\r\n      // className={gilroy.variable}\r\n      >\r\n        <Providers>\r\n          <MetaProvider>\r\n            <I18nProvider>\r\n              <LanguageProvider>\r\n                <InactivityHandler />\r\n                <Toaster\r\n                  position=\"top-right\"\r\n                  reverseOrder={false}\r\n                  toastOptions={{\r\n                    style: {\r\n                      zIndex: 99999,\r\n                    },\r\n                  }}\r\n                />\r\n                <AuthHeartbeatProvider>{children}</AuthHeartbeatProvider>\r\n              </LanguageProvider>\r\n            </I18nProvider>\r\n          </MetaProvider>\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAKA,MAAM,iBAAiB;IAAC,CAAA,GAAA,oIAAA,CAAA,6BAA0B,AAAD;IAAK,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD;CAAI;AAC9E,MAAM,mBAAmB;IACvB,OAAO;IACP,aACE;IACF,gBAAgB;IAChB,UAAU;IACV,gBACE;IACF,cAAc;IACd,eAAe;IACf,qBACE;AACJ;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;IACjE,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC,8HAAA,CAAA,UAAM;wBACL,KAAI;wBACJ,UAAS;;;;;;kCAEX,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,wBAAU;kCAClB,cAAA,8OAAC,2IAAA,CAAA,UAAsB;;;;;;;;;;kCAEzB,8OAAC,8HAAA,CAAA,UAAQ;wBACP,OAAO,aAAa;wBACpB,SAAS,WAAW;;;;;;;;;;;;0BAGxB,8OAAC;0BAGC,cAAA,8OAAC,2IAAA,CAAA,UAAS;8BACR,cAAA,8OAAC,8IAAA,CAAA,UAAY;kCACX,cAAA,8OAAC,gIAAA,CAAA,UAAY;sCACX,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;kDACf,8OAAC,iIAAA,CAAA,UAAiB;;;;;kDAClB,8OAAC,uJAAA,CAAA,UAAO;wCACN,UAAS;wCACT,cAAc;wCACd,cAAc;4CACZ,OAAO;gDACL,QAAQ;4CACV;wCACF;;;;;;kDAEF,8OAAC,kJAAA,CAAA,UAAqB;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  let { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  // if a nonce is explicitly passed to the script tag, favor that over the automatic handling\n  nonce = restProps.nonce || nonce\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n          nonce,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript({\n        ...props,\n        nonce,\n      })\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/next/script.js"], "sourcesContent": ["module.exports = require('./dist/client/script')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CheckmarkIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckmarkIcon() from the server but CheckmarkIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"CheckmarkIcon\",\n);\nexport const ErrorIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorIcon() from the server but ErrorIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ErrorIcon\",\n);\nexport const LoaderIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoaderIcon() from the server but LoaderIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"LoaderIcon\",\n);\nexport const ToastBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastBar() from the server but ToastBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ToastBar\",\n);\nexport const ToastIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastIcon() from the server but ToastIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ToastIcon\",\n);\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"default\",\n);\nexport const resolveValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveValue() from the server but resolveValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"resolveValue\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useToaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToaster() from the server but useToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"useToaster\",\n);\nexport const useToasterStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToasterStore() from the server but useToasterStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"useToasterStore\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6EACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CheckmarkIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckmarkIcon() from the server but CheckmarkIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"CheckmarkIcon\",\n);\nexport const ErrorIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorIcon() from the server but ErrorIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ErrorIcon\",\n);\nexport const LoaderIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoaderIcon() from the server but LoaderIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"LoaderIcon\",\n);\nexport const ToastBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastBar() from the server but ToastBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ToastBar\",\n);\nexport const ToastIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastIcon() from the server but ToastIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ToastIcon\",\n);\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"Toaster\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/react-hot-toast/dist/index.mjs from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"default\",\n);\nexport const resolveValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveValue() from the server but resolveValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"resolveValue\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"toast\",\n);\nexport const useToaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToaster() from the server but useToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"useToaster\",\n);\nexport const useToasterStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToasterStore() from the server but useToasterStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"useToasterStore\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yDACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/core/types.ts", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/core/utils.ts", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/core/store.ts", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/core/toast.ts", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/core/use-toaster.ts", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/toast-bar.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/toast-icon.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/error.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/loader.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/checkmark.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/components/toaster.tsx", "file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/node_modules/react-hot-toast/src/index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "names": ["isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "useEffect", "useState", "useRef", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "useEffect", "useCallback", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "useEffect", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "useCallback", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "styled", "keyframes", "React", "styled", "keyframes", "styled", "keyframes", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "styled", "keyframes", "rotate", "LoaderIcon", "p", "styled", "keyframes", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "styled", "IndicatorWrapper", "enter", "keyframes", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "styled", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "keyframes", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "css", "setup", "React", "setup", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"], "mappings": "", "debugId": null}}]}