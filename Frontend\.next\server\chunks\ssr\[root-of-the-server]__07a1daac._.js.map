{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/pricing/PricingClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/pricing/PricingClient.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/pricing/PricingClient.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/pricing/PricingClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/pricing/PricingClient.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/pricing/PricingClient.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/pricing/page.js"], "sourcesContent": ["import { cookies } from \"next/headers\";\r\nimport { Suspense } from \"react\";\r\nimport PricingClient from \"./PricingClient\";\r\n\r\nexport default async function Page() {\r\n    const cookieStore = await cookies();\r\n    const loginToken = cookieStore.get(\"authToken\") || null;\r\n\r\n    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/stripe/plans`, {\r\n        cache: \"no-store\",\r\n    });\r\n\r\n    const { data: plans } = await res.json();\r\n    const defaultIsMonthly = false;\r\n\r\n    let activeSubscription = null;\r\n    let nextPlan = null;\r\n    let previousPlan = null;\r\n\r\n    if (loginToken) {\r\n        try {\r\n            const subRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/active/subscription`, {\r\n                headers: {\r\n                    Authorization: `Bearer ${loginToken.value}`,\r\n                    Accept: \"application/json\",\r\n                },\r\n                cache: \"no-store\",\r\n            });\r\n            const subJson = await subRes.json();\r\n            activeSubscription = subJson?.data || null;\r\n            nextPlan = subJson?.next_plan || null;\r\n            previousPlan = subJson?.previous_plan || null;\r\n        } catch (err) {\r\n            console.error(\"Failed to fetch active subscription:\", err);\r\n        }\r\n    }\r\n    const filteredPlans = plans.filter(plan => {\r\n        const isCorrectBilling = plan.billing_type === (defaultIsMonthly ? \"monthly\" : \"yearly\");\r\n        const isFreePlan = plan.billing_type === \"free\";\r\n\r\n        if (loginToken) {\r\n            return isFreePlan || isCorrectBilling;\r\n        } else {\r\n            return isCorrectBilling;\r\n        }\r\n    });\r\n    return (\r\n        <Suspense fallback={<div>Loading...</div>}>\r\n            <PricingClient\r\n                plans={plans}\r\n                billingPlans={filteredPlans}\r\n                defaultIsMonthly={defaultIsMonthly}\r\n                activeSubscription={activeSubscription}\r\n                authToken={loginToken}\r\n                nextPlan={nextPlan}\r\n                previousPlan={previousPlan}\r\n            />\r\n        </Suspense>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC1B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,aAAa,YAAY,GAAG,CAAC,gBAAgB;IAEnD,MAAM,MAAM,MAAM,MAAM,6DAAwC,oBAAoB,CAAC,EAAE;QACnF,OAAO;IACX;IAEA,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,IAAI,IAAI;IACtC,MAAM,mBAAmB;IAEzB,IAAI,qBAAqB;IACzB,IAAI,WAAW;IACf,IAAI,eAAe;IAEnB,IAAI,YAAY;QACZ,IAAI;YACA,MAAM,SAAS,MAAM,MAAM,6DAAwC,2BAA2B,CAAC,EAAE;gBAC7F,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,WAAW,KAAK,EAAE;oBAC3C,QAAQ;gBACZ;gBACA,OAAO;YACX;YACA,MAAM,UAAU,MAAM,OAAO,IAAI;YACjC,qBAAqB,SAAS,QAAQ;YACtC,WAAW,SAAS,aAAa;YACjC,eAAe,SAAS,iBAAiB;QAC7C,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,wCAAwC;QAC1D;IACJ;IACA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QAC/B,MAAM,mBAAmB,KAAK,YAAY,KAAK,CAAC,sCAAmB,0BAAY,QAAQ;QACvF,MAAM,aAAa,KAAK,YAAY,KAAK;QAEzC,IAAI,YAAY;YACZ,OAAO,cAAc;QACzB,OAAO;YACH,OAAO;QACX;IACJ;IACA,qBACI,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;sBAAI;;;;;;kBACrB,cAAA,8OAAC,kJAAA,CAAA,UAAa;YACV,OAAO;YACP,cAAc;YACd,kBAAkB;YAClB,oBAAoB;YACpB,WAAW;YACX,UAAU;YACV,cAAc;;;;;;;;;;;AAI9B", "debugId": null}}]}