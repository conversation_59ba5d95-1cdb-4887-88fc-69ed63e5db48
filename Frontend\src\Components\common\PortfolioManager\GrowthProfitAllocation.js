import React, { useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CommonTooltip from '@/Components/UI/CommonTooltip';

const GrowthInputRow = ({
  label,
  summary,
  value,
  placeholder,
  onChange,
  data_type,
  hasFormula
}) => {
  const [inputValue, setInputValue] = useState(value ?? '');
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    setInputValue(value ?? '');
  }, [value]);

  const formatDisplay = (val) => {
    if (val === '' || val === null || isNaN(val)) return val;

    if (data_type === 'Currency') return `$${val}`;
    if (data_type === 'Number (Percentage)') {
      return `${parseFloat(val).toFixed(2)}%`;
    }

    if (data_type?.toLowerCase().includes('ratio')) {
      return `${parseFloat(val).toFixed(2)}X`;
    }

    return val;
  };


  const parseValue = (val) => {
    if (data_type === 'Currency') return val.replace(/[^0-9.]/g, '');
    if (data_type === 'Number (Percentage)') {
      const num = parseFloat(val.replace(/[^0-9.]/g, ''));
      return isNaN(num) ? '' : `${num.toFixed(2)}`;
    }
    if (data_type?.toLowerCase().includes('ratio')) return val.replace(/[^0-9.]/g, '');
    return val;
  };

  const handleChange = (val) => {
    let cleaned = val.replace(/[^0-9.]/g, '');

    const dotCount = (cleaned.match(/\./g) || []).length;
    if (dotCount > 1) return;

    const [intPart, decimalPart = ''] = cleaned.split('.');
    let limitedDecimal = decimalPart;

    if (data_type === 'Currency') {
      limitedDecimal = decimalPart.slice(0, 8);
    } else if (data_type?.toLowerCase().includes('percentage')) {
      limitedDecimal = decimalPart.slice(0, 6);
    }

    let limited = intPart;
    if (cleaned.includes('.')) {
      limited += '.' + limitedDecimal;
    }

    setInputValue(limited);

    if (!hasFormula && /^[0-9]+(\.[0-9]+)?$/.test(limited)) {
      onChange(parseValue(limited));
    }
  };

  return (
    <Row className="mt-2 gx-2">
      <Col md={9} xs={7}>
        <div className="blueCard">
          <p>{label}</p>
          <CommonTooltip
            className="subTooltip"
            content={
              <>
                <p>{summary}</p>
              </>
            }
            position="top-right"
          >
            <SolidInfoIcon />
          </CommonTooltip>
        </div>
      </Col>
      <Col md={3} xs={5}>
        <div className={hasFormula ? "DisabledCard" : "whiteCard"} >
          <input
            type="text"
            title={formatDisplay(inputValue)}
            value={
              (data_type?.toLowerCase().includes('percentage') || data_type?.toLowerCase().includes('ratio'))
                ? (isFocused ? inputValue : formatDisplay(inputValue))
                : formatDisplay(inputValue)
            }
            placeholder={placeholder}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onChange={(e) => handleChange(e.target.value)}
            disabled={hasFormula}
          />
        </div>

      </Col>
    </Row>
  );
};

export default function GrowthProfitAllocation({ data = [], accountSizeField, onChangeField }) {
  const growthGoalField = data.find(f => f.database_field?.toLowerCase() === 'portfolio_account_growth_goal');

  const goalValueIsEmpty = !growthGoalField?.value || growthGoalField.value === '0';
  const accountSizeIsEmpty = !accountSizeField?.value || accountSizeField.value === '0';

  const processedData = data.map((field) => {
    const dbField = field.database_field?.toLowerCase();

    if (dbField === 'portfolio_account_growth_value_of_goal') {
      if (goalValueIsEmpty) {
        return {
          ...field,
          value: 'No Goal Set',
          hasFormula: true
        };
      } else if (accountSizeIsEmpty) {
        return {
          ...field,
          value: 'No Funds',
          hasFormula: true
        };
      }
    }

    if (dbField === 'portfolio_account_growth_goal_factor') {
      if (accountSizeIsEmpty) {
        return {
          ...field,
          value: 'No Funds',
          hasFormula: true
        };
      }
    }

    return field;
  });

  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Growth & Profit Allocation</p>
      </div>

      {processedData.map((field, index) => (
        <GrowthInputRow
          key={index}
          label={field.label ?? field.database_field}
          summary={field?.summary}
          value={field.value ?? ''}
          placeholder={field.account_field_placeholder}
          data_type={field.data_type}
          hasFormula={field.hasFormula}
          onChange={(newValue) => onChangeField(field.id, newValue)}
        />
      ))}
    </div>
  );
}

