/* [project]/src/app/globals.scss.css [app-client] (css) */
body {
  font-size: 1rem;
  font-family: var(--font-g<PERSON>roy);
  background-color: #011132;
  overflow-x: clip;
}

*, :after, :before {
  box-sizing: border-box;
}

div.__toast, .react-hot-toast, [data-sonner-toast], [data-toast] {
  z-index: 10000 !important;
}

hr {
  color: #fff;
}

img {
  max-width: 100%;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.text-link {
  color: #00adef;
}

.text-link:hover {
  opacity: .6;
}

a, span {
  display: inline-block;
}

svg path {
  transition: all .3s ease-in-out;
}

ol {
  margin: 0;
  padding: 0;
}

small {
  font-family: <PERSON><PERSON>, "sans-serif";
}

body {
  overflow: hidden;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6, p {
  color: #fff;
  margin-bottom: 0;
}

@media (max-width: 1199px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  h1, .h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {
  h1, .h1 {
    font-size: 1.3rem;
  }
}

@media (max-width: 1269px) {
  h2, .h2 {
    font-size: 3rem;
  }
}

@media (max-width: 767px) {
  h2, .h2 {
    font-size: 1.363rem;
  }
}

@media (max-width: 1199px) {
  h3, .h3 {
    font-size: 1.688rem;
  }
}

@media (max-width: 767px) {
  h3, .h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 767px) {
  h4, .h4 {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

@media (max-width: 767px) {
  h5, .h5 {
    font-size: 1rem;
    line-height: 25px;
  }
}

@media (max-width: 767px) {
  h6, .h6 {
    font-size: 1rem;
  }
}

@media (max-width: 767px) {
  p {
    font-size: .875rem;
  }
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

.font-weight-800 {
  font-weight: 800;
}

.divider {
  opacity: 1;
  background-color: #666;
  width: 100%;
  height: 1px;
  margin: 1.25rem 0;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.py-40 {
  padding: 40px 0;
}

.py-80 {
  padding: 80px 0;
}

@media (max-width: 767px) {
  .py-80 {
    padding: 40px 0;
  }
}

.py-100 {
  padding: 100px 0;
}

@media (max-width: 1199px) {
  .py-100 {
    padding: 70px 0 !important;
  }
}

@media (max-width: 767px) {
  .py-100 {
    padding: 50px 0 !important;
  }
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

@media (max-width: 767px) {
  .mt-40 {
    margin-top: 30px !important;
  }
}

.mt-50 {
  margin-top: 50px !important;
}

@media (max-width: 767px) {
  .mt-50 {
    margin-top: 30px !important;
  }
}

.my-10 {
  margin: 10px 0 !important;
}

.my-20 {
  margin: 20px 0 !important;
}

.my-30 {
  margin: 30px 0 !important;
}

.my-40 {
  margin: 40px 0 !important;
}

@media (max-width: 767px) {
  .my-40 {
    margin: 30px 0 !important;
  }
}

.my-50 {
  margin: 50px 0 !important;
}

@media (max-width: 767px) {
  .my-50 {
    margin: 30px 0 !important;
  }
}

figure {
  margin-bottom: 0;
}

.black_text {
  color: #000;
}

.white_text {
  color: #fff;
}

.red_text {
  color: #ff0302 !important;
}

.red_text svg path {
  fill: #ff0302 !important;
}

.green_text {
  color: #32cd33 !important;
}

.green_text svg path {
  fill: #32cd33 !important;
}

.gray_text {
  color: #9c9a9f !important;
}

.gray_text svg path {
  fill: #9c9a9f !important;
}

.white_icon svg path {
  fill: #fff !important;
}

.yellowlight_text {
  color: #feff14 !important;
}

.greenlight_text {
  color: #7aff67 !important;
}

.redlight_text {
  color: #d54d3f !important;
}

.darkblue_text {
  color: #04498c !important;
}

.blue_text {
  color: #00adef !important;
}

.grey_text {
  color: #c5c5d5 !important;
}

.lightgrey_text {
  color: #c5c5c5 !important;
}

.darkgrey_text {
  color: gray !important;
}

.yellow_text {
  color: #fea500 !important;
}

.yellow_text svg path {
  fill: #fea500 !important;
}

.green_bg {
  background-color: #7af870 !important;
}

.red_bg {
  background-color: #ff696a !important;
}

.blue_bg {
  background-color: #031940 !important;
}

.baseblue_bg {
  background-color: #3791d3 !important;
  border-radius: 1.25rem !important;
}

.baseblue_bg .solidArrow svg path {
  fill: #00adef !important;
}

.baseblue_bg:hover {
  background: linear-gradient(0deg, rgba(255, 255, 255, .157), rgba(255, 255, 255, .157)), linear-gradient(270.33deg, rgba(0, 173, 239, .4), rgba(0, 173, 239, .15) 45.5%, rgba(0, 173, 239, .4) 98%) !important;
}

.bluelight_bg {
  color: #fff !important;
  background-color: #04498c !important;
}

.greenlight_bg {
  background-color: #7af870 !important;
}

.white_bg {
  background-color: #fff !important;
}

.whitelight_bg {
  background-color: rgba(255, 255, 255, .2) !important;
}

.Redgrandient {
  background: linear-gradient(0deg, rgba(255, 255, 255, .1), rgba(255, 255, 255, .1)), linear-gradient(270.33deg, rgba(255, 105, 106, .4) 0%, rgba(255, 105, 106, .2) 50%, rgba(255, 105, 106, .4) 100%) !important;
}

.Redgrandient svg path {
  fill: #ff696a !important;
}

.Redgrandient:hover {
  background: linear-gradient(0deg, rgba(255, 255, 255, .157), rgba(255, 255, 255, .157)), linear-gradient(270.33deg, rgba(255, 105, 106, .5), rgba(255, 105, 106, .25), rgba(255, 105, 106, .5)) !important;
}

.greengrandient {
  background: linear-gradient(0deg, rgba(255, 255, 255, .1), rgba(255, 255, 255, .1)), linear-gradient(270.33deg, rgba(50, 205, 51, .4) 0%, rgba(50, 205, 51, .15) 45.5%, rgba(50, 205, 51, .4) 98%) !important;
}

.greengrandient svg path {
  fill: #32cd33 !important;
}

.greengrandient:hover {
  background: linear-gradient(0deg, rgba(255, 255, 255, .157), rgba(255, 255, 255, .157)), linear-gradient(270.33deg, rgba(50, 205, 51, .5), rgba(50, 205, 51, .25) 45.5%, rgba(50, 205, 51, .5) 98%) !important;
}

.bluegrandient {
  background: linear-gradient(0deg, rgba(255, 255, 255, .1), rgba(255, 255, 255, .1)), linear-gradient(270.33deg, rgba(0, 173, 239, .4) 0%, rgba(0, 173, 239, .15) 45.5%, rgba(0, 173, 239, .4) 98%) !important;
  border-radius: 1.25rem !important;
}

.bluegrandient .solidArrow svg path {
  fill: #00adef !important;
}

.bluegrandient:hover {
  background: linear-gradient(0deg, rgba(255, 255, 255, .157), rgba(255, 255, 255, .157)), linear-gradient(270.33deg, rgba(0, 173, 239, .4), rgba(0, 173, 239, .15) 45.5%, rgba(0, 173, 239, .4) 98%) !important;
}

.greengrandientbg {
  background: linear-gradient(0deg, rgba(255, 255, 255, .1), rgba(255, 255, 255, .1)), linear-gradient(270.33deg, rgba(50, 205, 51, .4) 0%, rgba(50, 205, 51, .15) 45.5%, rgba(50, 205, 51, .4) 98%) !important;
  border: 0 !important;
  border-radius: 1.25rem !important;
}

.redgrandientbg {
  background: linear-gradient(0deg, rgba(255, 255, 255, .1), rgba(255, 255, 255, .1)), linear-gradient(270.33deg, rgba(255, 105, 106, .4) 0%, rgba(255, 105, 106, .2) 50%, rgba(255, 105, 106, .4) 100%) !important;
  border: 0 !important;
  border-radius: 1.25rem !important;
}

.bluedark_bg {
  background: #04498c !important;
}

.cardgrandient {
  background: radial-gradient(50% 50%, rgba(0, 185, 255, .5) 21.5%, rgba(0, 83, 153, .5) 100%), linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .2) 47.5%, rgba(255, 255, 255, 0) 100%);
}

.green_arrow svg path {
  fill: #32cd33 !important;
}

body ::-webkit-scrollbar {
  border-radius: 1rem;
  width: 5px;
  height: 4px;
}

body ::-webkit-scrollbar-track {
  box-shadow: none;
}

body ::-webkit-scrollbar-thumb {
  background-color: #00adef;
  border-radius: 1rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

@media (min-width: 1400px) {
  .container {
    max-width: 1300px;
  }
}

@media (max-width: 767px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
}

input:-webkit-autofill {
  -webkit-text-fill-color: #000;
  transition: background-color 5000s ease-in-out;
  caret-color: rgba(0, 0, 0, 0) !important;
}

input:-webkit-autofill:hover {
  -webkit-text-fill-color: #000;
  transition: background-color 5000s ease-in-out;
  caret-color: rgba(0, 0, 0, 0) !important;
}

input:-webkit-autofill:focus {
  -webkit-text-fill-color: #000;
  transition: background-color 5000s ease-in-out;
  caret-color: rgba(0, 0, 0, 0) !important;
}

input:-webkit-autofill:active {
  -webkit-text-fill-color: #000;
  transition: background-color 5000s ease-in-out;
  caret-color: rgba(0, 0, 0, 0) !important;
}

.commonCard {
  background-color: rgba(159, 159, 159, .1);
  border: 1px solid #00adef;
  border-radius: .625rem;
  padding: 2.5em;
}

@media (max-width: 1399px) {
  .commonCard {
    padding: 2em;
  }
}

@media (max-width: 1199px) {
  .commonCard {
    padding: 1.25em 1rem;
  }
}

.borderTabs {
  white-space: nowrap;
  border-bottom: 0;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: clip;
}

.borderTabs.nav {
  border-bottom: 1px solid #00adef;
}

.borderTabs.nav .nav-item .nav-link {
  color: #00adef;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-bottom: 3px solid rgba(0, 0, 0, 0);
  border-radius: 0;
  padding: 0 1.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  line-height: normal;
  transition: all .3s ease-in-out;
}

@media (max-width: 767px) {
  .borderTabs.nav .nav-item .nav-link {
    font-size: 1rem;
  }
}

.borderTabs.nav .nav-item .nav-link.active {
  color: #fff;
  border-bottom: 5px solid #00adef;
}

.radioBtn {
  flex-wrap: wrap;
  display: flex;
}

.radioBtn .checkbox_input .form-check {
  cursor: pointer;
  border: 1px solid #666;
  border-radius: 1rem;
  justify-content: center;
  align-items: center;
  min-height: 64px;
  padding: .5rem 1.25rem;
  transition: all .3s ease-in-out;
  display: flex;
}

@media (max-width: 1399px) {
  .radioBtn .checkbox_input .form-check {
    min-height: 50px;
  }
}

@media (max-width: 767px) {
  .radioBtn .checkbox_input .form-check {
    padding: .5rem 1rem;
  }
}

.radioBtn .checkbox_input .form-check .form-check-input {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0);
  margin: 0;
  width: 20px !important;
  height: 20px !important;
}

.radioBtn .checkbox_input .form-check .form-check-label {
  cursor: pointer;
  color: #9c9a9f;
  align-items: center;
  margin: 0;
  padding-left: 1.25rem;
  display: flex;
}

@media (max-width: 575px) {
  .radioBtn .checkbox_input .form-check .form-check-label {
    padding-left: .5rem;
  }
}

.radioBtn .checkbox_input .form-check .form-check-label .radioIcon {
  margin-right: .625rem;
}

.radioBtn .checkbox_input .form-check.active, .radioBtn .checkbox_input .form-check:hover {
  background-color: #160125;
  border-color: #00adef;
}

.radioBtn .checkbox_input .form-check.active .form-check-label, .radioBtn .checkbox_input .form-check:hover .form-check-label {
  color: #c5c5d5;
}

.big_tabs.nav .nav-item {
  display: flex;
}

.big_tabs.nav .nav-item .nav-link {
  text-align: center;
  letter-spacing: -1px;
  color: #fff;
  background-color: rgba(4, 73, 140, .2);
  border: 2px solid rgba(4, 73, 140, .2);
  border-radius: 30px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 1.5rem 1rem;
  font-size: 24px;
  font-weight: 700;
  display: flex;
}

@media (max-width: 767px) {
  .big_tabs.nav .nav-item .nav-link {
    padding: 1rem .5rem;
    font-size: 18px;
  }
}

.big_tabs.nav .nav-item .nav-link .tabs_icon {
  background-color: rgba(3, 25, 64, .3);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  margin-bottom: 14px;
  display: flex;
}

.big_tabs.nav .nav-item .nav-link.active, .big_tabs.nav .nav-item .nav-link:hover, .big_tabs.nav .nav-item .nav-link:focus {
  color: #fff;
  border: 2px solid rgba(0, 173, 239, .5);
  background: linear-gradient(#04498c 0%, #011426 100%) !important;
}

.slider-container .slick-slider .slick-arrow.slick-prev, .slider-container .slick-slider .slick-arrow.slick-next {
  z-index: 2;
  background-color: #00adef;
  border-radius: 10rem;
  width: 50px;
  height: 50px;
  position: absolute;
  bottom: 0;
}

@media (max-width: 991px) {
  .slider-container .slick-slider .slick-arrow.slick-prev, .slider-container .slick-slider .slick-arrow.slick-next {
    width: 32px;
    height: 32px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-prev {
  left: -25px;
}

@media (min-width: 576px) and (max-width: 767px) {
  .slider-container .slick-slider .slick-arrow.slick-prev {
    left: 5px;
  }
}

@media (max-width: 374px) {
  .slider-container .slick-slider .slick-arrow.slick-prev {
    left: 5px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-prev:before {
  content: "";
  opacity: 1;
  background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 8px;
  height: 15px;
  position: absolute;
  top: 50%;
  left: 48%;
  transform: translate(-50%, -50%)rotate(180deg);
}

.slider-container .slick-slider .slick-arrow.slick-next {
  right: -25px;
}

@media (min-width: 576px) and (max-width: 767px) {
  .slider-container .slick-slider .slick-arrow.slick-next {
    right: 5px;
  }
}

@media (max-width: 374px) {
  .slider-container .slick-slider .slick-arrow.slick-next {
    right: 5px;
  }
}

.slider-container .slick-slider .slick-arrow.slick-next:before {
  content: "";
  opacity: 1;
  background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
  background-repeat: no-repeat;
  background-size: 100%;
  width: 8px;
  height: 15px;
  position: absolute;
  top: 54%;
  left: 52%;
  transform: translate(-50%, -50%);
}

.common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  align-items: center;
  padding: .625rem 1.25rem;
  font-size: 1.25rem;
  display: flex;
}

@media (max-width: 991px) {
  .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.8rem;
  }
}

@media (min-width: 1200px) {
  .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #283f67;
  }
}

.common_dropdown.dropdown .dropdown-menu {
  background-color: #031940;
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: .625rem;
  min-width: 200px;
}

.common_dropdown.dropdown .dropdown-menu .dropdown-item {
  color: #fff;
  padding: .625rem 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.common_dropdown.dropdown .dropdown-menu .dropdown-item:hover {
  color: #fff;
  background-color: #283f67;
}

@media (max-width: 991px) {
  .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.home-page .common_dropdown.dropdown .dropdown-menu {
  background-color: #1e222d;
}

.home-page .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover {
  color: #fff;
  background-color: #2a2e39;
}

.form-control {
  min-height: 56px;
  box-shadow: none;
  color: #fff;
  background-color: rgba(255, 255, 255, .3);
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: 1rem;
  outline: none;
  width: 100%;
  padding: .5rem 1.25rem;
  font-size: 1rem;
}

@media (max-width: 1599px) {
  .form-control {
    min-height: 52px;
    font-size: 1rem;
  }
}

.form-control.is-invalid, .form-control.was-validated, .form-control:invalid {
  background-image: none;
  border-color: #ff0302;
}

.form-control:hover {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-control::placeholder {
  color: #fff;
  opacity: .4;
}

.form-control:disabled {
  background-color: rgba(0, 0, 0, 0);
}

.form-control:focus {
  box-shadow: none;
  color: #fff;
  background-color: rgba(255, 255, 255, .3);
  border: 1px solid rgba(255, 255, 255, .3);
}

.form-control.passwordInput {
  padding-right: 4.375rem;
}

.login_fontStyle_forget {
  font-weight: 700;
}

.text_area_bg {
  background-color: rgba(255, 255, 255, .3);
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: 15px;
  width: 100%;
  height: 15rem;
}

.text-pre-line {
  white-space: pre-line;
}

.select-btn {
  background: #136fcb;
  width: 50%;
  margin: 10px 0;
  border-radius: 7px !important;
}

.green_btn {
  color: #fff;
  background-color: #32cd33 !important;
}

.green_btn:hover {
  background-color: #2bb72b !important;
}

.white_btn {
  color: #000 !important;
  background-color: #fff !important;
  border: 1px solid rgba(0, 0, 0, .2) !important;
}

.yellow_btn {
  color: #fff;
  background-color: #fea500 !important;
}

.password_check p {
  text-align: center;
  font-size: 14px;
}

.password_check .box1 p {
  color: #ff696a;
  font-weight: 600;
}

.password_check .box1_bg {
  background-color: #ff696a;
}

.password_check .box2_bg {
  background-color: #ff6a23;
}

.password_check .box3_bg {
  background-color: #ffa723;
}

.password_check .box4_bg {
  background-color: #fcd53f;
}

.password_check .box5_bg {
  background-color: #dff33b;
}

.password_check .white10_bg {
  background-color: rgba(255, 255, 255, .063);
}

.security_check .user_email {
  color: #32cd33;
  font-size: 18px;
  font-weight: 600;
}

.security_check_input input {
  text-align: center;
  background-color: rgba(255, 255, 255, .19);
  border-radius: 15px;
  width: 58px;
  height: 57px;
  font-size: 30px;
}

.security_check_input input:focus-visible {
  outline: 1px solid #fff !important;
}

.security_check_resend_btn {
  background-color: #031940;
  border-radius: 50px;
  padding: 10px 50px;
  font-size: 20px;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

.security_check_resend_btn:hover {
  color: #fff;
  background-color: #0099d1;
}

.security_check_resend_btn:disabled, .security_check_resend_btn:disabled:hover {
  cursor: not-allowed;
  background-color: #6c757d;
}

.rotate {
  animation: 1s linear spin;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.svg-baseblue {
  filter: invert(49%) sepia(63%) saturate(5181%) hue-rotate(171deg) brightness(96%) contrast(97%);
  width: 33px;
  height: 32px;
}

.baseblue_border {
  border-color: #00adef !important;
}

.darkblue_border {
  border-color: #04498c !important;
}

.darkgray_border {
  border-color: gray !important;
}

.portfolio-blur-overlay {
  z-index: 99999;
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
  background-color: rgba(255, 255, 255, .5);
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.portfolio-blur-overlay .loader-content {
  text-align: center;
  color: #1338ff;
  font-weight: bold;
}

.portfolio-blur-overlay .loader-content .spinner-border {
  width: 2.5rem;
  height: 2.5rem;
}

.loading-screen {
  justify-content: center;
  align-items: center;
  height: 100vh;
  display: flex;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Regular.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: Gilroy-Bold;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Bold.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Extrabold.woff2") format("woff2");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-LightItalic.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: Gilroy;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-BoldItalic.woff2") format("woff2");
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: Gilroy-Semibold;
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff") format("woff");
  src: url("https://cdn.tradereply.com/dev/site-assets/fonts/Gilroy-Semibold.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

:root {
  --font-gilroy: "Gilroy", sans-serif;
}

*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  line-height: 1.5;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="button"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="reset"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button {
  height: auto;
}

::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.end-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  right: 0;
}

.end-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  right: 0;
}

.end-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  right: 0;
}

.end-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.end-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.end-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.start-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  left: 0;
}

.start-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  left: 0;
}

.start-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  left: 0;
}

.start-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.start-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.start-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.top-1 {
  top: .25rem;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.m-2 {
  margin: .5rem;
}

.m-auto {
  margin: auto;
}

.mx-1 {
  margin-left: .25rem;
  margin-right: .25rem;
}

.mx-2 {
  margin-left: .5rem;
  margin-right: .5rem;
}

.my-1 {
  margin-top: .25rem;
  margin-bottom: .25rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.my-3 {
  margin-top: .75rem;
  margin-bottom: .75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.me-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: 0;
}

.me-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: 0;
}

.me-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: 0;
}

.me-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: 0;
}

.me-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: 0;
}

.me-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: 0;
}

.me-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .5rem;
}

.me-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .5rem;
}

.me-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .5rem;
}

.me-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .5rem;
}

.me-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .5rem;
}

.me-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .5rem;
}

.me-3:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .75rem;
}

.me-3:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .75rem;
}

.me-3:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: .75rem;
}

.me-3:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .75rem;
}

.me-3:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .75rem;
}

.me-3:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: .75rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ms-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .25rem;
}

.ms-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .25rem;
}

.ms-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .25rem;
}

.ms-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .25rem;
}

.ms-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .25rem;
}

.ms-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .25rem;
}

.ms-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .5rem;
}

.ms-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .5rem;
}

.ms-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .5rem;
}

.ms-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .5rem;
}

.ms-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .5rem;
}

.ms-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .5rem;
}

.ms-3:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .75rem;
}

.ms-3:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .75rem;
}

.ms-3:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-left: .75rem;
}

.ms-3:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .75rem;
}

.ms-3:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .75rem;
}

.ms-3:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-right: .75rem;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-8 {
  margin-top: 2rem;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-row {
  display: table-row;
}

.hidden {
  display: none;
}

.h-auto {
  height: auto;
}

.w-48 {
  width: 12rem;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[140px\] {
  min-width: 140px;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[65px\] {
  min-width: 65px;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.max-w-\[160px\] {
  max-width: 160px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-full {
  max-width: 100%;
}

.flex-1 {
  flex: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.origin-top {
  transform-origin: top;
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
  cursor: pointer;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-stretch {
  justify-content: stretch;
}

.gap-1 {
  gap: .25rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.\!rounded-md {
  border-radius: .375rem !important;
}

.rounded {
  border-radius: .25rem;
}

.rounded-\[15px\] {
  border-radius: 15px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: .5rem;
}

.rounded-md {
  border-radius: .375rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgba(75, 85, 99, var(--tw-border-opacity, 1));
}

.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgba(129, 140, 248, var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: rgba(0, 0, 0, 0);
}

.border-white\/30 {
  border-color: rgba(255, 255, 255, .3);
}

.\!bg-\[\#B4B4B4\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgba(180, 180, 180, var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#00b7ff\] {
  --tw-bg-opacity: 1;
  background-color: rgba(0, 183, 255, var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(229, 231, 235, var(--tw-bg-opacity, 1));
}

.bg-gray-500\/75 {
  background-color: rgba(107, 114, 128, .75);
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(238, 242, 255, var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(220, 38, 38, var(--tw-bg-opacity, 1));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}

.bg-white\/20 {
  background-color: rgba(255, 255, 255, .2);
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: .25rem;
}

.p-2 {
  padding: .5rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-8 {
  padding: 2rem;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-40 {
  padding-top: 10rem;
  padding-bottom: 10rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.pb-1 {
  padding-bottom: .25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pe-0:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 0;
}

.pe-0:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 0;
}

.pe-0:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 0;
}

.pe-0:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 0;
}

.pe-0:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 0;
}

.pe-0:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 0;
}

.pe-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .25rem;
}

.pe-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .25rem;
}

.pe-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .25rem;
}

.pe-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .25rem;
}

.pe-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .25rem;
}

.pe-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .25rem;
}

.pe-3:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .75rem;
}

.pe-3:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .75rem;
}

.pe-3:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: .75rem;
}

.pe-3:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .75rem;
}

.pe-3:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .75rem;
}

.pe-3:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: .75rem;
}

.pe-4:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 1rem;
}

.pe-4:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 1rem;
}

.pe-4:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-right: 1rem;
}

.pe-4:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 1rem;
}

.pe-4:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 1rem;
}

.pe-4:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-left: 1rem;
}

.ps-1:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .25rem;
}

.ps-1:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .25rem;
}

.ps-1:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .25rem;
}

.ps-1:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .25rem;
}

.ps-1:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .25rem;
}

.ps-1:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .25rem;
}

.ps-2:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .5rem;
}

.ps-2:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .5rem;
}

.ps-2:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .5rem;
}

.ps-2:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .5rem;
}

.ps-2:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .5rem;
}

.ps-2:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .5rem;
}

.ps-3:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .75rem;
}

.ps-3:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .75rem;
}

.ps-3:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: .75rem;
}

.ps-3:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .75rem;
}

.ps-3:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .75rem;
}

.ps-3:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: .75rem;
}

.ps-4:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: 1rem;
}

.ps-4:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: 1rem;
}

.ps-4:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  padding-left: 1rem;
}

.ps-4:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: 1rem;
}

.ps-4:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: 1rem;
}

.ps-4:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  padding-right: 1rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-3 {
  padding-top: .75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-top {
  vertical-align: top;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-wide {
  letter-spacing: .025em;
}

.tracking-wider {
  letter-spacing: .05em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.text-\[\#00adef\] {
  --tw-text-opacity: 1;
  color: rgba(0, 173, 239, var(--tw-text-opacity, 1));
}

.text-\[\#00b7ff\] {
  --tw-text-opacity: 1;
  color: rgba(0, 183, 255, var(--tw-text-opacity, 1));
}

.text-\[\#32CD33\] {
  --tw-text-opacity: 1;
  color: rgba(50, 205, 51, var(--tw-text-opacity, 1));
}

.text-\[\#ff696a\] {
  --tw-text-opacity: 1;
  color: rgba(255, 105, 106, var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgba(0, 0, 0, var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgba(59, 130, 246, var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgba(79, 70, 229, var(--tw-text-opacity, 1));
}

.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgba(67, 56, 202, var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.text-white\/70 {
  color: rgba(255, 255, 255, .7);
}

.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-25 {
  opacity: .25;
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.outline-none {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(0, 0, 0, var(--tw-ring-opacity, 1));
}

.ring-opacity-5 {
  --tw-ring-opacity: .05;
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-75 {
  transition-duration: 75ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

:root {
  --foreground: #fff;
  --background: #011132;
  --font-gilroy: "Gilroy", sans-serif;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #011132;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-gilroy);
}

.osano-cm-widget {
  display: none;
}

.osano-cm-dialog {
  border: 1px solid #00719d;
}

.arrow-right {
  z-index: 2;
  border-radius: 10rem;
  background-color: #00adef !important;
  width: 50px !important;
  height: 50px !important;
}

.slick-prev, .slick-next {
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}

.popup {
  z-index: 1000;
  background: #fff;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  position: absolute;
  overflow: auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, .2);
}

.nextjs-toast-errors-parent {
  display: none;
}

.text-sec {
  color: #fff;
}

.font-14 {
  font-size: 14px;
}

.font-18 {
  font-size: 18px;
}

.popup-container {
  justify-content: center;
  align-items: center;
  padding: 10px;
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.min-h-500 {
  min-width: 350px !important;
  height: 600px !important;
  min-height: 600px !important;
}

.caret {
  animation: 1s step-end infinite blink;
}

.scroll-lock {
  width: 100%;
  position: fixed;
  overflow: hidden;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.new-link {
  transition: all .3s ease-in-out;
  color: #00adef !important;
}

.text-md-nowrap {
  white-space: nowrap;
}

@media (max-width: 700px) {
  .text-md-nowrap {
    white-space: unset;
  }
}

.scroll-table {
  max-height: 270px;
  overflow-y: scroll;
}

.bg-trans > * {
  background-color: rgba(0, 0, 0, 0);
}

.jodit-container {
  background: #fff !important;
}

.jodit-container .jodit-wysiwyg {
  min-height: 300px;
  padding: 10px;
  font-size: 16px;
  color: #000 !important;
  background: #8b7c7c !important;
}

.jodit-container .jodit-toolbar {
  border-bottom: 1px solid #ddd;
  background: #f8f9fa !important;
}

.cart_button {
  width: 70%;
  border-radius: 10px !important;
}

.cart_select {
  color: #000;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 10px;
  width: 25%;
  padding: 0 20px;
}

.bb-blue {
  border-bottom: 3px solid rgba(0, 173, 239, .3);
}

.ws-normal {
  white-space: normal !important;
}

.txt-blue {
  color: #00adef;
}

.search-highlight {
  color: #04498c;
  font-weight: 600 !important;
}

.scroll-hidden {
  max-height: 100vh;
  overflow-y: auto;
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
}

a, a:hover {
  color: #00adef;
  text-decoration: none;
  transition: all .3s ease-in-out;
}

h1, .h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 1199px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  h1, .h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {
  h1, .h1 {
    font-size: 1.3rem;
  }
}

h2, .h2 {
  font-size: 5rem;
  font-weight: 800;
}

@media (max-width: 1269px) {
  h2, .h2 {
    font-size: 3rem;
  }
}

@media (max-width: 767px) {
  h2, .h2 {
    font-size: 1.363rem;
  }
}

h3, .h3 {
  font-size: 2.8rem;
  font-weight: 800;
}

@media (max-width: 1199px) {
  h3, .h3 {
    font-size: 1.688rem;
  }
}

@media (max-width: 767px) {
  h3, .h3 {
    font-size: 1.25rem;
  }
}

h4, .h4 {
  font-size: 1.65rem;
  font-weight: 600;
  line-height: 35px;
}

@media (max-width: 767px) {
  h4, .h4 {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

h5, .h5 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 30px;
}

@media (max-width: 767px) {
  h5, .h5 {
    font-size: 1rem;
    line-height: 25px;
  }
}

h6, .h6 {
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 767px) {
  h6, .h6 {
    font-size: 1rem;
  }
}

p {
  font-size: 1rem;
  font-weight: 400;
}

@media (max-width: 767px) {
  p {
    font-size: .875rem;
  }
}

.content-center {
  justify-content: center;
  align-items: center;
  display: flex;
}

.custom_checkbox {
  align-items: center;
  gap: 10px;
  margin-bottom: 1.25rem;
  display: flex;
}

.custom_checkbox_input {
  background-color: rgba(0, 0, 0, 0) !important;
  border: 1px solid #00adef !important;
  width: 20px !important;
  height: 20px !important;
  margin-top: 0 !important;
}

.custom_checkbox_input:focus {
  box-shadow: none !important;
}

.custom_checkbox_input:checked {
  background-color: #00adef !important;
  border: 1px solid #00adef !important;
}

.custom_checkbox_label {
  color: #fff;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
}

.switch {
  width: 50px;
  height: 28px;
  display: inline-block;
  position: relative;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  color: #fff;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 550px) {
  .switch-label {
    font-size: 16px;
  }
}

@media (max-width: 350px) {
  .switch-label {
    font-size: 14px;
  }
}

.slider {
  cursor: pointer;
  background-color: #fff;
  border-radius: 34px;
  transition: all .4s;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.slider:before {
  content: "";
  background-color: #9c9a9f;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  transition: all .4s;
  position: absolute;
  bottom: 3px;
  left: 3px;
}

input:checked + .slider {
  background-color: #0099d1;
}

input:checked + .slider:before {
  background-color: #fff;
  transform: translateX(22px);
}

.error-message {
  font-size: 16px;
  font-weight: 700;
  display: block;
  color: #ff696a !important;
}

.success-message {
  font-size: 16px;
  font-weight: 700;
  display: block;
  color: #32cd33 !important;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 68, 68, var(--tw-bg-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.focus\:border-gray-300:focus {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

.focus\:border-indigo-700:focus {
  --tw-border-opacity: 1;
  border-color: rgba(67, 56, 202, var(--tw-border-opacity, 1));
}

.focus\:bg-gray-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.focus\:bg-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.focus\:bg-gray-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.focus\:bg-indigo-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 231, 255, var(--tw-bg-opacity, 1));
}

.focus\:text-gray-800:focus {
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity, 1));
}

.focus\:text-indigo-800:focus {
  --tw-text-opacity: 1;
  color: rgba(55, 48, 163, var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(99, 102, 241, var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.active\:bg-gray-900:active {
  --tw-bg-opacity: 1;
  background-color: rgba(17, 24, 39, var(--tw-bg-opacity, 1));
}

.active\:bg-red-700:active {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.disabled\:opacity-25:disabled {
  opacity: .25;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

@media (min-width: 640px) {
  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mt-0 {
    margin-top: 0;
  }

  .sm\:w-\[200px\] {
    width: 200px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-\[180px\] {
    max-width: 180px;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .sm\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .sm\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .sm\:py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem;
  }

  .sm\:text-\[16px\] {
    font-size: 16px;
  }

  .sm\:text-\[20px\] {
    font-size: 20px;
  }

  .sm\:text-\[32px\] {
    font-size: 32px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {
  .md\:table-cell {
    display: table-cell;
  }

  .md\:table-row {
    display: table-row;
  }

  .md\:hidden {
    display: none;
  }

  .md\:min-w-\[100px\] {
    min-width: 100px;
  }

  .md\:min-w-\[45px\] {
    min-width: 45px;
  }

  .md\:min-w-\[50px\] {
    min-width: 50px;
  }

  .md\:min-w-\[70px\] {
    min-width: 70px;
  }

  .md\:min-w-\[80px\] {
    min-width: 80px;
  }

  .md\:max-w-\[100px\] {
    max-width: 100px;
  }

  .md\:max-w-\[120px\] {
    max-width: 120px;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:px-1 {
    padding-left: .25rem;
    padding-right: .25rem;
  }

  .md\:py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .md\:py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem;
  }

  .md\:text-start {
    text-align: start;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:min-w-\[120px\] {
    min-width: 120px;
  }

  .lg\:min-w-\[140px\] {
    min-width: 140px;
  }

  .lg\:min-w-\[160px\] {
    min-width: 160px;
  }

  .lg\:min-w-\[65px\] {
    min-width: 65px;
  }

  .lg\:min-w-\[800px\] {
    min-width: 800px;
  }

  .lg\:min-w-\[80px\] {
    min-width: 80px;
  }

  .lg\:max-w-\[200px\] {
    max-width: 200px;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.ltr\:origin-top-left:where([dir="ltr"], [dir="ltr"] *) {
  transform-origin: 0 0;
}

.ltr\:origin-top-right:where([dir="ltr"], [dir="ltr"] *) {
  transform-origin: 100% 0;
}

.rtl\:origin-top-left:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: 0 0;
}

.rtl\:origin-top-right:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: 100% 0;
}

/*# sourceMappingURL=src_app_globals_scss_css_e59ae46c._.single.css.map*/