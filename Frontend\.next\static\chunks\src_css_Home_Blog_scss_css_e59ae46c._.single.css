/* [project]/src/css/Home/Blog.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

.blog {
  padding: 5rem 0;
  position: relative;
}

.blog_cards {
  margin-bottom: 50px !important;
}

@media (max-width: 767px) {
  .blog_cards {
    margin-bottom: 20px !important;
  }
}

@media (max-width: 575px) {
  .blog_cards {
    margin-bottom: 10px !important;
  }
}

.blog_cards .slider-container {
  position: relative;
}

.blog_cards .slider-container .slick-slider {
  margin: 0 -15px;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track {
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide {
  height: auto;
  padding: 0 15px;
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide > div {
  width: 100%;
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active {
  padding-top: 0;
}

.blog_postcard {
  cursor: pointer;
  background-color: #032251;
  border: 1px solid rgba(255, 255, 255, .5);
  border-radius: 1.25rem;
  height: 100%;
  position: relative;
}

@media (max-width: 767px) {
  .blog_postcard {
    margin-bottom: 1.25rem;
  }
}

.blog_postcard:hover .blog_postcard_img img {
  transform: scale(1.1);
}

.blog_postcard_img {
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  position: relative;
  overflow: hidden;
}

.blog_postcard_img:before {
  content: "";
  background-color: rgba(255, 255, 255, .6);
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  width: 100%;
  padding-top: 54.25%;
  display: block;
}

.blog_postcard_img_overlay {
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.blog_postcard_img img {
  object-fit: cover;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  width: 100%;
  height: 100%;
  transition: all .3s ease-in-out;
}

.blog_postcard_content {
  padding: .65rem 1.25rem 2rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content {
    padding: .65rem 1rem;
  }
}

.blog_postcard_content h3 {
  letter-spacing: -.1px;
  color: #c5c5d5;
  margin-bottom: .625rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content h3 {
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.blog_postcard_content p {
  letter-spacing: -.1px;
  color: #fff;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 2rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content p {
    font-size: 15px;
    line-height: 23px;
  }
}

@media (max-width: 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-prev {
    top: 100px !important;
    left: -45px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-prev.slick-disabled {
  opacity: .7;
  background-color: #414c60;
}

@media (max-width: 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-next {
    top: 100px !important;
    right: -30px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-next.slick-disabled {
  opacity: .7;
  background-color: #414c60;
}

.recent_post {
  cursor: pointer;
  background-color: #032251;
  border: 1px solid rgba(8, 5, 5, .5);
  border-radius: 1.25rem;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  position: relative;
}

.recent_post h1 {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 767px) {
  .recent_post h1 {
    margin-bottom: 30px;
  }

  .recent_post {
    margin-bottom: 1.25rem;
  }
}

.recent_post_img {
  border-top-left-radius: 1.25rem;
  border-bottom-left-radius: 1.25rem;
  width: 366px;
  position: relative;
  overflow: hidden;
}

@media (max-width: 991px) {
  .recent_post_img {
    width: 40%;
  }
}

@media (max-width: 599px) {
  .recent_post_img {
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    border-bottom-left-radius: 0;
    width: 100%;
  }

  .recent_post_img:before {
    content: "";
    background-color: rgba(255, 255, 255, 0);
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    width: 100%;
    padding-top: 54.25%;
    display: block;
  }
}

.recent_post_img img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

@media (max-width: 767px) {
  .recent_post_img img {
    height: 100%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
  }
}

.recent_post_content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: calc(100% - 366px);
  padding: 2rem 1.25rem;
}

@media (max-width: 991px) {
  .recent_post_content {
    width: 60%;
    padding: 1rem 1.25rem;
  }
}

@media (max-width: 599px) {
  .recent_post_content {
    width: 100%;
    padding: 2rem 1rem;
  }
}

.recent_post_content small {
  display: block;
}

.recent_post_content h4 {
  margin-top: .625rem;
  margin-bottom: .625rem;
}

.recent_post_content p, .recent_post_content small {
  color: #c5c5d5;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 27px;
}

@media (max-width: 767px) {
  .recent_post_content p, .recent_post_content small {
    font-size: 1rem;
  }
}

.recent_post_time {
  color: #00adef;
  margin-top: 10px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 26px;
  display: block;
}

@media (max-width: 767px) {
  .recent_post_time {
    font-size: .9rem;
  }
}

.recontPostTitle {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 767px) {
  .recontPostTitle {
    margin-bottom: 30px;
    font-size: 1.5rem;
  }
}

.slick-slider .slick-prev, .slick-slider .slick-next {
  cursor: pointer;
  color: rgba(0, 0, 0, 0);
  background: none;
  border: none;
  outline: none;
  width: 20px;
  height: 20px;
  padding: 0;
  display: block;
  top: 50%;
  -webkit-transform: translate(0, -50%) !important;
  -ms-transform: translate(0, -50%) !important;
  position: absolute !important;
  transform: translateY(-50%) !important;
}

/*# sourceMappingURL=src_css_Home_Blog_scss_css_e59ae46c._.single.css.map*/