{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1f6f972e._.js", "server/edge/chunks/[root-of-the-server]__61fc81c1._.js", "server/edge/chunks/edge-wrapper_4145c107.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/user/:path*{(\\\\.json)}?", "originalSource": "/user/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/pricing{(\\\\.json)}?", "originalSource": "/pricing"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/change-password{(\\\\.json)}?", "originalSource": "/change-password"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/account/:path*{(\\\\.json)}?", "originalSource": "/account/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/super-admin/:path*{(\\\\.json)}?", "originalSource": "/super-admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/not-found{(\\\\.json)}?", "originalSource": "/not-found"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/security-check{(\\\\.json)}?", "originalSource": "/security-check"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JAdWpMZW7YX2HWGBSRb2tC6MF8pA5fg8t+vFkOc3uBA=", "__NEXT_PREVIEW_MODE_ID": "29999353d843f3c6a677e27721f9af05", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "399cf31716e2017eddbbc4d4a720fe0a51886e3b1b7b8fc1d6a62648c700cf42", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "41ce7e70129f27e78858524f8a37b5318d4b6b2d5cf80b7226963d69a2ea1c15"}}}, "instrumentation": null, "functions": {}}